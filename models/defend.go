package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models/defend"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/semver/v3"
)

type Defend struct {
	BundleId   string         `json:"bundle_id"`
	AppVersion string         `json:"app_version"`
	Status     bool           `json:"status"`
	Data       map[string]any `json:"data"`
}

const (
	DefendAllVersion    string = "all"
	DefendDefaultVersin string = "default"
)

type DefendBundleConfig struct {
	All     map[string]any `json:"all"`
	Default map[string]any `json:"default"`
	List    []Defend       `json:"list"`
}

type DefendRequest struct {
	BundleId   string `json:"bundle_id"`
	AppVersion string `json:"app_version"`
}

type DefendResponse struct {
	Code    int            `json:"code"`
	Message string         `json:"msg"`
	Data    map[string]any `json:"data"`
}

const DefendConfigsCacheKey string = "hs:testergame:defend:allconfigs"

const DefendCacheExpiration = time.Hour * 24 * 7

const DefendUpdateInterval = time.Minute * 10

type DefendConfigs struct {
	mux            sync.Mutex
	config         defend.Config
	dbDefendConfig *defend.DBDefendConfig
	configs        map[string]DefendBundleConfig
	sugared        *zap.SugaredLogger
}

func NewDefendConfigs(defendConfig defend.Config) *DefendConfigs {
	dc := &DefendConfigs{
		config:         defendConfig,
		dbDefendConfig: &defend.DBDefendConfig{},
		configs:        make(map[string]DefendBundleConfig),
	}
	dc.loadConfigs()
	go dc.update(defendConfig.UpdateInterval)
	return dc
}

func (dc *DefendConfigs) SetEventLogger(logger *zap.SugaredLogger) {
	dc.sugared = logger
}

func (dc *DefendConfigs) Save(data []byte) error {
	if !json.Valid(data) {
		return errors.New("invalid json data")
	}
	configs := make([]Defend, 0)
	err := json.Unmarshal(data, &configs)
	if err != nil {
		return err
	}
	hasDefault := false
	for _, config := range configs {
		if config.AppVersion == DefendDefaultVersin {
			hasDefault = true
		}
		if !slices.Contains([]string{DefendAllVersion, DefendDefaultVersin}, config.AppVersion) {
			_, err := semver.NewVersion(config.AppVersion)
			if err != nil {
				return fmt.Errorf("version(%s), Parse Error: %v", config.AppVersion, err)
			}
		}
	}
	if !hasDefault {
		return errors.New("config no default version")
	}
	return redisClient.Set(context.Background(), DefendConfigsCacheKey, data, DefendCacheExpiration).Err()
}

func (dc *DefendConfigs) Get() map[string]DefendBundleConfig {
	return dc.configs
}

func (dc *DefendConfigs) loadConfigs() {
	dc.mux.Lock()
	defer dc.mux.Unlock()
	defer func() {
		if r := recover(); r != nil {
			sugared.Errorf("panic err: %v", r)
		}
	}()
	sugared.Infof("defend config update")
	validConfigs := make(map[string]DefendBundleConfig)
	configs, err := dc.dbDefendConfig.FindConfigs()
	if err != nil {
		sugared.Errorf("find config err: %v", err)
		return
	}
	for _, config := range configs {
		if _, ok := validConfigs[config.BundleId]; !ok {
			validConfigs[config.BundleId] = DefendBundleConfig{
				List: make([]Defend, 0),
			}
		}
		configJson := make(map[string]any)
		jsonDecode := json.NewDecoder(strings.NewReader(config.Config))
		jsonDecode.UseNumber()
		err = jsonDecode.Decode(&configJson)
		if err != nil {
			sugared.Errorf("unmarshal config err: %v", err)
		}
		validConfig := DefendBundleConfig{
			List: make([]Defend, 0),
		}
		if vc, ok := validConfigs[config.BundleId]; ok {
			validConfig = vc
		}
		if config.State == 1 {
			if config.Version == DefendAllVersion {

				validConfig.All = configJson
				validConfigs[config.BundleId] = validConfig
				continue
			}
			if config.Version == DefendDefaultVersin {
				validConfig.Default = configJson
				validConfigs[config.BundleId] = validConfig
				continue
			}
			defendConfig := Defend{}
			defendConfig.BundleId = config.BundleId
			defendConfig.AppVersion = config.Version
			defendConfig.Data = configJson
			defendConfig.Status = true
			validConfig.List = append(validConfig.List, defendConfig)
			validConfigs[config.BundleId] = validConfig
		}
	}
	// 缓存获取重写，测试使用
	if dc.config.UseCacheRewrite {
		if vConfigs := dc.loadConfigsCache(); vConfigs != nil {
			validConfigs = vConfigs
		}
	}
	for bundleId, config := range validConfigs {
		if config.Default == nil {
			sugared.Errorf("bundle(%s) has no default config", bundleId)
			delete(validConfigs, bundleId)
		}
		checkVersion := true
		for _, bundleConfig := range config.List {
			_, err := semver.NewVersion(bundleConfig.AppVersion)
			if err != nil {
				sugared.Errorf("Version(%s) Parse Error: %v", bundleConfig.AppVersion, err)
				checkVersion = false
				break
			}
		}
		if !checkVersion {
			continue
		}
		slices.SortFunc(config.List, func(a, b Defend) int {
			aVersion, _ := semver.NewVersion(a.AppVersion)
			bVersion, _ := semver.NewVersion(b.AppVersion)
			return bVersion.Compare(aVersion)
		})
	}

	dc.configs = validConfigs
	sugared.Infof("defend config update success")
}

func (dc *DefendConfigs) loadConfigsCache() map[string]DefendBundleConfig {
	configs := make(map[string]DefendBundleConfig)
	defendConfigs := make([]Defend, 0)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	defendJson, err := redisClient.Get(ctx, DefendConfigsCacheKey).Result()
	if err != nil {
		sugared.Errorf("load config err: %v", err)
		return nil
	}
	err = json.Unmarshal([]byte(defendJson), &defendConfigs)
	if err != nil {
		sugared.Errorf("unmarshal config err: %v", err)
		return nil
	}
	for _, config := range defendConfigs {
		defendBundleConfig := DefendBundleConfig{
			List: make([]Defend, 0),
		}
		if dbc, ok := configs[config.BundleId]; ok {
			defendBundleConfig = dbc
		}
		if config.AppVersion == DefendAllVersion {
			defendBundleConfig.All = config.Data
			configs[config.BundleId] = defendBundleConfig
			continue
		}
		if config.AppVersion == DefendDefaultVersin {
			defendBundleConfig.Default = config.Data
			configs[config.BundleId] = defendBundleConfig
			continue
		}
		defendBundleConfig.List = append(defendBundleConfig.List, config)
		configs[config.BundleId] = defendBundleConfig
	}
	return configs
}

func (dc *DefendConfigs) update(updateInterval int64) {
	ticker := time.NewTicker(time.Minute * time.Duration(updateInterval))
	defer ticker.Stop()
	for range ticker.C {
		dc.loadConfigs()
	}
}

func (dc *DefendConfigs) InitConfig(body []byte) DefendResponse {
	response := DefendResponse{Code: 1, Message: "success"}
	request := DefendRequest{}
	err := json.Unmarshal(body, &request)
	if err != nil {
		sugared.Errorf("json.Unmarshal(%q): %v", body, err)
		response.Code = 0
		response.Message = "invalid request json"
		return response
	}
	if bundleDefendConfig, ok := dc.configs[request.BundleId]; ok {
		if bundleDefendConfig.All != nil {
			response.Data = bundleDefendConfig.All
			return response
		}
		appVersion, err := semver.NewVersion(request.AppVersion)
		if err != nil {
			sugared.Errorf("semver.NewVersion(%q): %v", request.AppVersion, err)
			response.Code = 0
			response.Message = "invalid app version"
			return response
		}
		for _, config := range bundleDefendConfig.List {
			configVersion, err := semver.NewVersion(config.AppVersion)
			if err != nil {
				sugared.Errorf("semver.NewVersion(%q): %v", config.AppVersion, err)
				continue
			}
			if appVersion.Equal(configVersion) || appVersion.GreaterThan(configVersion) {
				response.Data = config.Data
				return response
			}
		}
		response.Data = bundleDefendConfig.Default
		return response
	} else {
		response.Code = 0
		response.Message = fmt.Sprintf("bundle %s not found", request.BundleId)
		sugared.Errorf(response.Message)
		return response
	}
}

type EarlyWarning struct {
	BundleId string         `json:"bundle_id"`
	Type     string         `json:"type"`
	Data     map[string]any `json:"data"`
}

func (dc *DefendConfigs) EarlyWarning(body []byte) {
	ew := EarlyWarning{}
	err := json.Unmarshal(body, &ew)
	if err != nil {
		sugared.Errorf("json.Unmarshal(%q): %v", body, err)
		return
	}
	labels := prometheus.Labels{"bundle_id": ew.BundleId, "type": ew.Type}
	metrics.DefendEarlyWarning.With(labels).Inc()
	dc.sugared.Infow("early-warning", "bundle_id", ew.BundleId, "type", ew.Type, "data", ew.Data)
}
