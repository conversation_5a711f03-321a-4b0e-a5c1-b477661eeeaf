package models

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"hungrystudio.com/core/encrypt"
	"hungrystudio.com/datatester/models/adconfigs"
	"net/http"
	"os"
	"slices"
	"time"
)

type AreaMapping struct {
	Name      string   `yaml:"name" json:"name"`
	ParamKey  string   `yaml:"paramKey" json:"paramKey"`
	Countries []string `yaml:"countries" json:"countries"`
}

type BundleConfig struct {
	BundleId              string                        `yaml:"bundleId" json:"bundleId"`
	Platform              string                        `yaml:"platform" json:"platform"`
	TableId               string                        `yaml:"tableId" json:"tableId"`
	MinApiVersion         string                        `yaml:"minApiVersion" json:"minApiVersion"`
	UpdateInterval        time.Duration                 `yaml:"updateInterval" json:"updateInterval"`
	TesterAdWayNums       map[string][]string           `yaml:"testerAdWayNums" json:"testerAdWayNums"`
	AdsConfigFilters      []string                      `yaml:"adsConfigFilters" json:"adsConfigFilters"`
	CountryList           []string                      `yaml:"countryList" json:"countryList"`
	AreaMappings          map[string]AreaMapping        `yaml:"areaMappings" json:"areaMappings"`
	AbTesterConfig        AppDataTesterConfig           `yaml:"abTesterConfig" json:"abTesterConfig"`
	Switch                SwitchConfig                  `yaml:"switch" json:"switch"`
	AdInfoDefault         map[string]any                `yaml:"adInfoDefault" json:"adInfoDefault"`
	AdConfigs             map[string]adconfigs.AdConfig `yaml:"adConfigs" json:"adConfigs"`
	NewFilingSequence     []string                      `yaml:"newFilingSequence" json:"newFilingSequence"`
	DynamicFilingSequence []string                      `yaml:"dynamicFilingSequence" json:"dynamicFilingSequence"`
	Csr                   CsrConfig                     `yaml:"csr" json:"csr"`

	hash string // 更新文件hash值，md5
}

func (bc *BundleConfig) checkTesterAdWayNum(apiVersion string, adWayNum string) bool {
	for _, aVersion := range []string{"default", apiVersion} {
		if aValues, ok := bc.TesterAdWayNums[aVersion]; ok {
			if slices.Contains(aValues, adWayNum) {
				return true
			}
		}
	}
	return false
}

// Update 更新BundleConfig
func (bc *BundleConfig) Update(bConfig BundleConfig) {
	if bc.BundleId != bConfig.BundleId {
		return
	}
	//bc.Platform = bConfig.Platform
	//bc.TableId = bConfig.TableId
	if bConfig.MinApiVersion != "" {
		bc.MinApiVersion = bConfig.MinApiVersion
	}
	if bConfig.UpdateInterval != 0 {
		bc.UpdateInterval = bConfig.UpdateInterval
	}
	for k, vv := range bConfig.TesterAdWayNums {
		if len(vv) > 0 {
			bc.TesterAdWayNums[k] = vv
		}
	}
	if len(bConfig.AdsConfigFilters) > 0 {
		bc.AdsConfigFilters = bConfig.AdsConfigFilters
	}
	if len(bConfig.CountryList) > 0 {
		bc.CountryList = bConfig.CountryList
	}
	if len(bConfig.AdInfoDefault) > 0 {
		for k, v := range bConfig.AdInfoDefault {
			bc.AdInfoDefault[k] = v
		}
	}
	if len(bConfig.Switch) > 0 {
		for k, v := range bConfig.Switch {
			bc.Switch[k] = v
		}
	}
	if len(bConfig.AreaMappings) > 0 {
		for k, v := range bConfig.AreaMappings {
			if len(v.Countries) > 0 && v.ParamKey != "" {
				bc.AreaMappings[k] = v
			}
		}
	}
	if bConfig.AbTesterConfig.AppKey != "" && len(bConfig.AbTesterConfig.MappingCountryFields) > 0 && len(bConfig.AbTesterConfig.MediaSources) > 0 {
		bc.AbTesterConfig = bConfig.AbTesterConfig
	}
	for len(bConfig.AdConfigs) > 0 {
		for k, v := range bConfig.AdConfigs {
			bc.AdConfigs[k] = v
		}
	}
	if len(bConfig.NewFilingSequence) > 0 {
		bc.NewFilingSequence = bConfig.NewFilingSequence
	}
	if len(bConfig.DynamicFilingSequence) > 0 {
		bc.DynamicFilingSequence = bConfig.DynamicFilingSequence
	}
}

func (bc *BundleConfig) updateFromFile() {
	updateFile := "data/bundles/" + bc.BundleId + ".json"
	if _, err := os.Stat(updateFile); err != nil {
		//sugared.Errorf("AutoUpdate: file %s doesn't exist", updateFile)
		return
	}
	hash, err := encrypt.Md5File(updateFile)
	if err != nil {
		sugared.Errorf("AutoUpdate: encrypt file %s failed,error: %v", updateFile, err)
		return
	}
	if bc.hash == hash {
		return
	}
	file, err := os.Open(updateFile)
	if err != nil {
		sugared.Errorf("AutoUpdate: open file %s failed, error: %v", updateFile, err)
		return
	}
	defer file.Close()
	bcTemp := BundleConfig{}
	err = json.NewDecoder(file).Decode(&bcTemp)
	if err != nil {
		sugared.Errorf("AutoUpdate: decode file %s failed, error: %v", updateFile, err)
		return
	}
	bc.Update(bcTemp)
	bc.hash = hash
	sugared.Errorf("AutoUpdate: update file %s successfully", updateFile)
}

func (bc *BundleConfig) autoUpdate(ctx context.Context) {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			sugared.Infof("Stop AutoUpdate BundleConfig: %s", bc.BundleId)
			return
		case <-ticker.C:
			bc.updateFromFile()
		}
	}
}

// countryArea 国家地区映射
func countryArea(countryCode string, areaMappings map[string]AreaMapping) (string, string, bool) {
	for _, mapping := range areaMappings {
		if slices.Contains(mapping.Countries, countryCode) {
			return mapping.Name, mapping.ParamKey, true
		}
	}
	return "", "", false
}

func ParseBundleConfigs(addr string) []BundleConfig {
	bConfigs := make([]BundleConfig, 0)
	if addr[:4] == "http" {
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		}
		response, err := client.Get(addr)
		if err != nil {
			sugared.Errorf("http.Get(%s): %v", addr, err)
			return nil
		}
		defer response.Body.Close()
		jsonDecoder := json.NewDecoder(response.Body)
		err = jsonDecoder.Decode(&bConfigs)
		if err != nil {
			sugared.Errorf("json.Decoder.Decode(): %v", err)
			return nil
		}
		return bConfigs
	} else {
		file, err := os.Open(addr)
		if err != nil {
			sugared.Errorf("os.Open(%s): %v", addr, err)
			return nil
		}
		defer file.Close()
		jsonDecoder := json.NewDecoder(file)
		err = jsonDecoder.Decode(&bConfigs)
		if err != nil {
			sugared.Errorf("json.Decoder.Decode(): %v", err)
			return nil
		}
		return bConfigs
	}
}

// 所有包配置
var bundleConfigs map[string]*BundleConfig

// InitBundleConfigs 初始化bundleConfigs
func InitBundleConfigs(ctx context.Context) {
	bundleConfigs = make(map[string]*BundleConfig)
	bundleSlices := ParseBundleConfigs("configs/bundle.json")
	for _, bundle := range bundleSlices {
		bundleConfigs[bundle.BundleId] = &bundle
		go bundleConfigs[bundle.BundleId].autoUpdate(ctx)
	}
}
