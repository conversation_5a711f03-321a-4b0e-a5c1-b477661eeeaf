package models

import (
	"context"
	"fmt"
	"io"
	"math/rand"
	"slices"
	"sync"

	"hungrystudio.com/datatester/models/adconfigs"
	"hungrystudio.com/datatester/models/distributes"
)

type Traffics struct {
	mux         sync.RWMutex
	ts          map[string]TrafficInterface
	ctx         context.Context
	abLogWriter io.Writer
}

func NewTraffics(ctx context.Context, abLogWriter io.Writer) *Traffics {
	ts := &Traffics{
		ctx:         ctx,
		ts:          make(map[string]TrafficInterface),
		abLogWriter: abLogWriter,
	}
	for _, bundleConfig := range bundleConfigs {
		ts.addTraffic(bundleConfig)
	}

	return ts
}

func (ts *Traffics) addTraffic(bundleConfig *BundleConfig) {
	ts.mux.Lock()
	defer ts.mux.Unlock()
	var t TrafficInterface
	sugared.Infof("Add Traffic %s", bundleConfig.BundleId)
	switch bundleConfig.BundleId {
	case BundleIdSudokuAndroid, BundleIdSudokuIos, BundleIdMatch3dAndroid:
		t = NewSudokuTraffic(ts.ctx, bundleConfig, ts.abLogWriter)
		//csr分流方案
		tDynamic := NewSudoDynamicTraffic(ts.ctx, bundleConfig, ts.abLogWriter)
		groupDynamic := fmt.Sprintf("%s.%s", "dynamic", bundleConfig.BundleId)
		ts.ts[groupDynamic] = tDynamic
	default:
		t = NewTraffic(ts.ctx, bundleConfig, ts.abLogWriter)
	}

	ts.ts[bundleConfig.BundleId] = t
	sugared.Infof("Add Traffic %s Success", bundleConfig.BundleId)
}

func (ts *Traffics) GetTraffic(bundleId string) TrafficInterface {
	ts.mux.RLock()
	defer ts.mux.RUnlock()
	if t, ok := ts.ts[bundleId]; ok {
		return t
	}
	return nil
}

type TrafficInterface interface {
	GetAdConfigs(params map[string]any) (adconfigs.AdConfigs, string, bool)
	GetTrafficConfig(params map[string]any) map[string]any
}

type Traffic struct {
	mux                     sync.RWMutex
	bundleConfig            *BundleConfig
	bundleId                string
	llConfigs               *LLConfigs
	didiConfigs             *DidiConfigs
	djhConfigs              *DjhConfigs
	adwayNumConfigs         *AdwayNumConfigs
	douDiConfigs            *DouDiConfigs
	guoShenConfigs          *GuoShenConfigs
	noNormalConfigs         *NoNormalConfigs
	qaConfigs               *QAConfigs
	csrQaConfigs            *CsrQAConfigs
	specialConfigs          *SpecialConfigs
	testerConfigs           *TesterConfigs
	switchConfigs           *SwitchConfigs
	tianChongConfigs        *TianChongConfigs
	chunJingConfigs         *ChunJingConfigs
	versionGatherConfigs    *VersionGatherConfigs
	abTesterConfigs         *AbTesterConfigs
	productPiciSchemeConfig *ProductPiciSchemeConfig

	appDataTester *AppDataTester
}

func NewTraffic(ctx context.Context, bundleConfig *BundleConfig, abWriter io.Writer) *Traffic {
	baseLL := NewBaseLL(bundleConfig)
	t := &Traffic{
		bundleConfig:            bundleConfig,
		llConfigs:               NewLLConfigs(ctx, baseLL),
		douDiConfigs:            NewDouDiConfigs(ctx, baseLL),
		chunJingConfigs:         NewChunJingConfigs(ctx, baseLL),
		versionGatherConfigs:    NewVersionGatherConfigs(ctx, baseLL),
		productPiciSchemeConfig: NewProductPiciSchemeConfigs(ctx, baseLL),
		djhConfigs:              NewDjhConfigs(ctx, baseLL),
		adwayNumConfigs:         NewAdwayNumConfigs(ctx, baseLL),
		guoShenConfigs:          NewGuoShenConfigs(ctx, baseLL),
		noNormalConfigs:         NewNoNormalConfigs(ctx, baseLL),
		qaConfigs:               NewQAConfigs(ctx, baseLL),
		csrQaConfigs:            NewCsrQAConfigs(ctx, baseLL),
		specialConfigs:          NewSpecialConfigs(ctx, baseLL),
		testerConfigs:           NewTesterConfigs(ctx, baseLL),
		switchConfigs:           NewSwitchConfigs(ctx, baseLL),
		tianChongConfigs:        NewTianChongConfigs(ctx, baseLL),
		abTesterConfigs:         NewAbTesterConfigs(ctx, baseLL),
		didiConfigs:             NewDidiConfigs(ctx, baseLL),
	}
	//t.appDataTester = NewAppDataTester(ctx, bundleConfig.AbTesterConfig, abWriter)
	return t
}

func (t *Traffic) GetTrafficConfig(params map[string]any) map[string]any {
	sugared.Infof("Start Execute GetTrafficConfig")
	var apiVersion = params["api_version"].(string)
	var appVersion = params["appVersion"].(string)
	var serverCountryCode = params[ParamNameServerCountryCode].(string)
	var serverIp = params[ParamNameServerIP].(string)
	//研发批次类型
	if _, ok := params["shunt_type"].(string); !ok {
		params["shunt_type"] = "new"
	}

	initConfig := make(map[string]any)

	// 获取AdConfigs、分配版本号、是否参与分流
	adConfigs, allotVersion, isDistributed := t.GetAdConfigs(params)
	initConfig["allot_version"] = allotVersion

	// 获取所有开关变量值,依据apiVersion
	if apiVersionSwitchConfig := t.switchConfigs.getSwitch(apiVersion); apiVersionSwitchConfig != nil {
		for k, v := range apiVersionSwitchConfig {
			initConfig[k] = v
		}
	}
	// 设置开关变量
	if switchConfig := t.setSwitch(apiVersion, appVersion, serverCountryCode, serverIp, adConfigs.Switch); switchConfig != nil {
		for k, v := range switchConfig {
			initConfig[k] = v
		}
	}
	// 选择符合条件的AdConfig
	var adInfo map[string]any
	adInfo = adConfigs.GetAdInfo(params, t.bundleConfig.AdsConfigFilters)
	if adInfo == nil {
		adInfo = make(map[string]any)
	}
	// 件是否要参加火山AB实验，参加则取回火山AB实验配置的AdConfig
	if isDistributed && adConfigs.CheckAbTester() {
		sugared.Infof("参与火山分流")
		if validExperimentVars := t.abTesterConfigs.getExperimentVars(apiVersion); validExperimentVars != nil {
			//sugared.Infof("ApiVersion: %s, ValidExperimentVars: %v", apiVersion, validExperimentVars)
			if abAdInfo, vid, ok := t.appDataTester.Execute(t.abTesterConfigs.getExperimentVars(apiVersion), params); ok {
				//sugared.Infof("HuoShanAbTester AbConfig: %v", abConfig)
				for k, v := range abAdInfo {
					adInfo[k] = v
				}
				initConfig["vid"] = vid
			}
		}
	}

	var thinkUid = params["thinkuid"].(string)
	// 用户是否当天首次请求
	if isFirst, ok := adInfo["is_first_status"].(string); ok && isFirst == "open" {
		if CheckDayFirst(t.bundleId, thinkUid) {
			adInfo["isfirst"] = "1"
		} else {
			adInfo["isfirst"] = "0"
		}
		delete(adInfo, "is_first_status")
	}
	//sugared.Infof("adConfig.resetIsFirst AdConfig: %v", adConfig)
	initConfig["ad_info"] = adInfo
	return initConfig
}

// getSwitch 获取开关设置
func (t *Traffic) setSwitch(apiVersion, appVersion, countryCode, ip string, adConfigsSwitch map[string]any) SwitchConfig {
	var s = make(SwitchConfig)
	// 设置apiVersion对应开关变量配置
	if appSwitchConfig := t.switchConfigs.getSwitch(apiVersion); appSwitchConfig != nil {
		for k, v := range appSwitchConfig {
			s[k] = v
		}
	}
	// 设置 apiVersion，adwaynum对应的配置的开关配置
	for k, v := range adConfigsSwitch {
		s[k] = v
	}
	// 如果此时开关变量没有设置，则取配置中默认的开关变量值
	if len(s) == 0 {
		s = t.bundleConfig.Switch
	}
	// 设置ip地址和国家代码
	s["ip"] = ip
	s["ip_country"] = ip
	s["hs_country_code"] = countryCode
	// 设置地区目前为is_eea 欧洲地区
	if _, paramName, ok := countryArea(countryCode, t.bundleConfig.AreaMappings); ok {
		s[paramName] = "1"
	}
	// 设置is_data_tester值
	if t.testerConfigs.checkTester(apiVersion) {
		s["is_data_tester"] = "1"
	} else {
		s["is_data_tester"] = "0"
	}
	// 设置 is_open_sub、is_eea的值
	if t.guoShenConfigs.checkGuoShen(appVersion) {
		s["is_open_sub"] = "1"
		s["is_eea"] = "0"
	} else {
		s["is_open_sub"] = "0"
	}
	return s
}

// GetAdConfigs 流量分配 返回分配后的AdConfigs、使用的策略版本号、是否参与了分流
// 如果是参与分流用户则返回true，否则false
// 对新分流用户进行会执行abTester
// 滴滴分流不惨与abTester
func (t *Traffic) GetAdConfigs(params map[string]any) (adconfigs.AdConfigs, string, bool) {
	var apiVersion = params["api_version"].(string)
	var thinkUid = params["thinkuid"].(string)
	var adWayNum = params["adwaynum"].(string)
	var serverCountryCode = params[ParamNameServerCountryCode].(string)
	var opt = params["opt"].(string)
	var adConfigs adconfigs.AdConfigs
	var err error
	sugared.Infof("GetAdConfigs apiVersion: %v,adwaynum: %s, thinduid: %s", apiVersion, adWayNum, thinkUid)

	// 低端机型判断
	if adConfigs, err = t.noNormalConfigs.GetNoNormalAdConfigs(opt); err == nil {
		//sugared.Infof("GetNoNormalAdConfigs: %v", adConfigs)
		return adConfigs, apiVersion, false
	}

	// 判断纯净版
	if layerType, ok := params[ParamNameLayerType].(string); ok && layerType == ParamNameLayerTypeValue {
		if adConfigs, err = t.chunJingConfigs.getAdConfigs(apiVersion); err == nil {
			//sugared.Infof("return chunjing adConfigs: %v", adConfigs)
			return adConfigs, apiVersion, false
		}
	}
	// isDataTester 判断逻辑
	//isDataTester := t.testerConfigs.checkTester(apiVersion)
	//if isDataTester {
	//	if testerAdWayNums, ok := t.bundleConfig.TesterAdWayNums[apiVersion]; ok {
	//		if slices.Contains(testerAdWayNums, adWayNum) {
	//			if adConfigs = t.llConfigs.GetAdConfigs(apiVersion, adWayNum); adConfigs != nil {
	//				return adConfigs, apiVersion, false
	//			}
	//		}
	//	}
	//}

	// 检查是否为qa用户，如果是，则重置adWayNum为qa用户指定adWayNum
	t.qaConfigs.resetQaAdWayNum(&adWayNum, thinkUid)

	// 用户指定配置
	if adConfigs, err = t.specialConfigs.getConfig(thinkUid); err == nil {
		return adConfigs, apiVersion, false
	}

	// 检查用户的apiVersion和adWayNum是否存在，存在用户无需继续继续分流【params["shunt_type"].(string)默认取新增 new】
	if adConfigs, err = t.llConfigs.GetAdConfigs("new", apiVersion, adWayNum); err == nil {
		sugared.Infof("用户无需分流")
		return adConfigs, apiVersion, false
	}

	// 分流【params["shunt_type"].(string)默认取新增 new】
	if adWayNums := t.llConfigs.GetAllAdWayNum("new", apiVersion); adWayNums != nil {
		sugared.Infof("Start Exec Distribute")
		// 滴滴分流
		if ddAdWayNum, err := t.distributeDidi(apiVersion, thinkUid); err == nil {
			if adWayNumConfig, ok := adWayNums[ddAdWayNum]; ok {
				return adWayNumConfig.Config, apiVersion, false
			}
		}
		// 原按国家加权轮询分流方式
		sugared.Infof("用户开始依据国家进行分流")
		if !slices.Contains(t.bundleConfig.CountryList, serverCountryCode) {
			serverCountryCode = "QT"
		}
		// Rate > 0 参与分组
		schemes := make([]distributes.Scheme, 0)
		for k, v := range adWayNums {
			sugared.Infof("AdwayNum: %s,Rate: %f", k, v.Rate)
			if v.Rate > 0 {
				scheme := distributes.Scheme{
					Name:   k,
					Weight: int(v.Rate * 100),
				}
				schemes = append(schemes, scheme)
			}
		}
		// 按国家分流 比例分流
		sugared.Infof("Distributes Schemes : %v", schemes)
		dayDistributeProvider := distributeFactory.GetDistributeProvider(distributes.DayDistributeProviderInstance)
		if dayDistributeProvider != nil {
			group := fmt.Sprintf("%s_%s", t.bundleConfig.BundleId, apiVersion)
			scheme := dayDistributeProvider.Distribute(group, serverCountryCode, schemes)
			sugared.Infof("Distributed Scheme: %v", scheme)
			if v, ok := adWayNums[scheme.Name]; ok {
				return v.Config, apiVersion, true
			}
		}

	}
	// apiVersion兜底配置
	if adConfigs, err = t.douDiConfigs.getDouDi(apiVersion); err == nil {
		sugared.Infof("getDouDi: %v", adConfigs)
		return adConfigs, apiVersion, false
	}
	// 返回配置中的默认总兜底
	sugared.Infof("get Default Doudi")
	return adconfigs.NewAdConfigsWithAdInfo(t.bundleConfig.AdInfoDefault), apiVersion, false
}

// distributeDidi 滴滴分流 按照随机比例分配
func (t *Traffic) distributeDidi(apiVersion, distinctId string) (string, error) {
	// 滴滴分流
	if didiConfig, ok := t.didiConfigs.getDidiConfig(apiVersion); ok {
		didiScehmes := didiConfig.Schemes
		if rand.Intn(100) < didiConfig.Rate {
			schemeIndex, err := didiDistributeProvider.NextScheme(didiConfig.Scene, didiConfig.Group, didiConfig.Platform, distinctId, len(didiScehmes))
			if err == nil {
				if schemeIndex >= 0 && schemeIndex < len(didiScehmes) {
					return didiScehmes[schemeIndex], nil
				} else if schemeIndex == -1 {
					return didiConfig.DefaultScheme, nil
				}
				sugared.Errorf("didi schemeIndex: %d error", schemeIndex)
			} else {
				sugared.Errorf("didiDistributeProvider.NextScheme Error: %v", err)
			}
		}
	}
	return "", fmt.Errorf("no didi distribute")
}

// getDidiDistributeGroup 获取滴滴分流组
func (t *Traffic) getDidiDistributeGroup(apiVersion string) string {
	return fmt.Sprintf("%s-%s", t.bundleConfig.BundleId, apiVersion)
}

func (t *Traffic) execDidiDistribute(group, distinctId, platform string, bucketCount int) (int, error) {
	return 0, nil
}
