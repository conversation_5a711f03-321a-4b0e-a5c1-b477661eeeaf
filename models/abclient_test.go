package models

import (
	"encoding/json"
	"fmt"
	"github.com/volcengine/datatester-go-sdk/client"
	"os"
	"testing"
)

var appKey = "1d8c51002e88d2a734530ef53b8049cc"
var abClient *client.AbClient

func TestAbProductConfig(t *testing.T) {
	abClient = newAbClient(appKey, os.Stdout)
	productConfig := abClient.GetConfig()
	productJson, err := json.Marshal(productConfig)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(string(productJson))
}
