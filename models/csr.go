package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/Masterminds/semver/v3"
	"golang.org/x/exp/maps"
	"hungrystudio.com/datatester/common/bb"
	"hungrystudio.com/datatester/dbconfigs"
	"hungrystudio.com/datatester/models/csr"
	"hungrystudio.com/datatester/models/distributes"
	"slices"
	"strconv"
	"sync"
	"time"
)

type CSRRequest struct {
	BundleId       string  `json:"bundleId"`
	AppVersion     string  `json:"appVersion"`
	DistinctId     string  `json:"thinkuid"`
	InstallTime    int64   `json:"install_time"`
	CSRNum         string  `json:"hit_way_0814"`
	NewPlayerState bool    `json:"newPlayerState"`
	Ecpm           float64 `json:"ecpm"`
	AdNum          float64 `json:"adnum"`
	GameNum        float64 `json:"gameNum"`
	Aday           int     `json:"aday"`
}

//type RequestCsrParams map[string]any

const (
	CSRNumNo       string = "9999" // 默认值
	CSRNumNew      string = "9901" // 新增
	CSRNumUserNull string = "9902" // 数仓用户为空
	CSRNumParseErr string = "9903" // json解析错误
	CSRNumNoOpen   string = "9904" // csr分流未开启
)

const CsrDBUpdateInterval = time.Minute * 1

var invalidCSRNums = []string{
	CSRNumNo,
	CSRNumNew,
	CSRNumUserNull,
	CSRNumParseErr,
	CSRNumNoOpen,
}

type CsrDBConfig struct {
	Id            uint
	BundleId      string
	ClientVersion string
	GroupId       string
	Ratio         int
	Config        bb.AbWayMaps
	StartTs       int
	EndTs         int
	IsOpen        int
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

const (
	CSRTransitionTypeNew    string = "new"
	CSRTransitionTypeActive string = "active"
)

const (
	CSRTransitionSchemeSwitchOn  = "on"
	CSRTransitionSchemeSwitchOff = "off"
)

const CSRTransitionSchemeServerSwtichField = "serverSwitch"

type CsrTransition struct {
	Id            uint
	BundleId      string
	ClientVersion *semver.Version
	MaVersion     *semver.Version
	Type          string
	GroupId       string
	Ratio         int
	MarkRatio     []int
	Config        []map[string]any
	Schemes       []distributes.Scheme
	SchemeIndex   map[string]int
	StartTs       int64
	EndTs         int64
	IsOpen        int
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

type CsrDBConfigs struct {
	mux         sync.RWMutex
	configs     map[string][]*CsrTransition
	transitions map[string]map[string][]*CsrTransition
}

func NewCsrDBConfigs() *CsrDBConfigs {
	dbCsrConfigs := &CsrDBConfigs{
		configs: make(map[string][]*CsrTransition),
	}
	dbCsrConfigs.loadConfigs()
	go dbCsrConfigs.updateConfigs()
	return dbCsrConfigs
}

func (csrConfigs *CsrDBConfigs) loadConfigs() {
	csrConfigs.mux.Lock()
	defer csrConfigs.mux.Unlock()
	sugared.Infof("CsrDBConfigs Update Start")

	confList := make(map[string][]*CsrTransition)
	if configs := dbconfigs.FindCsrConfigByCsr(); configs != nil {
		sugared.Infof("Update CsrConfig Old Start")
		for _, config := range configs {
			if _, ok := confList[config.BundleId]; !ok {
				confList[config.BundleId] = make([]*CsrTransition, 0)
			}
			csrConf, err := parseDBCsrConfig(config)
			if err != nil {
				continue
			}
			confList[config.BundleId] = append(confList[config.BundleId], csrConf)
		}
		sugared.Infof("Update CsrConfig Old Success")
	}
	csrConfigs.configs = confList
	for key, configs := range confList {
		sugared.Infof("Key: %s", key)
		for _, config := range configs {
			sugared.Infof("BundleId: %s,ClientVersion: %s", config.BundleId, config.ClientVersion)
		}
	}

	transitions := make(map[string]map[string][]*CsrTransition)
	if transitionsDB := dbconfigs.FindCsrConfigTransition(); transitionsDB != nil {
		sugared.Infof("Update CsrConfig Transition Start")
		for _, config := range transitionsDB {
			//sugared.Infof("transitionDB: %+v", config)
			if _, ok := transitions[config.BundleId]; !ok {
				transitions[config.BundleId] = make(map[string][]*CsrTransition)
			}
			transitionConfig, err := parseDBCsrTransition(config)
			if err != nil {
				sugared.Errorf("parseDBCsrTransition err: %v", err)
				continue
			}
			if _, ok := transitions[config.BundleId][config.Type]; !ok {
				transitions[config.BundleId][config.Type] = make([]*CsrTransition, 0)
			}
			transitions[config.BundleId][config.Type] = append(transitions[config.BundleId][config.Type], transitionConfig)
		}
		sugared.Infof("Update CsrConfig Transition Success")
	}
	csrConfigs.transitions = transitions

	sugared.Infof("CsrDBConfigs Update Success")
}

func (csrConfigs *CsrDBConfigs) updateConfigs() {
	ticker := time.NewTicker(CsrDBUpdateInterval)
	for range ticker.C {
		csrConfigs.loadConfigs()
	}
}

// filterOld 过滤就版本 只本用户版本参数
func (csrConfigs *CsrDBConfigs) filterOld(csrRequest CSRRequest) []*CsrTransition {
	csrConfigs.mux.RLock()
	defer csrConfigs.mux.RUnlock()
	if transitions, ok := csrConfigs.configs[csrRequest.BundleId]; ok {
		now := time.Now().Unix()
		version, err := semver.NewVersion(csrRequest.AppVersion)
		if err != nil {
			sugared.Errorf("Error parsing app version %s, error: %s", csrRequest.AppVersion, err.Error())
			return nil
		}
		csrDBConfigList := make([]*CsrTransition, 0)
		for _, v := range transitions {
			if now >= v.StartTs && now <= v.EndTs {
				if version.Compare(v.ClientVersion) >= 0 {
					csrDBConfigList = append(csrDBConfigList, v)
				}
			}
		}
		if len(csrDBConfigList) == 0 {
			return nil
		}
		return csrDBConfigList
	}
	return nil
}

// filterCsrTransitions 过滤符合用户需求的CsrTransition
func (csrConfigs *CsrDBConfigs) filterCsrTransitions(csrRequest CSRRequest) []*CsrTransition {
	csrConfigs.mux.RLock()
	defer csrConfigs.mux.RUnlock()
	appVersion, err := semver.NewVersion(csrRequest.AppVersion)
	if err != nil {
		sugared.Errorf("Error parsing app version %s, error: %s", csrRequest.AppVersion, err.Error())
		return nil
	}
	tType := CSRTransitionTypeActive
	if csrRequest.NewPlayerState {
		tType = CSRTransitionTypeNew
	}
	currentTime := time.Now().Unix()
	if transitionTypes, ok := csrConfigs.transitions[csrRequest.BundleId][tType]; ok {
		transitions := make([]*CsrTransition, 0)
		for _, transition := range transitionTypes {
			if currentTime >= transition.StartTs && currentTime <= transition.EndTs {
				if appVersion.Compare(transition.ClientVersion) >= 0 && appVersion.Compare(transition.MaVersion) <= 0 {
					transitions = append(transitions, transition)
				}
			}
		}
		return transitions
	}
	sugared.Errorf("Bundle(%s) Type(%s) not found config", csrRequest.BundleId, tType)
	return nil
}

func (csrConfigs *CsrDBConfigs) getTransitionConfigs() map[string]map[string][]*CsrTransition {
	csrConfigs.mux.RLock()
	defer csrConfigs.mux.RUnlock()
	return csrConfigs.transitions
}

func parseDBCsrConfig(config dbconfigs.CsrConfig) (*CsrTransition, error) {
	clientVersion, err := semver.NewVersion(config.ClientVersion)
	if err != nil {
		sugared.Errorf("Error parsing client version %s, error: %s", config.ClientVersion, err.Error())
		return nil, err
	}
	confs := make([]map[string]any, 0)
	err = json.Unmarshal([]byte(config.Config), &confs)
	if err != nil {
		return nil, err
	}
	transition := &CsrTransition{
		Id:            config.Id,
		BundleId:      config.BundleId,
		ClientVersion: clientVersion,
		GroupId:       config.GroupId,
		Ratio:         config.Ratio,
		Config:        make([]map[string]any, 0),
		SchemeIndex:   make(map[string]int),
		Schemes:       make([]distributes.Scheme, 0),
		StartTs:       int64(config.StartTs),
		EndTs:         int64(config.EndTs),
		IsOpen:        config.IsOpen,
		CreatedAt:     config.CreatedAt,
		UpdatedAt:     config.UpdatedAt,
	}
	index := 0
	for _, conf := range confs {
		abWayNum, ok := conf["abWayNum"]
		if !ok {
			sugared.Errorf("SchemeConfig(%+v) not found abWayNum", conf)
			continue
		}
		abWayValue, ok := conf["abWay"]
		if !ok {
			sugared.Errorf("SchemeConfig(%+v) not found abWay", conf)
			continue
		}
		schemeName := fmt.Sprintf("%v_%v", abWayNum, abWayValue)
		transition.Schemes = append(transition.Schemes, distributes.Scheme{Name: schemeName})
		transition.SchemeIndex[schemeName] = index
		conf["ratio"] = config.Ratio
		transition.Config = append(transition.Config, conf)
		index++
	}
	return transition, nil
}

func parseDBCsrTransition(config dbconfigs.AbtestCsrConfigTransition) (*CsrTransition, error) {
	clientVersion, err := semver.NewVersion(config.ClientVersion)
	if err != nil {
		sugared.Errorf("Error parsing client version %s, error: %s", config.ClientVersion, err.Error())
		return nil, err
	}
	maxVersion, err := semver.NewVersion(config.MaxVersion)
	if err != nil {
		sugared.Errorf("Error parsing max version %s, error: %s", config.MaxVersion, err.Error())
		return nil, err
	}
	markRatio := make([]int, 0)
	err = json.Unmarshal([]byte(config.MarkRatio), &markRatio)
	if err != nil {
		sugared.Errorf("Error parsing mark ratio %s, error: %s", config.MarkRatio, err.Error())
		return nil, err
	}
	transition := &CsrTransition{
		Id:            config.Id,
		BundleId:      config.BundleId,
		ClientVersion: clientVersion,
		MaVersion:     maxVersion,
		Type:          config.Type,
		GroupId:       config.GroupId,
		Ratio:         config.Ratio,
		MarkRatio:     markRatio,
		Config:        make([]map[string]any, 0),
		SchemeIndex:   make(map[string]int),
		Schemes:       make([]distributes.Scheme, 0),
		StartTs:       int64(config.StartTs),
		EndTs:         int64(config.EndTs),
		IsOpen:        config.IsOpen,
		CreatedAt:     config.CreatedAt,
		UpdatedAt:     config.UpdatedAt,
	}
	confs := make([]map[string]any, 0)
	err = json.Unmarshal([]byte(config.Config), &confs)
	if err != nil {
		sugared.Errorf("Error parsing CSRTransition.Config: %+v,error: %v", transition, err)
		return nil, err
	}
	index := 0
	for _, conf := range confs {
		//sugared.Infof("Conf: %+v", conf)
		if switchValue, ok := conf[CSRTransitionSchemeServerSwtichField]; ok {
			if v, ok := switchValue.(string); ok && v == CSRTransitionSchemeSwitchOn {
				schemeName := ParseSchemeId(transition.Type, conf)
				if schemeName == "" {
					continue
				}
				transition.Schemes = append(transition.Schemes, distributes.Scheme{Name: schemeName})
				transition.SchemeIndex[schemeName] = index
				transition.Config = append(transition.Config, conf)
				index++
			}
		}
	}
	sugared.Infof("tansitionInfo: BundlId:%s,ClientVersion: %s,MaxVersion:%s,Type:%s,Gruop:%s,Ratio:%d,MarkRatio:%+v",
		transition.BundleId, transition.ClientVersion, transition.MaVersion, transition.Type, transition.GroupId, transition.Ratio, transition.MarkRatio)
	sugared.Infof("transition.Schemes: %+v", transition.Schemes)
	sugared.Infof("transition.SchemeIndex: %+v", transition.SchemeIndex)
	return transition, nil
}

func ParseSchemeId(confType string, conf map[string]any) string {
	id := ""
	if confType == CSRTransitionTypeNew {
		if plan, ok := conf["plan"]; ok {
			id = fmt.Sprintf("%v", plan)
		}
	} else {
		abWayNum, ok := conf["abWayNum"]
		if !ok {
			sugared.Errorf("SchemeConfig(%+v) not found abWayNum", conf)
			return id
		}
		abWayValue, ok := conf["abWay"]
		if !ok {
			sugared.Errorf("SchemeConfig(%+v) not found abWay", conf)
			return id
		}
		id = fmt.Sprintf("%v_%v", abWayNum, abWayValue)
	}
	return id
}

const CSRParamCacheKeyPrefix string = "CSRBBGP:"

type CSRCacheData struct {
	ActiveDays   int     `json:"activeDays"`
	AvgGameTime  float64 `json:"avgGameTime"`
	AvgAds       float64 `json:"avgAds"`
	AvgGames     float64 `json:"avgGames"`
	AvgAdRevenue float64 `json:"avgAdRevenue"`
	Ecpm         float64 `json:"eCPM"`
}

type CsrConfig struct {
	IsOpen           bool                `yaml:"is_open" json:"is_open"`
	IndentityKeys    []string            `yaml:"indentity_keys" json:"indentity_keys"`
	IndentityConfigs CsrIndentityConfigs `yaml:"indentity_configs" json:"indentity_configs"`
}

type CsrIndentityConfig struct {
	Kind  string `json:"kind"`
	Value []any  `json:"value"`
}

type CsrIndentityConfigs map[string]CsrIndentityConfig

type CSRTrafficConfig struct {
	ConfigDB       string               `yaml:"configDB" json:"configDB"`
	UserCache      string               `yaml:"userCache" json:"userCache"`
	CsrParamsCache string               `yaml:"csrParamsCache" json:"csrParamsCache"`
	UpdateInterval int64                `yaml:"updateInterval" json:"updateInterval"`
	Csr            map[string]CsrConfig `yaml:"csr" json:"csr"`
}

type CSRTraffic struct {
	config          CSRTrafficConfig
	bundleCsrConfig map[string]CsrConfig
	csrDBConfigs    *CsrDBConfigs
	mux             sync.Mutex
	algos           map[string]*distributes.SudokuRoundRobin
}

func NewCSRTraffic(csrTrafficConfig CSRTrafficConfig) *CSRTraffic {
	csrDbConfigs := NewCsrDBConfigs()
	return &CSRTraffic{
		config:          csrTrafficConfig,
		bundleCsrConfig: csrTrafficConfig.Csr,
		csrDBConfigs:    csrDbConfigs,
		mux:             sync.Mutex{},
		algos:           make(map[string]*distributes.SudokuRoundRobin),
	}
}

type CsrResult struct {
	CsrNum string         `json:"csrNum"`
	Key    string         `json:"key"`
	Group  string         `json:"group"`
	Config bb.AbWayConfig `json:"config"`
}

type CsrResponse struct {
	CsrNum  string  `json:"csr_num"`
	CsrJson CsrJson `json:"csr_json"`
	Key     string  `json:"key"`
}

type CsrJson struct {
	Data        []map[string]any `json:"data"`
	ShutIndexes []string         `json:"shutIndexes"`
}

func (c *CSRTraffic) GetConfig(paramsJson []byte) CsrResponse {
	csrRequest := CSRRequest{}
	err := json.Unmarshal(paramsJson, &csrRequest)
	if err != nil {
		sugared.Errorf("Failed to unmarshal CSR request: %s, err: %s", paramsJson, err.Error())
		return CsrResponse{CsrNum: CSRNumParseErr}
	}
	// 新用户不分流
	if (time.Now().UnixMilli() - csrRequest.InstallTime) < 24*60*60*1000*14 {
		sugared.Infof("install time not 14 days")
		return CsrResponse{CsrNum: CSRNumNew}
	}

	csrConfig, ok := c.bundleCsrConfig[csrRequest.BundleId]
	if !ok {
		sugared.Infof("bundleConfig: %+v", csrConfig)
		return CsrResponse{CsrNum: CSRNumNoOpen}
	}

	// csr分流是否开启
	if !csrConfig.IsOpen {
		sugared.Infof("csrConfig.IsOpen: %v", csrConfig.IsOpen)
		return CsrResponse{CsrNum: CSRNumNoOpen}
	}
	// 判断对应版本配置
	transitions := c.csrDBConfigs.filterOld(csrRequest)
	if transitions == nil {
		return CsrResponse{CsrNum: CSRNumNo}
	}

	csrRequest.NewPlayerState = false

	data := make([]map[string]any, 0)
	shutIndexes := make([]string, 0)
	csrParams := map[string]any{
		"ecpm":    csrRequest.Ecpm,
		"adnum":   csrRequest.AdNum,
		"gamenum": csrRequest.GameNum,
	}
	wg := sync.WaitGroup{}
	lock := sync.Mutex{}
	dataAppend := func(schemeData map[string]any, shutIndex string) {
		lock.Lock()
		defer lock.Unlock()
		data = append(data, schemeData)
		shutIndexes = append(shutIndexes, shutIndex)
	}
	for _, transition := range transitions {
		wg.Add(1)
		go func(transition *CsrTransition) {
			defer wg.Done()
			group := transition.GroupId
			var schemeData = make(map[string]any)
			var shutIndex = ""
			var scheme = ""
			var err error
			userSchemeCache, errCache := c.getUserCache(csrRequest.BundleId, csrRequest.AppVersion, group, csrRequest.DistinctId)
			if errCache != nil {
				schemeData, shutIndex, scheme, err = c.distributeTransition(transition, false, csrConfig, csrParams)
			} else {
				if index, ok := transition.SchemeIndex[userSchemeCache]; ok {
					schemeData = maps.Clone(transition.Config[index])
					shutIndex = ""
				} else {
					schemeData, shutIndex, scheme, err = c.distributeTransition(transition, false, csrConfig, csrParams)
				}
			}
			if err == nil {
				schemeData["ratio"] = transition.Ratio
				dataAppend(schemeData, shutIndex)
				c.setUserCache(transition.BundleId, transition.ClientVersion.String(), transition.GroupId, csrRequest.DistinctId, scheme, transition.EndTs)
			}
		}(transition)

	}
	wg.Wait()
	return CsrResponse{
		CsrJson: CsrJson{
			Data:        data,
			ShutIndexes: shutIndexes,
		},
	}
}

func (c *CSRTraffic) GetTransitionConfig(paramsJson []byte) CsrResponse {
	csrRequest := CSRRequest{}
	err := json.Unmarshal(paramsJson, &csrRequest)
	if err != nil {
		sugared.Errorf("paramsJson json.Unmarshal err: %+v", err)
		return CsrResponse{CsrNum: CSRNumParseErr}
	}
	sugared.Infof("RequestParams: %+v", csrRequest)
	csrConfig, ok := c.bundleCsrConfig[csrRequest.BundleId]
	if !ok {
		sugared.Errorf("BundleId(%s) not found csr config", csrRequest.BundleId)
		return CsrResponse{CsrNum: CSRNumNoOpen}
	}
	if !csrConfig.IsOpen {
		sugared.Infof("BundleId(%s),csrConfig.IsOpen: %t", csrRequest.BundleId, csrConfig.IsOpen)
		return CsrResponse{CsrNum: CSRNumNoOpen}
	}
	if transitions := c.csrDBConfigs.filterCsrTransitions(csrRequest); transitions != nil {
		data := make([]map[string]any, 0)
		shutIndexes := make([]string, 0)
		lock := sync.Mutex{}

		dataAppend := func(schemeData map[string]any, shutIndex string) {
			lock.Lock()
			defer lock.Unlock()
			data = append(data, schemeData)
			shutIndexes = append(shutIndexes, shutIndex)
		}

		wg := sync.WaitGroup{}
		var csrParams map[string]any
		if !csrRequest.NewPlayerState {
			csrParams = map[string]any{
				"ecpm":    csrRequest.Ecpm,
				"adnum":   csrRequest.AdNum,
				"gamenum": csrRequest.GameNum,
				"aday":    csrRequest.Aday,
			}
		}
		for _, transition := range transitions {
			wg.Add(1)
			go func(transition *CsrTransition) {
				defer wg.Done()
				var schemeData map[string]any
				var shutIndex string
				var scheme string
				var err error
				//var userSchemeCache string
				userSchemeCache, errCache := c.getUserCache(csrRequest.BundleId, csrRequest.AppVersion, transition.GroupId, csrRequest.DistinctId)
				if errCache != nil {
					// 直接分配
					schemeData, shutIndex, scheme, err = c.distributeTransition(transition, csrRequest.NewPlayerState, csrConfig, csrParams)
				} else {
					// 判断用户是否当前方案是否有效
					if index, ok := transition.SchemeIndex[userSchemeCache]; ok {
						schemeData = maps.Clone(transition.Config[index])
						scheme = userSchemeCache
						shutIndex = ""
					} else {
						// 方案无效重新分配
						schemeData, shutIndex, scheme, err = c.distributeTransition(transition, csrRequest.NewPlayerState, csrConfig, csrParams)
					}
				}
				if err == nil {
					schemeData["ratio"] = transition.Ratio
					schemeData["markRatio"] = transition.MarkRatio
					delete(schemeData, CSRTransitionSchemeServerSwtichField)
					dataAppend(schemeData, shutIndex)
					c.setUserCache(transition.BundleId, transition.ClientVersion.String(), transition.GroupId, csrRequest.DistinctId, scheme, transition.EndTs)
				}
			}(transition)
		}
		wg.Wait()
		return CsrResponse{
			CsrJson: CsrJson{
				Data:        data,
				ShutIndexes: shutIndexes,
			},
		}
	}
	return CsrResponse{CsrNum: CSRNumNoOpen}
}

func (c *CSRTraffic) distributeTransition(transition *CsrTransition, isNew bool, csrConfig CsrConfig, csrParams map[string]any) (map[string]any, string, string, error) {
	csrIdentity := ""
	algoKey := "hs:csr:" + transition.BundleId + ":" + transition.ClientVersion.String() + ":" + transition.GroupId
	if isNew {
		algoKey += ":new"
	} else {
		for i, iKey := range csrConfig.IndentityKeys {
			value, ok := csrParams[iKey]
			if !ok {
				continue
			}
			if i > 0 {
				csrIdentity += "_"
			}
			keyCsr := csrConfig.IndentityConfigs[iKey]
			identity := csr.ParseIndentity(keyCsr.Kind, value, keyCsr.Value)
			csrIdentity += strconv.Itoa(identity)
		}
		algoKey += ":" + csrIdentity + ":active"
	}
	if len(transition.Schemes) == 0 {
		sugared.Errorf("transition.Schemes len 0, transitionSchemes: %v", transition.Schemes)
		return nil, "", "", errors.New("no algo")
	}
	algo := c.getCsrAlgo(algoKey, slices.Clone(transition.Schemes))
	scheme := algo.Next().Name
	sugared.Infof("AlgoKey: %s,IsNew: %t, Scheme: %s", algoKey, isNew, scheme)
	return maps.Clone(transition.Config[transition.SchemeIndex[scheme]]), csrIdentity, scheme, nil
}

func (c *CSRTraffic) userCacheKey(bundleId, appVersion, groupId, distinctId string) string {
	return fmt.Sprintf("hs:csr:user%s:%s:%s:%s", bundleId, appVersion, groupId, distinctId)
}

func (c *CSRTraffic) setUserCache(bundleId, appVersion, groupId, distinctId, csrNum string, endTs int64) {
	expiration := time.Duration(endTs-time.Now().Unix()) * time.Second
	key := c.userCacheKey(bundleId, appVersion, groupId, distinctId)
	err := redisClient.Set(context.Background(), key, csrNum, expiration).Err()
	if err != nil {
		sugared.Errorf("Failed to set user cache key: %s, err: %s", csrNum, err.Error())
	}
}

func (c *CSRTraffic) getUserCache(bundleId, appVersion, groupId, distinctId string) (string, error) {
	key := c.userCacheKey(bundleId, appVersion, groupId, distinctId)
	crsNum, err := redisClient.Get(context.Background(), key).Result()
	if err != nil {
		sugared.Errorf("Failed to get user cache key: %s, err: %s", key, err.Error())
	}
	return crsNum, err
}

func (c *CSRTraffic) getCsrAlgo(key string, schemes []distributes.Scheme) *distributes.SudokuRoundRobin {
	c.mux.Lock()
	defer c.mux.Unlock()
	algo, ok := c.algos[key]
	if !ok {
		randSchemes := distributes.RandomOrder(schemes)
		sugared.Infof("key: %s, Schemes: %v", key, randSchemes)
		algo = distributes.NewSudokuRoundRobin(key, randSchemes)
		if algo != nil {
			c.algos[key] = algo
		} else {
			return nil
		}
	}
	algo.ResetSchemes(schemes)
	return algo
}

func (c *CSRTraffic) GetTransitions() map[string]map[string][]*CsrTransition {
	return c.csrDBConfigs.getTransitionConfigs()
}
