package models

import (
	"bytes"
	"encoding/json"
	"hungrystudio.com/datatester/models/testergame"
	"io"
	"net/http"
	"testing"
	"time"

	"hungrystudio.com/core/uuid"
)

const testServerUrl = "http://bbggt-test.afafb.com/game_init"

func getGameRequestJson(t *testing.T, gameRequest testergame.GameInitRequest) []byte {
	requestData, err := json.Marshal(gameRequest)
	if err != nil {
		t.<PERSON>("Marshal error: %v", err)
	}
	return requestData
}

func sendGameInit(t *testing.T, request []byte) []byte {
	client := &http.Client{}
	resp, err := client.Post(testServerUrl, "application/json", bytes.NewBuffer(request))
	if err != nil {
		t.<PERSON>rf("Post error: %v", err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("ReadAll error: %v", err)
	}
	return body
}

func parseResult(t *testing.T, response []byte) testergame.ExperimentResult {
	var result map[string]map[string]testergame.ExperimentResult
	err := json.Unmarshal(response, &result)
	if err != nil {
		t.Errorf("Unmarshal error: %v", err)
	}
	if experimentDataMap, ok := result["experimentDataMap"]; ok {
		if experimentResult, ok := experimentDataMap["defaultLayer"]; ok {
			return experimentResult
		}
	}
	t.Errorf("ExperimentResult not found")
	return testergame.ExperimentResult{}
}

func TestGameInitNewUser(t *testing.T) {
	uid := uuid.NewUUID()
	gameRequest := testergame.GameInitRequest{
		Uid:           uid,
		InstallTime:   time.Now().UnixMilli() - 1000*60*60,
		GameVersion:   "2.9.6",
		SdkVersion:    "9.9.9",
		BundleId:      "com.block.juggle",
		GameWayNum:    "",
		IsClientPanel: false,
		OldBucket:     0,
	}
	requestJson := getGameRequestJson(t, gameRequest)
	t.Logf("requestJson: %s", requestJson)
	response := sendGameInit(t, requestJson)
	experimentResult := parseResult(t, response)
	if experimentResult.ExperimentConfig != nil {
		t.Logf("GameInitNewUser BucketId: %d, GameWayNum: %s, ExperimentType: %d", experimentResult.BucketId, experimentResult.GameWayNum, experimentResult.ExperimentType)
	} else {
		t.Log("no experiment")
	}
}
