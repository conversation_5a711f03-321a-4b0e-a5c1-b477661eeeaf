package business

import (
	"encoding/json"
	"hungrystudio.com/datatester/models/distributes"
	"strconv"
	"strings"
	"sync"
	"time"
)

type Configs struct {
	configs      map[string]map[string]map[string]AdwaySchemes
	whiteConfigs map[string]map[string]map[string]map[string]map[string]any
	mux          sync.RWMutex
}

type AdwaySchemes struct {
	MiniVersion string                    `json:"miniVersion"`
	WhiteList   map[string]string         `json:"white_list"`
	Schemes     map[string]map[string]any `json:"schemes"`
	DisSchemes  []distributes.Scheme      `json:"disSchemes"`
}

const ConfigsUpdateInterval = time.Minute * 1

const (
	SchemeStatusClosed = iota
	SchemeStatusOpen
)

func NewConfigs(updateInterval int64) *Configs {
	bc := &Configs{
		configs: make(map[string]map[string]map[string]AdwaySchemes),
	}
	bc.loadConfigs()
	go bc.update(updateInterval)
	return bc
}

func (bc *Configs) loadConfigs() {
	bc.mux.Lock()
	defer bc.mux.Unlock()
	sugared.Info("Start Load Configs")
	dbConfigs, err := GetAllBusinessConfigs()
	if err != nil {
		return
	}
	allConfigs := make(map[string]map[string]map[string]AdwaySchemes)
	whiteConfigs := make(map[string]map[string]map[string]map[string]map[string]any)
	for _, dbConfig := range dbConfigs {

		if _, ok := allConfigs[dbConfig.Bundleid]; !ok {
			allConfigs[dbConfig.Bundleid] = make(map[string]map[string]AdwaySchemes)
			whiteConfigs[dbConfig.Bundleid] = make(map[string]map[string]map[string]map[string]any)
		}
		if _, ok := allConfigs[dbConfig.Bundleid][dbConfig.Version]; !ok {
			allConfigs[dbConfig.Bundleid][dbConfig.Version] = make(map[string]AdwaySchemes)
			whiteConfigs[dbConfig.Bundleid][dbConfig.Version] = make(map[string]map[string]map[string]any)
		}
		if _, ok := allConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype]; !ok {
			allConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype] = AdwaySchemes{
				MiniVersion: dbConfig.MiniVersion,
				WhiteList:   make(map[string]string),
				Schemes:     make(map[string]map[string]any),
				DisSchemes:  make([]distributes.Scheme, 0),
			}
			whiteConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype] = make(map[string]map[string]any)
		}
		schemeConfig := make(map[string]any)
		err = json.Unmarshal([]byte(dbConfig.Config), &schemeConfig)
		if err != nil {
			sugared.Errorf("json.Unmarshal(%s) error: %v", dbConfig.Config, err)
			continue
		}
		if dbConfig.WhiteList != "" {
			whiteList := strings.Split(dbConfig.WhiteList, ",")
			for _, distinctId := range whiteList {
				allConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype].WhiteList[distinctId] = dbConfig.Adwaynum
			}
			whiteConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype][dbConfig.Adwaynum] = schemeConfig
		}
		if dbConfig.Status == SchemeStatusOpen {
			weight, err := strconv.ParseFloat(dbConfig.Rate, 32)
			if err != nil {
				sugared.Errorf("strconv.ParseFloat(%s) error: %v", dbConfig.Rate, err)
				continue
			}
			//schemeConfig := make(map[string]any)
			//err = json.Unmarshal([]byte(dbConfig.Config), &schemeConfig)
			//if err != nil {
			//	sugared.Errorf("json.Unmarshal(%s) error: %v", dbConfig.Config, err)
			//	continue
			//}

			disScheme := distributes.Scheme{
				Name:   dbConfig.Adwaynum,
				Weight: int(weight * 100),
			}

			adwaySchemes := allConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype]
			adwaySchemes.Schemes[dbConfig.Adwaynum] = schemeConfig
			adwaySchemes.DisSchemes = append(adwaySchemes.DisSchemes, disScheme)
			allConfigs[dbConfig.Bundleid][dbConfig.Version][dbConfig.Adwaytype] = adwaySchemes
		}
	}
	bc.configs = allConfigs
	bc.whiteConfigs = whiteConfigs
	sugared.Info("Over Load Configs Success")
}

func (bc *Configs) update(updateInterval int64) {
	ticker := time.NewTicker(time.Minute * time.Duration(updateInterval))
	for range ticker.C {
		bc.loadConfigs()
	}
}

func (bc *Configs) GetAllConfigs() map[string]map[string]map[string]AdwaySchemes {
	bc.mux.RLock()
	defer bc.mux.RUnlock()
	return bc.configs
}

func (bc *Configs) GetVersionConfigs(bundleId, version string) map[string]AdwaySchemes {
	bc.mux.RLock()
	defer bc.mux.RUnlock()
	if configs, ok := bc.configs[bundleId][version]; ok {
		return configs
	}
	return nil
}

func (bc *Configs) GetAllWhilteConfigs() map[string]map[string]map[string]map[string]map[string]any {
	bc.mux.RLock()
	defer bc.mux.RUnlock()
	return bc.whiteConfigs
}

func (bc *Configs) GetWhiteConfig(bundleId, version, adwayType, adwayNum string) map[string]any {
	bc.mux.RLock()
	defer bc.mux.RUnlock()
	if config, ok := bc.whiteConfigs[bundleId][version][adwayType][adwayNum]; ok {
		return config
	}
	return nil
}
