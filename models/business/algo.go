package business

import (
	"slices"
	"sync"

	"hungrystudio.com/datatester/models/distributes"
)

type SchemeAlgo struct {
	algos map[string]distributes.DistributeAlgo
	sync.Mutex
}

func NewSchemeAlgo() *SchemeAlgo {
	return &SchemeAlgo{
		algos: make(map[string]distributes.DistributeAlgo),
	}
}

func (sa *SchemeAlgo) GetAlgo(key string, schemes []distributes.Scheme) distributes.DistributeAlgo {
	if algo, ok := sa.algos[key]; ok {
		algo.ResetSchemes(schemes)
		return algo
	}
	return sa.newAlgo(key, slices.Clone(schemes))
}

func (sa *SchemeAlgo) newAlgo(key string, schemes []distributes.Scheme) distributes.DistributeAlgo {
	sa.Lock()
	defer sa.Unlock()
	algo := distributes.NewWeightRoundRobin(key, schemes)
	sa.algos[key] = algo
	return algo
}

func (sa *SchemeAlgo) Close() {
	sa.Lock()
	defer sa.Unlock()
	for _, algo := range sa.algos {
		algo.Close()
	}
}
