package business

import (
	"encoding/json"
	"fmt"
)

type DBConfig struct {
	Id          uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Bundleid    string `xorm:"'bundleid' not null comment('包名') unique(bundleid_version_adwaytype_adwaynum) VARCHAR(64)"`
	Version     string `xorm:"'version' not null comment('api版本') unique(bundleid_version_adwaytype_adwaynum) index VARCHAR(64)"`
	Adwaytype   string `xorm:"'adwaytype' not null comment('方案类型') unique(bundleid_version_adwaytype_adwaynum) VARCHAR(32)"`
	Adwaynum    string `xorm:"'adwaynum' not null comment('方案id') unique(bundleid_version_adwaytype_adwaynum) index VARCHAR(32)"`
	Config      string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate        string `xorm:"'rate' not null comment('占比') DECIMAL(10,2)"`
	Type        int    `xorm:"'type' not null default 0 comment('新增活跃 0活跃 1新增')"`
	MiniVersion string `xorm:"'mini_version' not null comment('最小appVersion') VARCHAR(16)"`
	WhiteList   string `xorm:"'white_list' comment('白名单') TEXT(65535)"`
	Status      int    `xorm:"'status' not null default 0 comment('0 生效 1 不生效') index INT"`
}

type DBConfigJson struct {
	Id        uint           `json:"id"`
	Bundleid  string         `json:"bundleid"`
	Version   string         `json:"version"`
	Adwaytype string         `json:"adwaytype"`
	Adwaynum  string         `json:"adwaynum"`
	Config    map[string]any `json:"config"`
	Rate      string         `json:"rate"`
	Type      int            `json:"type"`
	Status    int            `json:"status"`
}

func (m *DBConfig) TableName() string {
	return "business_config"
}

func SaveBusinessConfigToDB(jsonData []byte) error {
	configJsonData := DBConfigJson{}
	err := json.Unmarshal(jsonData, &configJsonData)
	if err != nil {
		sugared.Errorf("json.Unmarshal: %s, error: %v", string(jsonData), err)
		return err
	}
	configContent, err := json.Marshal(configJsonData.Config)
	if err != nil {
		sugared.Errorf("json.Marshal: %s, error: %v", configJsonData.Config, err)
		return err
	}
	dbConfigData := DBConfig{
		Bundleid:  configJsonData.Bundleid,
		Version:   configJsonData.Version,
		Adwaytype: configJsonData.Adwaytype,
		Adwaynum:  configJsonData.Adwaynum,
		Config:    string(configContent),
		Rate:      configJsonData.Rate,
		Type:      configJsonData.Type,
		Status:    configJsonData.Status,
	}
	sugared.Infof("configJsonData: %+v", configJsonData)
	sugared.Infof("dbConfigData: %+v", dbConfigData)
	n, err := xormEngine.Where("`bundleid`=? AND `version`=? AND `adwaytype`=? AND `adwaynum`=?", configJsonData.Bundleid, configJsonData.Version, configJsonData.Adwaytype, configJsonData.Adwaynum).Count(DBConfig{})
	if err != nil {
		sugared.Errorf("xormEngine Error: %v", err)
		return err
	}
	if n == 0 {
		n, err = xormEngine.Insert(dbConfigData)
		if err != nil {
			sugared.Errorf("xormEngine.Insert: %v, error: %v", dbConfigData, err)
			return err
		}
		if n == 1 {
			return nil
		}
	} else {
		n, err = xormEngine.Where("`bundleid`=? AND `version`=? AND `adwaytype`=? AND `adwaynum`=?", configJsonData.Bundleid, configJsonData.Version, configJsonData.Adwaytype, configJsonData.Adwaynum).
			Cols("config", "rate", "type", "status").Update(dbConfigData)
		if err != nil {
			sugared.Errorf("xormEngine.Update: %v, error: %v", dbConfigData, err)
			return err
		}
		sugared.Infof("xormEngine.Update: %v,affact rows: %d", dbConfigData, n)
		return nil
	}

	return fmt.Errorf("affact rows: %d", n)
}

func GetBusinessConfigsPagination(page, size int) ([]DBConfig, error) {
	start := (page - 1) * size
	dbConfigs := make([]DBConfig, 0)
	err := xormEngine.Where("`status`=1").Desc("`id`").Limit(size, start).Find(&dbConfigs)
	if err != nil {
		sugared.Errorf("xormEngine.Rows Error: %v", err)
		return nil, err
	}
	return dbConfigs, nil
}

func GetAllBusinessConfigs() ([]DBConfig, error) {
	dbConfigs := make([]DBConfig, 0)
	err := xormEngine.Where("`deleted`=0").Asc("`modify_at`").Find(&dbConfigs)
	if err != nil {
		sugared.Errorf("GetAllBusinessConfigs xormEngine Error: %v", err)
		return nil, err
	}
	return dbConfigs, nil
}
