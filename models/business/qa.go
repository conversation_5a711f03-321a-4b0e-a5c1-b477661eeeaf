package business

import (
	"context"
	"encoding/json"
	"sync"
	"time"
)

type QAConfig struct {
	BundleId   string `json:"bundleId"`
	DistinctId string `json:"distinctId"`
	ApiVersion string `json:"apiVersion"`
	AdwayType  string `json:"adwayType"`
	Adwaynum   string `json:"adwayNum"`
	State      bool   `json:"state"`
}
type Qa struct {
	configs map[string]map[string]map[string]map[string]QAConfig
	mux     sync.RWMutex
}

const (
	QaConfigsCacheKey    = "hs:datatester:business:qa"
	QaConfigsCacheExpire = time.Hour * 24 * 3
)

func NewAq() *Qa {
	qa := Qa{
		configs: make(map[string]map[string]map[string]map[string]QAConfig),
	}
	qa.getFromCache()
	return &qa
}

func (qa *Qa) Add(aqConfig QAConfig) {
	qa.mux.Lock()
	defer qa.mux.Unlock()
	if _, ok := qa.configs[aqConfig.BundleId]; !ok {
		qa.configs[aqConfig.BundleId] = make(map[string]map[string]map[string]QAConfig)
	}
	if _, ok := qa.configs[aqConfig.BundleId][aqConfig.ApiVersion]; !ok {
		qa.configs[aqConfig.BundleId][aqConfig.ApiVersion] = make(map[string]map[string]QAConfig)
	}
	if _, ok := qa.configs[aqConfig.BundleId][aqConfig.ApiVersion][aqConfig.AdwayType]; !ok {
		qa.configs[aqConfig.BundleId][aqConfig.ApiVersion][aqConfig.AdwayType] = make(map[string]QAConfig)
	}
	changed := false

	if !aqConfig.State {
		if _, ok := qa.configs[aqConfig.BundleId][aqConfig.DistinctId]; ok {
			sugared.Infof("delete bundle:%s, distinctId:%s", aqConfig.BundleId, aqConfig.DistinctId)
			delete(qa.configs[aqConfig.BundleId], aqConfig.DistinctId)
			changed = true
		}
	} else {
		qa.configs[aqConfig.BundleId][aqConfig.ApiVersion][aqConfig.AdwayType][aqConfig.DistinctId] = aqConfig
		sugared.Infof("add qa:%+v ", aqConfig)
		changed = true
		sugared.Infof("add qa change: %t", changed)
	}
	sugared.Infof("add save %t,qa.configs: %+v", changed, qa.configs)
	if changed {
		sugared.Infof("save qa cache")
		qa.saveCache()
	}
}

func (qa *Qa) Get(bundleId string, distinctId, apiVersion, adwayType string) (QAConfig, bool) {
	qa.mux.RLock()
	defer qa.mux.RUnlock()
	if config, ok := qa.configs[bundleId][apiVersion][adwayType][distinctId]; ok {
		if config.State {
			return config, true
		}
	}
	return QAConfig{}, false
}

func (qa *Qa) saveCache() {
	jsonBody, err := json.Marshal(qa.configs)
	if err != nil {
		sugared.Errorf("json marshal err: %v", err)
		return
	}
	sugared.Infof("json qa:%s", string(jsonBody))
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	err = redisClient.Set(ctx, QaConfigsCacheKey, string(jsonBody), QaConfigsCacheExpire).Err()
	if err != nil {
		sugared.Errorf("redis set err: %v", err)
	}
}

func (qa *Qa) getFromCache() {
	qa.mux.Lock()
	defer qa.mux.Unlock()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	jsonBody, err := redisClient.Get(ctx, QaConfigsCacheKey).Bytes()
	if err != nil {
		sugared.Errorf("redis get err: %v", err)
		return
	}
	configs := make(map[string]map[string]map[string]map[string]QAConfig)
	err = json.Unmarshal(jsonBody, &configs)
	if err != nil {
		sugared.Errorf("json unmarshal err: %v", err)
		return
	}
	redisClient.Expire(ctx, QaConfigsCacheKey, QaConfigsCacheExpire)
	qa.configs = configs
}
