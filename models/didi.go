package models

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// DidiDistributeProvider 滴滴分流器
type DidiDistributeProvider struct {
	config DidiConfig
	client *http.Client
}

// DidiConfig 滴滴分流配置
type DidiConfig struct {
	Api     string        `yaml:"api" json:"api"`
	Timeout time.Duration `yaml:"timeout" json:"timeout"`
}

// DidiRequest 滴滴请求接口数据结构
type DidiRequest struct {
	Scene     string `json:"scene"`
	Group     string `json:"group"`
	UserId    string `json:"user_id"`
	BucketCnt int    `json:"bucket_cnt"`
	Platform  string `json:"platform"`
}

// DidiResponse 滴滴接口返回数据结构
type DidiResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Bucket int `json:"bucket"`
	}
}

// NewDidiDistributeProvider 新建滴滴分流器
func NewDidiDistributeProvider(didiConfig DidiConfig) *DidiDistributeProvider {
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
		Timeout: didiConfig.Timeout * time.Millisecond,
	}
	return &DidiDistributeProvider{
		config: didiConfig,
		client: client,
	}
}

// NextScheme 获取分流结果
func (dd *DidiDistributeProvider) NextScheme(scene, group, platform, distinctId string, bucketCnt int) (int, error) {
	request := DidiRequest{
		Scene:     scene,
		Group:     group,
		UserId:    distinctId,
		Platform:  platform,
		BucketCnt: bucketCnt,
	}
	body, err := json.Marshal(request)
	if err != nil {
		sugared.Errorf("failed to marshal request: %v", err)
		return -1, err
	}
	response, err := dd.client.Post(dd.config.Api, "application/json", bytes.NewBuffer(body))
	if err != nil {
		sugared.Errorf("failed to send request: %v", err)
		return -1, err
	}
	defer response.Body.Close()
	if response.StatusCode != 200 {
		sugared.Errorf("didi distribute provider did not return 200")
		return -1, fmt.Errorf("didi distribute provider did not return 200")
	}
	didiResponse := DidiResponse{}
	err = json.NewDecoder(response.Body).Decode(&didiResponse)
	if err != nil {
		sugared.Errorf("didi distribute provider did not decode response: %v", err)
		return -1, err
	}
	if didiResponse.Code != 0 {
		sugared.Errorf("didi distribute provider return %d, message: %s", didiResponse.Code, didiResponse.Message)
		return -1, fmt.Errorf("didi distribute provider return %d, message: %s", didiResponse.Code, didiResponse.Message)
	}
	return didiResponse.Data.Bucket, nil
}

func (dd *DidiDistributeProvider) resetPlatform(pf string) string {
	if pf == "android" {
		return "gp"
	}
	return pf
}
