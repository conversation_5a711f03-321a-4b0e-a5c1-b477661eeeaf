package defend

import "encoding/json"

type DBDefendConfig struct {
	Id       int    `xorm:"'id' not null pk autoincr INT"`
	BundleId string `xorm:"'bundle_id' not null comment('包名') unique(bundle_id_version) VARCHAR(64)"`
	Version  string `xorm:"'version' not null comment('版本') unique(bundle_id_version) VARCHAR(16)"`
	Config   string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	State    int    `xorm:"'state' not null comment('状态1 开启 0 关闭') INT"`
}

func (m *DBDefendConfig) TableName() string {
	return "defend_config"
}

func (m *DBDefendConfig) FindConfigs() ([]DBDefendConfig, error) {
	configs := make([]DBDefendConfig, 0)
	err := xormEngine.Where("`state` = ?", 1).Desc("`id`").Find(&configs)
	if err != nil {
		return nil, err
	}
	return configs, nil
}

func (m *DBDefendConfig) ParseConfig(configData string) (map[string]any, error) {
	configJson := make(map[string]any)
	err := json.Unmarshal([]byte(configData), &configJson)
	return configJson, err
}
