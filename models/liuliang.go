package models

import (
	"context"
	"encoding/json"
	"fmt"
	"hungrystudio.com/datatester/dbconfigs"
	"hungrystudio.com/datatester/models/adconfigs"
	"hungrystudio.com/datatester/models/distributes"
	"strconv"
	"strings"
	"sync"
	"time"
)

type LiuLiang interface {
	loadConfigs()
}

// BaseLL 基础流量配置
type BaseLL struct {
	bundleConfig *BundleConfig
	mux          sync.Mutex
}

// updateConfigs 更新配置
func (bllc *BaseLL) updateConfigs(ctx context.Context, ll LiuLiang) {
	ticker := time.NewTicker(bllc.bundleConfig.UpdateInterval * time.Minute)
	for {
		select {
		case <-ctx.Done():
			sugared.Infof("Over UpdateConfig")
			return
		case <-ticker.C:
			sugared.Infof("UpdateConfig, bundleId: %s", bllc.bundleConfig.BundleId)
			ll.loadConfigs()
		}
	}
}

func NewBaseLL(bundleConfig *BundleConfig) *BaseLL {
	return &BaseLL{
		bundleConfig: bundleConfig,
		mux:          sync.Mutex{},
	}
}

// LLConfig 流量配置
type LLConfig struct {
	AdWayNum   string
	ApiVersion string
	Rate       float64
	Type       int
	Config     adconfigs.AdConfigs
}

// LLConfigs 主流量配置 基于apiVersion adWayNum
type LLConfigs struct {
	*BaseLL
	configs map[string]map[string]map[string]LLConfig
}

// NewLLConfigs 创建主流量配置
func NewLLConfigs(ctx context.Context, baseLL *BaseLL) *LLConfigs {
	c := &LLConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]map[string]map[string]LLConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 主流配置重写
func (llcs *LLConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]map[string]map[string]LLConfig)

	//获取最新数据
	if llConfigs := dbconfigs.FindLiuLiangConfigs(llcs.bundleConfig.TableId); llConfigs != nil {
		sugared.Infof("FindLiuLiangConfigs %s - %s - %s", llcs.bundleConfig.BundleId, llcs.bundleConfig.TableId, llcs.bundleConfig.MinApiVersion)

		for _, llDBConfig := range llConfigs {

			//方案类型：默认新增，0活跃，1新增
			shuntType := "new"
			//暂不区分新增活跃
			//if llDBConfig.Type == 0 {
			//	shuntType = "dynamic"
			//}

			if _, ok := configsRs[shuntType]; !ok {
				configsRs[shuntType] = make(map[string]map[string]LLConfig)
			}
			if _, ok := configsRs[shuntType][llDBConfig.Version]; !ok {
				configsRs[shuntType][llDBConfig.Version] = make(map[string]LLConfig)
			}

			//获取比例
			rate, err := strconv.ParseFloat(llDBConfig.Rate, 10)
			if err != nil {
				rate = 0
			}

			//解析配置
			adConfigs, err := adconfigs.ParseAdConfigs(llDBConfig.Config)
			if err == nil {
				for k, vAdConfig := range llcs.bundleConfig.AdConfigs {
					if vv, ok := adConfigs.FilterAdConfigs[k]; ok {
						vv.Keys = vAdConfig.Keys
						vv.Filters = vAdConfig.Filters
						adConfigs.FilterAdConfigs[k] = vv
					}
				}
			}
			//组装config配置
			adWayNumStr := strconv.Itoa(llDBConfig.Adwaynum)
			llConfig := LLConfig{
				AdWayNum:   adWayNumStr,
				ApiVersion: llDBConfig.Version,
				Rate:       rate,
				Type:       llDBConfig.Type,
				Config:     adConfigs,
			}
			configsRs[shuntType][llDBConfig.Version][adWayNumStr] = llConfig
		}
	}

	//重置
	llcs.mux.Lock()
	llcs.configs = configsRs
	llcs.mux.Unlock()
}

// GetAllAdWayNum 获取所有指定apiVersion的所有配置
func (llcs *LLConfigs) GetAllAdWayNum(shuntType string, apiVersion string) map[string]LLConfig {
	if adWayNums, ok := llcs.configs[shuntType][apiVersion]; ok {
		return adWayNums
	}
	return nil
}

// GetAdConfigs 获取AdConfigs
func (llcs *LLConfigs) GetAdConfigs(shuntType string, apiVersion string, adWayNum string) (adconfigs.AdConfigs, error) {
	if llc, ok := llcs.configs[shuntType][apiVersion][adWayNum]; ok {
		return llc.Config, nil
	}
	return adconfigs.AdConfigs{}, fmt.Errorf("no AdConfig by apiVersion: %s,shuntType: %s,adWayNum: %s", apiVersion, shuntType, adWayNum)
}

// AdwayNumConfigs 方案对应配置，从主配置表获取，当version为空时
type AdwayNumConfigs struct {
	*BaseLL
	configs map[string]LLConfig
}

// NewAdwayNumConfigs 创建AdwayNumConfigs
func NewAdwayNumConfigs(ctx context.Context, baseLL *BaseLL) *AdwayNumConfigs {
	c := &AdwayNumConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]LLConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入version为空的adwaynum对应的配置
func (awConfigs *AdwayNumConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]LLConfig)

	//获取最新数据
	if llConfigs := dbconfigs.FindLiuLiangNoVersionConfigs(awConfigs.bundleConfig.TableId); llConfigs != nil {
		sugared.Infof("FindLiuLiangNoVersionConfigs %s - %s - %s", awConfigs.bundleConfig.BundleId, awConfigs.bundleConfig.TableId, awConfigs.bundleConfig.MinApiVersion)
		for _, llDBConfig := range llConfigs {
			rate, err := strconv.ParseFloat(llDBConfig.Rate, 10)
			if err != nil {
				rate = 0
			}
			adConfigs, err := adconfigs.ParseAdConfigs(llDBConfig.Config)
			if err == nil {
				adWayNumStr := strconv.Itoa(llDBConfig.Adwaynum)
				llConfig := LLConfig{
					AdWayNum:   adWayNumStr,
					ApiVersion: llDBConfig.Version,
					Rate:       rate,
					Type:       llDBConfig.Type,
					Config:     adConfigs,
				}
				configsRs[adWayNumStr] = llConfig
			}
		}
	}

	//重置
	awConfigs.mux.Lock()
	awConfigs.configs = configsRs
	awConfigs.mux.Unlock()
}

// LLDidiConfig Didi分流表配置
type LLDidiConfig struct {
	ApiVersion    string
	Schemes       []string
	Rate          int
	DefaultScheme string
	Scene         string
	Group         string
	Platform      string
}

type DidiConfigs struct {
	*BaseLL
	configs map[string]LLDidiConfig
}

func NewDidiConfigs(ctx context.Context, baseLL *BaseLL) *DidiConfigs {
	c := &DidiConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]LLDidiConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 拉取滴滴配置
func (didi *DidiConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]LLDidiConfig)

	//获取最新数据
	if didiConfigs := dbconfigs.FindLiuLiangDidiConfigs(didi.bundleConfig.TableId); didiConfigs != nil {
		sugared.Infof("FindLiuLiangDidiConfigs %s - %s - %s", didi.bundleConfig.BundleId, didi.bundleConfig.TableId, didi.bundleConfig.MinApiVersion)
		for _, didiDBConfig := range didiConfigs {
			config := make(map[string]string)
			err := json.Unmarshal([]byte(didiDBConfig.Config), &config)
			if err != nil {
				sugared.Errorf("json.Unmarshal(%s): %v", didiDBConfig.Config, err)
			} else {
				schemes := make([]string, 0)
				for i := 0; i < didiDBConfig.Cnt; i++ {
					iStr := strconv.Itoa(i)
					schemes = append(schemes, config[iStr])
				}
				defaultScheme := config["-1"]
				didiConfig := LLDidiConfig{
					ApiVersion:    didiDBConfig.Apiversion,
					Schemes:       schemes,
					DefaultScheme: defaultScheme,
					Rate:          didiDBConfig.Rate,
					Scene:         didiDBConfig.Scene,
					Group:         didiDBConfig.GroupStr,
					Platform:      didiDBConfig.Platform,
				}
				configsRs[didiConfig.ApiVersion] = didiConfig
			}
		}
	}

	//重置
	didi.mux.Lock()
	didi.configs = configsRs
	didi.mux.Unlock()
}

// getDidiConfig 获取滴滴配置
func (didi *DidiConfigs) getDidiConfig(apiVersion string) (LLDidiConfig, bool) {
	if didiConfig, ok := didi.configs[apiVersion]; ok {
		return didiConfig, true
	}
	return LLDidiConfig{}, false
}

// LLDjhConfig 多聚合配置
type LLDjhConfig struct {
	AdWayNum string
	Rate     float64
	Mark     string
	Config   adconfigs.AdConfigs
}

type DjhConfigs struct {
	*BaseLL
	configs map[string]map[string]LLDjhConfig
}

func NewDjhConfigs(ctx context.Context, baseLL *BaseLL) *DjhConfigs {
	c := &DjhConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]map[string]LLDjhConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入多聚合配置
func (djh *DjhConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]map[string]LLDjhConfig)

	//获取最新数据
	if llDjhConfigs := dbconfigs.FindDjhConfigs(djh.bundleConfig.TableId); llDjhConfigs != nil {
		sugared.Infof("FindDjhConfigs %s - %s - %s", djh.bundleConfig.BundleId, djh.bundleConfig.TableId, djh.bundleConfig.MinApiVersion)
		for _, llDBConfig := range llDjhConfigs {
			adWayNumStr := strconv.Itoa(llDBConfig.Adwaynum)
			if _, ok := configsRs[adWayNumStr]; !ok {
				configsRs[adWayNumStr] = make(map[string]LLDjhConfig)
			}
			rate, err := strconv.ParseFloat(llDBConfig.Rate, 10)
			if err != nil {
				rate = 0
			}
			adConfigs, err := adconfigs.ParseAdConfigs(llDBConfig.Config)
			if err == nil {
				for k, cAdConfigs := range djh.bundleConfig.AdConfigs {
					if vv, ok := adConfigs.FilterAdConfigs[k]; ok {
						vv.Keys = cAdConfigs.Keys
						vv.Filters = cAdConfigs.Filters
						adConfigs.FilterAdConfigs[k] = vv
					}
				}
				llDjhConfig := LLDjhConfig{
					AdWayNum: adWayNumStr,
					Mark:     llDBConfig.Mark,
					Rate:     rate,
					Config:   adConfigs,
				}

				configsRs[adWayNumStr][llDBConfig.Mark] = llDjhConfig
			}
		}
	}

	//重置
	djh.mux.Lock()
	djh.configs = configsRs
	djh.mux.Unlock()
}

// DouDiConfigs 指定apiVersion的兜底配置
type DouDiConfigs struct {
	*BaseLL
	configs map[string]adconfigs.AdConfigs
}

// NewDouDiConfigs 新建兜底配置
func NewDouDiConfigs(ctx context.Context, baseLL *BaseLL) *DouDiConfigs {
	c := &DouDiConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]adconfigs.AdConfigs),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入兜底配置
func (dd *DouDiConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]adconfigs.AdConfigs)

	//获取最新数据
	if llDouDiConfigs := dbconfigs.FindDoudiConfigs(dd.bundleConfig.TableId); llDouDiConfigs != nil {
		sugared.Infof("FindDoudiConfigs %s - %s - %s", dd.bundleConfig.BundleId, dd.bundleConfig.TableId, dd.bundleConfig.MinApiVersion)
		for _, llDBConfig := range llDouDiConfigs {
			adConfigs, err := adconfigs.ParseAdConfigs(llDBConfig.Config)
			if err == nil {
				for k, cAdConfig := range dd.bundleConfig.AdConfigs {
					if vv, ok := adConfigs.FilterAdConfigs[k]; ok {
						vv.Keys = cAdConfig.Keys
						vv.Filters = cAdConfig.Filters
						adConfigs.FilterAdConfigs[k] = vv
					}
				}
				configsRs[llDBConfig.Apiversion] = adConfigs
			}
		}
	}

	//重置
	dd.mux.Lock()
	dd.configs = configsRs
	dd.mux.Unlock()
}

// getDouDi 获取兜底配置
func (dd *DouDiConfigs) getDouDi(apiVersion string) (adconfigs.AdConfigs, error) {
	if v, ok := dd.configs[apiVersion]; ok {
		return v, nil
	}
	return adconfigs.AdConfigs{}, fmt.Errorf("no AdConfigs by ApiVersion %s", apiVersion)
}

// GuoShenConfigs 过审配置
type GuoShenConfigs struct {
	*BaseLL
	configs map[string]int
}

// NewGuoShenConfigs 新建过审配置
func NewGuoShenConfigs(ctx context.Context, baseLL *BaseLL) *GuoShenConfigs {
	c := &GuoShenConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]int),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入过审配置
func (gs *GuoShenConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]int)

	//获取最新配置
	if llDBConfigs := dbconfigs.FindGuoShenConfigs(gs.bundleConfig.TableId, gs.bundleConfig.BundleId); llDBConfigs != nil {
		sugared.Infof("FindGuoShenConfigs %s - %s - %s", gs.bundleConfig.BundleId, gs.bundleConfig.TableId, gs.bundleConfig.MinApiVersion)
		for _, llDBConfig := range llDBConfigs {
			configsRs[llDBConfig.Version] = llDBConfig.Type
		}
	}

	//重置
	gs.mux.Lock()
	gs.configs = configsRs
	gs.mux.Unlock()
}

// checkGuoShen 检查是否审核状态
func (gs *GuoShenConfigs) checkGuoShen(appVersion string) bool {
	if v, ok := gs.configs[appVersion]; ok {
		if v > 0 {
			return true
		}
	}
	return false
}

// getGuoShenType 获取过审类型，查询不到返回-1
func (gs *GuoShenConfigs) getGuoShenType(appVersion string) int {
	if v, ok := gs.configs[appVersion]; ok {
		return v
	}
	return -1
}

// NoNormalConfigs 低端机配置
type NoNormalConfigs struct {
	*BaseLL
	configs map[string]adconfigs.AdConfigs
}

// NewNoNormalConfigs 新建低端机配置
func NewNoNormalConfigs(ctx context.Context, baseLL *BaseLL) *NoNormalConfigs {
	c := &NoNormalConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]adconfigs.AdConfigs),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入低端机配置
func (nnc *NoNormalConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]adconfigs.AdConfigs)

	//获取最新配置
	if llNoNormalConfigs := dbconfigs.FindNoNormalConfigs(nnc.bundleConfig.TableId); llNoNormalConfigs != nil {
		sugared.Infof("FindNoNormalConfigs %s - %s - %s", nnc.bundleConfig.BundleId, nnc.bundleConfig.TableId, nnc.bundleConfig.MinApiVersion)
		for _, llDBConfig := range llNoNormalConfigs {
			adConfigs, err := adconfigs.ParseAdConfigs(llDBConfig.Config)
			if err == nil {
				for k, cAdConfig := range nnc.bundleConfig.AdConfigs {
					if vv, ok := adConfigs.FilterAdConfigs[k]; ok {
						vv.Keys = cAdConfig.Keys
						vv.Filters = cAdConfig.Filters
						adConfigs.FilterAdConfigs[k] = vv
					}
				}
			}
			configsRs[llDBConfig.Opt] = adConfigs
		}
	}

	//重置
	nnc.configs = configsRs
}

// GetNoNormalAdConfigs 获取低端机配置
func (nnc *NoNormalConfigs) GetNoNormalAdConfigs(opt string) (adconfigs.AdConfigs, error) {
	if adConfigs, ok := nnc.configs[opt]; ok {
		return adConfigs, nil
	}
	return adconfigs.AdConfigs{}, fmt.Errorf("no AdConfigs by Opt %s", opt)
}

type QAConfigs struct {
	*BaseLL
	configs map[string]string
}

func NewQAConfigs(ctx context.Context, baseLL *BaseLL) *QAConfigs {
	c := &QAConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]string),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadQaConfigs 载入QA配置，以thinkuid为key
func (qa *QAConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]string)

	//获取最新配置
	if qaConfigs := dbconfigs.FindQaConfigs(qa.bundleConfig.TableId); qaConfigs != nil {
		sugared.Infof("FindQaConfigs %s - %s - %s", qa.bundleConfig.BundleId, qa.bundleConfig.TableId, qa.bundleConfig.MinApiVersion)
		for _, qaConfig := range qaConfigs {
			configsRs[qaConfig.Thinkuid] = qaConfig.Adwaynum
		}
	}

	//重置
	qa.mux.Lock()
	qa.configs = configsRs
	qa.mux.Unlock()
}

// resetQaAdWayNum 检查用户是否qa用户，是qa用户则重置adwaynum
func (qa *QAConfigs) resetQaAdWayNum(adWayNum *string, thinkUid string) {
	if aNum, ok := qa.configs[thinkUid]; ok {
		*adWayNum = aNum
	}
}

type CsrQAConfig struct {
	ApiVersion string
	Adwaynum   string
}

type CsrQAConfigs struct {
	*BaseLL
	configs map[string]CsrQAConfig
}

func NewCsrQAConfigs(ctx context.Context, baseLL *BaseLL) *CsrQAConfigs {
	c := &CsrQAConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]CsrQAConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadCsrQaConfigs 载入QA配置，以thinkuid为key
func (csrQa *CsrQAConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]CsrQAConfig)

	//获取最新配置
	if qaConfigs := dbconfigs.FindCsrQaConfigs(csrQa.bundleConfig.TableId); qaConfigs != nil {
		sugared.Infof("FindCsrQaConfigs %s - %s - %s", csrQa.bundleConfig.BundleId, csrQa.bundleConfig.TableId, csrQa.bundleConfig.MinApiVersion)
		for _, qaConfig := range qaConfigs {
			llCsrQAConf := CsrQAConfig{
				ApiVersion: qaConfig.Version,
				Adwaynum:   qaConfig.Adwaynum,
			}
			configsRs[qaConfig.Thinkuid] = llCsrQAConf
		}
	}

	//重置
	csrQa.mux.Lock()
	csrQa.configs = configsRs
	csrQa.mux.Unlock()
}

// resetCsrQaAdWayNum 检查用户是否qa用户，是qa用户则重置adwaynum、ApiVersion
func (csrQa *CsrQAConfigs) resetCsrQaAdWayNum(adWayNum *string, ApiVersion *string, thinkUid string, deviceId string) {
	if csrQaConf, ok := csrQa.configs[thinkUid]; ok {
		*adWayNum = csrQaConf.Adwaynum
		*ApiVersion = csrQaConf.ApiVersion
	} else if csrQaConf, ok := csrQa.configs[deviceId]; ok {
		*adWayNum = csrQaConf.Adwaynum
		*ApiVersion = csrQaConf.ApiVersion
	}
}

// SpecialConfigs 指定用户的配置
type SpecialConfigs struct {
	*BaseLL
	configs map[string]adconfigs.AdConfigs
}

// NewSpecialConfigs 新建指定用户配置
func NewSpecialConfigs(ctx context.Context, baseLL *BaseLL) *SpecialConfigs {
	c := &SpecialConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]adconfigs.AdConfigs),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入Special配置 以thinkuid为key
func (sc *SpecialConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]adconfigs.AdConfigs)

	//获取最新配置
	if specialConfigs := dbconfigs.FindSpecialConfigs(sc.bundleConfig.TableId); specialConfigs != nil {
		sugared.Infof("FindSpecialConfigs %s - %s - %s", sc.bundleConfig.BundleId, sc.bundleConfig.TableId, sc.bundleConfig.MinApiVersion)
		for _, specialConfig := range specialConfigs {
			adConfigs, err := adconfigs.ParseAdConfigs(specialConfig.Config)
			if err == nil {
				for k, cAdConfig := range sc.bundleConfig.AdConfigs {
					if vv, ok := adConfigs.FilterAdConfigs[k]; ok {
						vv.Keys = cAdConfig.Keys
						vv.Filters = cAdConfig.Filters
						adConfigs.FilterAdConfigs[k] = vv
					}
				}
			}
			configsRs[specialConfig.Thinkuid] = adConfigs
		}
	}

	//重置
	sc.configs = configsRs
}

// getConfig 获取用户指定配置
func (sc *SpecialConfigs) getConfig(thinkUid string) (adconfigs.AdConfigs, error) {
	if adConfigs, ok := sc.configs[thinkUid]; ok {
		return adConfigs, nil
	}
	return adconfigs.AdConfigs{}, fmt.Errorf("no AdConfigs by Thinkuid %s", thinkUid)
}

// TesterConfigs 测试配置
type TesterConfigs struct {
	*BaseLL
	configs map[string]int
}

// NewTesterConfigs 新建测试配置
func NewTesterConfigs(ctx context.Context, baseLL *BaseLL) *TesterConfigs {
	c := &TesterConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]int),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入Tester配置
func (tc *TesterConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]int)

	//获取最新配置
	if testerConfigs := dbconfigs.FindTesterConfigs(tc.bundleConfig.TableId); testerConfigs != nil {
		sugared.Infof("FindTesterConfigs %s - %s - %s", tc.bundleConfig.BundleId, tc.bundleConfig.TableId, tc.bundleConfig.MinApiVersion)
		for _, testerConfig := range testerConfigs {
			configsRs[testerConfig.Version] = testerConfig.Type
		}
	}

	//重置
	tc.mux.Lock()
	tc.configs = configsRs
	tc.mux.Unlock()
}

// checkTester 判断Tester, apiVersion
func (tc *TesterConfigs) checkTester(apiVersion string) bool {
	if v, ok := tc.configs[apiVersion]; ok {
		if v == 1 {
			return true
		}
	}
	return false
}

// SwitchConfig 开关配置类型
type SwitchConfig map[string]any

// SwitchConfigs 所有开关配置
type SwitchConfigs struct {
	*BaseLL
	configs map[string]SwitchConfig
}

// NewSwitchConfigs 创建所有开关配置
func NewSwitchConfigs(ctx context.Context, baseLL *BaseLL) *SwitchConfigs {
	c := &SwitchConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]SwitchConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入所有开关配置
func (sc *SwitchConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]SwitchConfig)

	//获取最新配置
	if swConfigs := dbconfigs.FindSwitchConfigs(sc.bundleConfig.TableId); swConfigs != nil {
		sugared.Infof("FindSwitchConfigs %s - %s - %s", sc.bundleConfig.BundleId, sc.bundleConfig.TableId, sc.bundleConfig.MinApiVersion)
		for _, swConfig := range swConfigs {
			sConfig := make(SwitchConfig)
			err := json.Unmarshal([]byte(swConfig.Config), &sConfig)
			if err != nil {
				sugared.Errorf("json.Unmarshal(%s): %v", swConfig.Config, err)
			} else {
				configsRs[swConfig.Apiversion] = sConfig
			}
		}
	}

	//重置
	sc.configs = configsRs
}

// getSwitch 获取switch配置
func (sc *SwitchConfigs) getSwitch(apiVersion string) SwitchConfig {
	if v, ok := sc.configs[apiVersion]; ok {
		return v
	}
	//默认配置
	if v, ok := sc.configs["v0"]; ok {
		return v
	}
	return nil
}

// LLTianChongConfig 不同版本的兜底策略
type LLTianChongConfig struct {
	ApiVersion string
	TCNum      string
	Rate       float64
	Config     map[string]any
}

// TianChongConfigs 填充配置
type TianChongConfigs struct {
	*BaseLL
	configs map[string]map[string]LLTianChongConfig
}

func NewTianChongConfigs(ctx context.Context, baseLL *BaseLL) *TianChongConfigs {
	c := &TianChongConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]map[string]LLTianChongConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入TianChong配置
func (tc *TianChongConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]map[string]LLTianChongConfig)

	//获取最新配置
	if tianChongConfigs := dbconfigs.FindTianChongConfigs(tc.bundleConfig.TableId); tianChongConfigs != nil {
		sugared.Infof("FindTianChongConfigs %s - %s - %s", tc.bundleConfig.BundleId, tc.bundleConfig.TableId, tc.bundleConfig.MinApiVersion)
		for _, tianChongConfig := range tianChongConfigs {

			if _, ok := configsRs[tianChongConfig.Apiversion]; !ok {
				configsRs[tianChongConfig.Apiversion] = make(map[string]LLTianChongConfig)
			}
			configs := make(map[string]any)

			err := json.Unmarshal([]byte(tianChongConfig.Config), &configs)
			if err != nil {
				sugared.Errorf("json.Unmarshal(%s): %v", tianChongConfig.Config, err)
			} else {
				rate, err := strconv.ParseFloat(tianChongConfig.Rate, 64)
				if err != nil {
					rate = 0
				}
				llt := LLTianChongConfig{
					ApiVersion: tianChongConfig.Apiversion,
					TCNum:      tianChongConfig.Tcnum,
					Config:     configs,
					Rate:       rate,
				}
				configsRs[tianChongConfig.Apiversion][tianChongConfig.Tcnum] = llt
			}
		}
	}

	//重置
	tc.mux.Lock()
	tc.configs = configsRs
	tc.mux.Unlock()
}

// ChunJingConfigs 纯净版配置
type ChunJingConfigs struct {
	*BaseLL
	configs map[string]adconfigs.AdConfigs
}

// NewChunJingConfigs 新建纯净配置
func NewChunJingConfigs(ctx context.Context, baseLL *BaseLL) *ChunJingConfigs {
	c := &ChunJingConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]adconfigs.AdConfigs),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入纯净配置
func (cjc *ChunJingConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]adconfigs.AdConfigs)

	//获取最新配置
	if chunJingConfigs := dbconfigs.FindChunJingConfigs(cjc.bundleConfig.TableId); chunJingConfigs != nil {
		sugared.Infof("FindChunJingConfigs %s - %s - %s", cjc.bundleConfig.BundleId, cjc.bundleConfig.TableId, cjc.bundleConfig.MinApiVersion)
		for _, chunJingConfig := range chunJingConfigs {
			adConfigs, err := adconfigs.ParseAdConfigs(chunJingConfig.Config)
			if err == nil {
				for k, cAdConfig := range cjc.bundleConfig.AdConfigs {
					if vv, ok := adConfigs.FilterAdConfigs[k]; ok {
						vv.Keys = cAdConfig.Keys
						vv.Filters = cAdConfig.Filters
						adConfigs.FilterAdConfigs[k] = vv
					}
				}
			}
			configsRs[chunJingConfig.Apiversion] = adConfigs
		}
	}

	//重置
	cjc.mux.Lock()
	cjc.configs = configsRs
	cjc.mux.Unlock()
}

// getAdConfigs 获取纯净版配置
func (cjc *ChunJingConfigs) getAdConfigs(apiVersion string) (adconfigs.AdConfigs, error) {
	//获取指定的纯净桶配置
	if adConfigs, ok := cjc.configs[apiVersion]; ok {
		return adConfigs, nil
	}
	//获取默认的纯净桶配置
	if adConfigs, ok := cjc.configs["v0"]; ok {
		return adConfigs, nil
	}
	return adconfigs.AdConfigs{}, fmt.Errorf("no AdConfigs found for apiVersion: %s", apiVersion)
}

// AbTesterConfigs 有效的abTester实验变量配置
type AbTesterConfigs struct {
	*BaseLL
	configs map[string][]string
}

// NewAbTesterConfigs 新建有效的abTester实验变量配置
func NewAbTesterConfigs(ctx context.Context, baseLL *BaseLL) *AbTesterConfigs {
	c := &AbTesterConfigs{
		BaseLL:  baseLL,
		configs: make(map[string][]string),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入变量配置
func (abTester *AbTesterConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string][]string)

	//获取最新配置
	if abTeserConfigs := dbconfigs.FindAbTesterConfigs(abTester.bundleConfig.TableId); abTeserConfigs != nil {
		sugared.Infof("FindAbTesterConfigs %s - %s - %s", abTester.bundleConfig.BundleId, abTester.bundleConfig.TableId, abTester.bundleConfig.MinApiVersion)
		for _, abTeserConfig := range abTeserConfigs {
			experimentVars := make([]string, 0)
			err := json.Unmarshal([]byte(abTeserConfig.Config), &experimentVars)
			if err != nil {
				sugared.Errorf("json.Unmarshal(%s): %v", abTeserConfig.Config, err)
			} else {
				configsRs[abTeserConfig.Apiversion] = experimentVars
			}
		}
	}

	//重置
	abTester.mux.Lock()
	abTester.configs = configsRs
	abTester.mux.Unlock()
}

// getExperimentVars 获取实验变量
func (abTester *AbTesterConfigs) getExperimentVars(apiVersion string) []string {
	if experimentVars, ok := abTester.configs[apiVersion]; ok {
		return experimentVars
	}
	return nil
}

// ProductSchemeConfig 研发方案配置
type ProductSchemeConfig struct {
	Scheme    []distributes.Scheme
	ShuntType string
	Config    map[string]interface{}
}

// ProductPiciSchemeConfig 研发方案配置
type ProductPiciSchemeConfig struct {
	*BaseLL
	configs map[string]ProductSchemeConfig
	mux     sync.Mutex
}

// NewProductPiciSchemeConfigs 新建有效的研发方案配置
func NewProductPiciSchemeConfigs(ctx context.Context, baseLL *BaseLL) *ProductPiciSchemeConfig {
	c := &ProductPiciSchemeConfig{
		BaseLL:  baseLL,
		configs: make(map[string]ProductSchemeConfig),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入研发方案配置
func (fi *ProductPiciSchemeConfig) loadConfigs() {

	//初始化
	configsRs := make(map[string]ProductSchemeConfig)

	//获取最新配置
	if productSchemeConfigs := dbconfigs.ProductPiciSchemeConfConfigs(fi.bundleConfig.BundleId); productSchemeConfigs != nil {
		sugared.Infof("ProductPiciSchemeConfigs %s ", fi.bundleConfig.BundleId)
		for _, productSchemeConf := range productSchemeConfigs {

			// 使用 strings.Split 将字符串拆分成数组
			schemeArray := strings.Split(productSchemeConf.Scheme, ",")

			// 初始化 Scheme 结构体切片
			var schemes []distributes.Scheme

			// 遍历分割后的字符串数组
			for _, plan := range schemeArray {
				scheme := distributes.Scheme{
					Name:   plan,
					Weight: 0,
				}
				schemes = append(schemes, scheme)
			}

			//随机排序
			schemes = distributes.RandomOrder(schemes)
			// 排序 schemes 切片按照 Name 字段
			//sort.Slice(schemes, func(i, j int) bool {
			//	return schemes[i].Name < schemes[j].Name
			//})

			shuntType := "new"
			if productSchemeConf.Type == 1 {
				shuntType = "dynamic"
			}
			configs := make(map[string]any)
			err := json.Unmarshal([]byte(productSchemeConf.ConfInfo), &configs)
			if err != nil {
				sugared.Errorf("json.Unmarshal(%s): %v", productSchemeConf.ConfInfo, err)
			} else {
				llt := ProductSchemeConfig{
					Scheme:    schemes,
					ShuntType: shuntType,
					Config:    configs,
				}
				index := fmt.Sprintf("%s_%s", shuntType, productSchemeConf.Pici)
				configsRs[index] = llt
			}

		}
	}

	fi.mux.Lock()
	fi.configs = configsRs
	fi.mux.Unlock()

}

// getAdConfigs 获取研发方案配置
func (fi *ProductPiciSchemeConfig) getProductPiciSchemeConfigs(piCi string, shuntType string) (ProductSchemeConfig, error) {
	index := fmt.Sprintf("%s_%s", shuntType, piCi)
	if schemeConfig, ok := fi.configs[index]; ok {
		return schemeConfig, nil
	}
	return ProductSchemeConfig{}, fmt.Errorf("no ProductPiciSchemeConfig by piCi %s shuntType %s", piCi, shuntType)
}

// VGatherCondition 版本分流条件
type VGatherCondition struct {
	MinNumber float64
	MaxNumber float64
}

// VersionGatherConfigs 版本分流配置
type VersionGatherConfigs struct {
	*BaseLL
	configs map[string]map[string]VGatherCondition
	mux     sync.Mutex
}

// NewVersionGatherConfigs 新建版本分流配置
func NewVersionGatherConfigs(ctx context.Context, baseLL *BaseLL) *VersionGatherConfigs {
	c := &VersionGatherConfigs{
		BaseLL:  baseLL,
		configs: make(map[string]map[string]VGatherCondition),
	}
	c.loadConfigs()
	go c.updateConfigs(ctx, c)
	return c
}

// loadConfigs 载入版本分流配置
func (vg *VersionGatherConfigs) loadConfigs() {

	//初始化
	configsRs := make(map[string]map[string]VGatherCondition)

	// 获取最新数据
	if llVersionGatherConfigs := dbconfigs.FindVersionGatherConfigs(vg.bundleConfig.TableId); llVersionGatherConfigs != nil {
		sugared.Infof("FindVersionGatherConfigs %s - %s - %s", vg.bundleConfig.BundleId, vg.bundleConfig.TableId, vg.bundleConfig.MinApiVersion)
		for _, llDBConfig := range llVersionGatherConfigs {
			configs := make(map[string]any)
			err := json.Unmarshal([]byte(llDBConfig.Config), &configs)

			if err != nil {
				sugared.Errorf("Error VersionGatherConfigs  loadConfigs unmarshalling config: %v", err)
				continue
			}

			for key, vGConf := range configs {
				if infoConf, ok := vGConf.(map[string]interface{}); ok {

					condition := VGatherCondition{
						MinNumber: 0,
						MaxNumber: 0,
					}

					//获取分流条件
					for infoKey, number := range infoConf {
						if infoKey == "min_number" {
							condition.MinNumber = number.(float64)
							continue
						}
						if infoKey == "max_number" {
							condition.MaxNumber = number.(float64)
							continue
						}
					}
					//条件设置错误
					if condition.MinNumber == 0 || condition.MaxNumber == 0 {
						continue
					}

					if len(configsRs[llDBConfig.Apiversion]) == 0 {
						configsRs[llDBConfig.Apiversion] = make(map[string]VGatherCondition)
					}
					configsRs[llDBConfig.Apiversion][key] = condition
				}
			}
		}
	}

	//重置
	vg.mux.Lock()
	vg.configs = configsRs
	vg.mux.Unlock()
}

// getVersionGather 获取版本分流配置
func (vg *VersionGatherConfigs) getVersionGather(apiVersion string) (map[string]VGatherCondition, error) {
	if v, ok := vg.configs[apiVersion]; ok {
		return v, nil
	}
	return make(map[string]VGatherCondition), fmt.Errorf("no AdConfigs by ApiVersion %s", apiVersion)
}
