package models

import (
	"context"
	"fmt"
	"time"
)

const (
	IsFirstDay = 1
	NoFirstDay = 0
)

// CheckDayFirst 检查是否当天第一次请求，依据utc时间
func CheckDayFirst(bundleId, thinkUid string) bool {
	utcTime := time.Now().UTC()
	ctx, cancel := RedisTimeoutContext()
	defer cancel()
	dateStr := utcTime.Format("20060102")
	rKey := fmt.Sprintf("%s_%s_isfirst", bundleId, dateStr)
	ok, err := redisClient.SIsMember(ctx, rKey, thinkUid).Result()
	if err != nil {
		go func(utcTime time.Time) {
			sugared.Infof("save isfirt thinkuid: %s to redis", thinkUid)
			redisClient.SAdd(context.Background(), rKey, thinkUid)
			nextDay := utcTime.AddDate(0, 0, 1)
			redisClient.Expire(context.Background(), rKey, nextDay.Sub(utcTime))
		}(utcTime)
		sugared.Errorf("redisClient.SIsMemeber err: %v", err)
		return true
	}
	if ok {
		return false
	} else {
		sugared.Infof("redisClient.SIsMemeber !ok")
		go redisClient.SAdd(context.Background(), rKey, thinkUid)
		return true
	}
}
