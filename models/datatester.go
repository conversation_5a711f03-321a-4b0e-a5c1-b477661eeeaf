package models

import (
	"context"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"github.com/volcengine/datatester-go-sdk/client"
	"github.com/volcengine/datatester-go-sdk/config"
	"github.com/volcengine/datatester-go-sdk/log"
	"hungrystudio.com/datatester/metrics"
	"io"
	"slices"
	"strings"
	"sync"
	"time"
)

const DefaultActivateChanLength = 2000

// AbMetaData 火山ABTester元数据
type AbMetaData struct {
	VariantNameMapsExperimentID map[string]string   `json:"variantNameMapsExperimentID"`
	ExperimentConditionsKey     map[string][]string `json:"experimentConditionsKey"`
}

// AbActivateData 激活数据结构
type AbActivateData struct {
	AppId          string
	DecisionId     string
	Experiment     string
	Vid            string
	UserAttributes map[string]any
	DefaultValue   any
	ApiVersion     string
}

const (
	AbMetaHost  = "https://hs.afafb.com"
	AbTraceHost = "https://hsdata.afafb.com"
)

type AppDataTesterConfig struct {
	AppKey               string   `yaml:"appKey" json:"appKey"`
	MediaSources         []string `yaml:"mediaSources" json:"mediaSources"`
	MappingCountryFields []string `yaml:"mappingCountryFields" json:"mappingCountryFields"`
}

// AppDataTester 火山ABTester对象
type AppDataTester struct {
	config       AppDataTesterConfig
	abClient     *client.AbClient
	abMetaMux    sync.Mutex
	abMetaData   AbMetaData
	activateChan chan AbActivateData
}

// newAbClient 新建火山abClient
func newAbClient(appKey string, abWriter io.Writer) *client.AbClient {
	rusLog := logrus.New()
	rusLog.SetOutput(abWriter)
	abClient := client.NewClient(
		appKey,
		config.WithMetaHost(AbMetaHost),
		config.WithTrackHost(AbTraceHost),
		config.WithAnonymousConfig(true, false),
		config.WithLogger(log.NewLogrusAdapt(rusLog)),
	)
	return abClient
}

// medieaSourceList media_source 合法数据列表
var mediaSourceList = []string{"digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int"}

// 火山ABTester后台 国家条件合法key值
var mappingCountryFields = []string{"max_country_code", "server_hs_ip_country", "hs_ip_country"}

// NewAppDataTester 创建AppDataTester
func NewAppDataTester(ctx context.Context, appDataTesterConfig AppDataTesterConfig, abLogWriter io.Writer) *AppDataTester {
	if appDataTesterConfig.MediaSources == nil {
		appDataTesterConfig.MediaSources = mediaSourceList
	}
	if appDataTesterConfig.MappingCountryFields == nil {
		appDataTesterConfig.MappingCountryFields = mappingCountryFields
	}
	abClient := newAbClient(appDataTesterConfig.AppKey, abLogWriter)
	appDataTester := &AppDataTester{
		config:       appDataTesterConfig,
		abClient:     abClient,
		activateChan: make(chan AbActivateData, DefaultActivateChanLength),
	}
	appDataTester.makeAbMetaData()
	go appDataTester.AutoUpdateMetaData(ctx)
	go appDataTester.Activate(ctx)
	return appDataTester
}

func (appDataTester *AppDataTester) Shutdown(ctx context.Context) error {
	<-ctx.Done()
	//dt.cancelFunc()
	return nil
}

// makeAbMetaData 获取AbMetaData 数据从火山ABTester
func (appDataTester *AppDataTester) makeAbMetaData() {
	appDataTester.abMetaMux.Lock()
	defer appDataTester.abMetaMux.Unlock()
	productConfig := appDataTester.abClient.GetConfig()
	varExpMap := make(map[string]string)
	expConditionsKey := make(map[string][]string)
	for _, exp := range productConfig.ExperimentMap {
		expId := exp.Id
		conditionsKey := make([]string, 0)
		for _, varObj := range exp.VariantMap {
			for variantName, _ := range varObj.Config {
				if strings.Contains(variantName, "_roas") {
					continue
				}
				varExpMap[variantName] = expId
			}
		}
		for _, filter := range exp.Release.Filters {
			// debug 打印条件
			//cJson, _ := json.Marshal(filter.Conditions)
			//sugared.Infof("Experiment: %s, Conditions: %s", expId, cJson)
			for _, condition := range filter.Conditions {
				conditionsKey = append(conditionsKey, condition.Key)
			}
		}
		expConditionsKey[expId] = conditionsKey
	}
	appDataTester.abMetaData = AbMetaData{
		VariantNameMapsExperimentID: varExpMap,
		ExperimentConditionsKey:     expConditionsKey,
	}
}

const UpdateVariantMapsInterval = 10

// AutoUpdateMetaData 定期更新AbMetaData
func (appDataTester *AppDataTester) AutoUpdateMetaData(ctx context.Context) {
	sugared.Infof("Start AutoUpdateMetaData Interval %d Minute", UpdateVariantMapsInterval)
	ticker := time.NewTicker(time.Minute * UpdateVariantMapsInterval)
	for {
		select {
		case <-ctx.Done():
			sugared.Infof("Exit AutoUpdateMetaData")
			return
		case <-ticker.C:
			appDataTester.makeAbMetaData()
		}
	}
}

// Activate 检查激活通道，取数据激活
func (appDataTester *AppDataTester) Activate(ctx context.Context) {
	sugared.Infof("Start AbActivate")
	closed := false
	for {
		select {
		case abActivateData := <-appDataTester.activateChan:
			appDataTester.activate(abActivateData)
		case <-ctx.Done():
			sugared.Infof("AbActivate done")
			closed = true
		}
		if closed {
			if len(appDataTester.activateChan) == 0 {
				sugared.Infof("Exit AbActivate")
				break
			}
		}
	}
}

// activate abTester激活
func (appDataTester *AppDataTester) activate(abActivateData AbActivateData) {
	acResult, err := appDataTester.abClient.Activate(abActivateData.Experiment, abActivateData.DecisionId, abActivateData.DecisionId, nil, abActivateData.UserAttributes)
	if err != nil {
		sugared.Errorf("Activate AppId:%s,Experiment: %s,DecisionId: %s Error: %v", abActivateData.AppId, abActivateData.Experiment, abActivateData.DecisionId, err)
	} else {
		sugared.Infof("Activate AppId:%s,Experiment: %s,DecisionId: %s,Attributes: %v,ActivateResult: %v", abActivateData.AppId, abActivateData.Experiment, abActivateData.DecisionId, abActivateData.UserAttributes, acResult)
		labels := prometheus.Labels{"appid": abActivateData.AppId, "experiment": abActivateData.Experiment, "vid": abActivateData.Vid}
		metrics.AbActivateCount.With(labels).Inc()
	}
}

// Execute 执行ABTester
func (appDataTester *AppDataTester) Execute(validExperiments []string, params map[string]any) (map[string]any, string, bool) {
	bundleId := params["bundleId"].(string)
	decisionId := params["thinkuid"].(string)
	extraApiVersion := params["api_version"].(string)
	userAttributes := params["attributes"].(map[string]any)
	if userAttributes == nil {
		sugared.Errorf("attributes param not exists")
		return nil, "", false
	}
	country := params[ParamNameServerCountryCode].(string)
	labels := prometheus.Labels{"appid": bundleId}
	metrics.AbRequestCount.With(labels).Inc()
	for _, experimentVar := range validExperiments {
		experimentId, ok := appDataTester.abMetaData.VariantNameMapsExperimentID[experimentVar]
		if ok {
			conditions, ok := appDataTester.abMetaData.ExperimentConditionsKey[experimentId]
			if ok {
				attributes := make(map[string]any)
				for _, cValue := range conditions {
					if aValue, ok := userAttributes[cValue]; ok {
						attributes[cValue] = aValue
					}
				}
				sugared.Infof("attributes: %v", attributes)
				// 重置国家过滤条件
				attributes = appDataTester.ResetAttributesCountry(attributes, country)
				sugared.Infof("ExperimentId : %s", experimentId)
				sugared.Infof("experimentVar: %s, attributes: %v", experimentVar, attributes)
				abResult, err := appDataTester.abClient.GetExperimentConfigs(experimentId, decisionId, attributes)
				if err == nil && abResult != nil {
					experimentInfo := appDataTester.filterExperiment(abResult, experimentVar, params)
					if experimentInfo != nil {
						adInfo := experimentInfo["val"].(map[string]any)

						vid := experimentInfo["vid"].(string)
						labels["experiment"] = experimentVar
						labels["vid"] = vid
						metrics.AbHitCount.With(labels).Inc()

						abActiviateData := AbActivateData{
							AppId:          bundleId,
							DecisionId:     decisionId,
							Experiment:     experimentVar,
							Vid:            vid,
							UserAttributes: attributes,
							DefaultValue:   nil,
							ApiVersion:     extraApiVersion,
						}

						go func(abActivateData AbActivateData) {
							appDataTester.activateChan <- abActiviateData
						}(abActiviateData)
						return adInfo, vid, true
					}
				} else {
					sugared.Errorf("ExperimentUnHit Error: %v", err)
				}
			}
		}

	}
	return nil, "", false
}

// filterExperiment 过滤实验，有限命中roas实验
func (appDataTester *AppDataTester) filterExperiment(abResult map[string]map[string]any, experimentVar string, params map[string]any) map[string]any {
	var experimentInfo map[string]any
	// media_source过滤
	if len(abResult) > 1 {
		if mediaSource, ok := params["media_source"].(string); ok && slices.Contains(appDataTester.config.MediaSources, mediaSource) {
			tVar := fmt.Sprintf("roas_%s", experimentVar)
			if tInfo, ok := abResult[tVar]; ok {
				experimentInfo = tInfo
			}
		}
	}
	if experimentInfo == nil {
		experimentInfo = abResult[experimentVar]
	}
	return experimentInfo
}

// ResetAttributesCountry 重置国家代码
func (appDataTester *AppDataTester) ResetAttributesCountry(attributes map[string]any, country string) map[string]any {
	for _, countryId := range appDataTester.config.MappingCountryFields {
		if _, ok := attributes[countryId]; ok {
			attributes[countryId] = country
			break
		}
	}
	return attributes
}
