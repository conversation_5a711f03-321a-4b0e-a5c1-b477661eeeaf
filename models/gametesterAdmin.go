package models

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"slices"
	"strconv"
	"time"

	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/notify"
	"hungrystudio.com/datatester/models/testergame"
	"xorm.io/xorm"
)

type Refactor2Config struct {
	TestingUploadApi string `yaml:"testingUploadApi" json:"testingUploadApi"`
	ProdUploadApi    string `yaml:"prodUploadApi" json:"prodUploadApi"`
}

// GametesterAdminConfig 配置
type GametesterAdminConfig struct {
	Bundles              []string                         `yaml:"bundles" json:"bundles"`
	DDConfigs            map[string][]notify.DDConfig     `yaml:"dd_configs" json:"dd_configs"`
	LocalRedis           string                           `yaml:"localRedis" json:"localRedis"`
	GrayRedis            string                           `yaml:"grayRedis" json:"grayRedis"`
	ProdRedis            string                           `yaml:"prodRedis" json:"prodRedis"`
	ProdMysql            string                           `yaml:"prodMysql" json:"prodMysql"`
	LocalMysql           string                           `yaml:"localMysql" json:"localMysql"`
	Validate             testergame.ValidateConfig        `yaml:"validate" json:"validate"`
	Refactor2            Refactor2Config                  `yaml:"refactor2" json:"refactor2"`
	OverScoreInterceptor *testergame.OverScoreInterceptor `yaml:"overScoreInterceptor" json:"overScoreInterceptor"`
	MultiLinks           map[string][]string              `yaml:"multiLinks" json:"multiLinks"`
	SecretKeys           map[string]map[string]string     `yaml:"secretKeys" json:"secretKeys"` // 服务端key map[env]map[bundle]key
}

// ExperimentState 实验状态
type ExperimentState struct {
	ExperimentID   string `json:"experiment_id"`
	StartTime      int64  `json:"start_time"`
	EndTime        int64  `json:"end_time"`
	State          string `json:"state"`
	ExperimentDays int    `json:"experiment_days"`
	ExperimentType int    `json:"experiment_type"`
	Bucket         int    `json:"bucket"`
	BucektDesc     string `json:"bucekt_desc"`
}

// GametesterAdmin 管理对象
type GametesterAdmin struct {
	bundles     []string
	localRedis  cache.RedisClient
	grayRedis   cache.RedisClient
	prodRedis   cache.RedisClient
	prodMysql   xorm.EngineInterface
	localMysql  xorm.EngineInterface
	adminConfig GametesterAdminConfig
}

const ExperimentsStatePushAPI = "https://admarket.youxi123.com/api/batchplan/save"
const ExperimentsStateCacheKey = "hs:gametester:expstates:%s"

// 提交实验状态类型
const (
	PushExpStateTypeConfig string = "config" // 配置变更，会影响开始时间，结束时间，状态
	PushExpStateTypeFlow   string = "flow"   // 会影响状态，会停量
)

func NewGametesterAdmin(localRedis, grayRedis, prodRedis cache.RedisClient) *GametesterAdmin {
	ga := &GametesterAdmin{
		localRedis: localRedis,
		grayRedis:  grayRedis,
		prodRedis:  prodRedis,
	}
	return ga
}

func (ga *GametesterAdmin) SetProdMysql(mysql xorm.EngineInterface) {
	ga.prodMysql = mysql
}

func (ga *GametesterAdmin) SetLocalMysql(mysql xorm.EngineInterface) {
	ga.localMysql = mysql
}

func (ga *GametesterAdmin) SetAdminConfig(config GametesterAdminConfig) {
	ga.adminConfig = config
}

// SaveBucketConfigs 保存配置
func (ga *GametesterAdmin) SaveBucketConfigs(bucketConfigs []testergame.BucketConfig, bundleId, env string) (err error) {
	sugared.Infof("保存Bundle: %s,Env: %s 配置打印：", bundleId, env)
	testergame.DumpBucketConfigs(bucketConfigs)
	enUploadEnvs := []string{testergame.EnvTesting, testergame.EnvGray}
	if slices.Contains(enUploadEnvs, env) {
		return ga.saveBucketConfigs(bucketConfigs, bundleId, env)
	}
	return fmt.Errorf("ENV:%s不允许上传配置", env)
}

// SaveBucketConfigs 保存配置
func (ga *GametesterAdmin) saveBucketConfigs(bucketConfigs []testergame.BucketConfig, bundleId, env string) (err error) {
	sugared.Infof("保存Bundle: %s,Env: %s 配置打印：", bundleId, env)
	testergame.DumpBucketConfigs(bucketConfigs)
	cacheKey := testergame.GetGameTesterCacheKeyWithBundleAndEnv(bundleId, env)
	cacheData, _ := json.Marshal(bucketConfigs)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	var bRedisClient cache.RedisClient
	switch env {
	case testergame.EnvTesting:
		bRedisClient = ga.localRedis
	case testergame.EnvGray:
		bRedisClient = ga.grayRedis
	case testergame.EnvProd:
		bRedisClient = ga.prodRedis
	default:
		return fmt.Errorf("不支持env: %s 配置文件上传", env)
	}
	err = bRedisClient.Set(ctx, cacheKey, cacheData, testergame.BucketCacheExpiration).Err()
	if err != nil {
		sugared.Error("ENV: %s Save Redis Error: %v", env, err)
		return err
	}
	_ = bRedisClient.Expire(ctx, cacheKey, testergame.BucketCacheExpiration)
	//if testergame.EnvProd == env && bundleId == "com.block.juggle" {
	//	err = ga.UploadToRefactor2(cacheData, env)
	//	if err != nil {
	//		sugared.Error("ENV: %s Save Refactor2 Error: %v", env, err)
	//	}
	//}
	return nil
}

// PushGray 发布灰度配置到线上
func (ga *GametesterAdmin) PushGray(bundleId string) error {
	var grayBucketConfigs []testergame.BucketConfig
	grayBucketConfigs = ga.getBundleBucketConfigs(bundleId, testergame.EnvGray)
	if grayBucketConfigs == nil {
		return fmt.Errorf("没有要发布的灰度环境")
	}
	sugared.Infof("发布到线上，打印包%s灰度配置：", bundleId)
	testergame.DumpBucketConfigs(grayBucketConfigs)

	if err := ga.saveBucketConfigs(grayBucketConfigs, bundleId, testergame.EnvProd); err != nil {
		sugared.Errorf("保存配置到生产环境错误: %v", err)
		return err
	}
	go ga.pushGrayNotifyDD(bundleId, ga.adminConfig.DDConfigs[bundleId])
	go func() {
		newExpStates := ga.getExperimentState(grayBucketConfigs)
		prodBucketConfigs := ga.getBundleBucketConfigs(bundleId, testergame.EnvProd)
		oldStates := ga.getExperimentState(prodBucketConfigs)
		newExpStates = ga.mergeExperimentState(oldStates, newExpStates)
		_ = ga.pushExperimentsState(bundleId, newExpStates, PushExpStateTypeConfig)
	}()
	return nil
}

func (ga *GametesterAdmin) pushGrayNotifyDD(bundleId string, ddConfigs []notify.DDConfig) {
	ddContent := "包:" + bundleId + " 从灰度发布到线上"
	for _, ddConfig := range ddConfigs {
		err := notify.DDNotifyWithConfig(ddConfig, ddContent)
		if err != nil {
			sugared.Errorf("ddConfig: %v 灰度发布到线上失败,%v", ddConfig, err)
		}
	}
}

func (ga *GametesterAdmin) Monitor(ctx context.Context, bundles []string, bundlesDDConfigs map[string][]notify.DDConfig) {
	sugared.Infof("Start GametesterAdmin Monitor")
	ticker := time.NewTicker(time.Minute * 1)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			sugared.Infof("Over GametesterAdmin Monitor")
			return
		case <-ticker.C:
			ga.monitorFlow(bundles, bundlesDDConfigs)
		}
	}
}

func (ga *GametesterAdmin) monitorFlow(bundles []string, bundlesDDConfigs map[string][]notify.DDConfig) {
	bucketsFlow := &testergame.BucketsFlow{}
	for _, bundleId := range bundles {
		if ddConfigs, ok := bundlesDDConfigs[bundleId]; ok {
			sugared.Infof("MonitorBucketMaxFlow: %s", bundleId)
			if bucketConfigs := ga.getBundleBucketConfigs(bundleId, testergame.EnvProd); bucketConfigs != nil {
				expStateUpdate := false
				for _, bucket := range bucketConfigs {
					if bucket.BucketState != testergame.BucketStateOnline {
						continue
					}
					sugared.Infof("BucketID: %d", bucket.BucketId)
					bucketFlow := bucketsFlow.Get(bucket.BundleId, bucket.BucketId)
					if bucket.MaxFlowNum > 0 && bucketFlow > bucket.MaxFlowNum {
						unixTime, err := bucketsFlow.SetMonitorMax(bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, testergame.BucketFlowTypeTotal)
						if err != nil {
							sugared.Infof("Bunlde:%s, Bucket: %d, MaxFlowNum: %d,Count: %d, SetMonitorMax Err: %v", bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow, err)
							continue
						}
						if unixTime > 0 {
							expStateUpdate = true
							localTime := time.UnixMilli(unixTime).Format("2006-01-02 15:04:05")
							content := &bytes.Buffer{}
							content.WriteString("包：" + bucket.BundleId + "\n")
							content.WriteString("桶ID:")
							content.WriteString(strconv.Itoa(bucket.BucketId))
							content.WriteString("\n桶名称：")
							content.WriteString(bucket.BucketTitle)
							content.WriteString("\n桶描述：")
							content.WriteString(bucket.BucketDesc)
							content.WriteString("\n")
							content.WriteString("最大人数:")
							content.WriteString(strconv.Itoa(bucket.MaxFlowNum))
							content.WriteString("\n")
							content.WriteString("当前人数:")
							content.WriteString(strconv.Itoa(bucketFlow))
							content.WriteString("\n")
							content.WriteString("已满量:")
							content.WriteString(localTime)
							sugared.Infof("MaxFlowNum: %d,Count: %d,Time: %s", bucket.MaxFlowNum, bucketFlow, localTime)
							bfr := BucketFullRequest{
								BundleId: bucket.BundleId,
								BucketId: strconv.Itoa(bucket.BucketId),
								LimitNum: bucket.MaxFlowNum,
								NowNum:   bucketFlow,
								EndTime:  localTime,
							}
							sendBucketFull(bfr)
							// 发送满量消息
							for _, ddConfig := range ddConfigs {
								err = notify.DDNotifyWithConfig(ddConfig, content.String())
								if err != nil {
									sugared.Errorf("notify.DDNotifyWithConfig, Config: %v err: %v", ddConfig, err)
								}
								sugared.Infof("DD Send Url: %s, At: %v", ddConfig.API, ddConfig.At)
							}

							//result = append(result, bucketResult)
							sugared.Infof("Bundle: %s, Bucket: %d, MaxFlowNum: %d, Count: %d, SetMonitorMax Success,UpdateTime: %s", bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow, time.UnixMilli(unixTime).Format(time.RFC3339))
						}
					}
					hourFlow, err := bucketsFlow.GetWithFlowType(testergame.BucketFlowTypeHour, bundleId, bucket.BucketId)
					if err != nil {
						hourFlow = 0
					}
					if bucket.HourMaxFlowNum > 0 && hourFlow > bucket.HourMaxFlowNum {
						unixTime, err := bucketsFlow.SetMonitorMax(bucket.BundleId, bucket.BucketId, bucket.HourMaxFlowNum, testergame.BucketFlowTypeHour)
						if err != nil {
							sugared.Infof("Bunlde:%s, Bucket: %d, MaxFlowNum: %d,Count: %d, SetMonitorMax Err: %v", bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow, err)
							continue
						}
						if unixTime > 0 {
							//expStateUpdate = true
							localTime := time.UnixMilli(unixTime).Format("2006-01-02 15:04:05")
							content := &bytes.Buffer{}
							content.WriteString("包：" + bucket.BundleId + "\n")
							content.WriteString("桶ID:")
							content.WriteString(strconv.Itoa(bucket.BucketId))
							content.WriteString("\n桶名称：")
							content.WriteString(bucket.BucketTitle)
							content.WriteString("\n桶描述：")
							content.WriteString(bucket.BucketDesc)
							content.WriteString("\n")
							content.WriteString(time.Now().Format("2006010215"))
							content.WriteString("小时最大人数:")
							content.WriteString(strconv.Itoa(bucket.HourMaxFlowNum))
							content.WriteString("\n")
							content.WriteString("当前人数:")
							content.WriteString(strconv.Itoa(hourFlow))
							content.WriteString("\n")
							content.WriteString("已满量:")
							content.WriteString(localTime)
							sugared.Infof("MaxFlowNum: %d,Count: %d,Time: %s", bucket.HourMaxFlowNum, hourFlow, localTime)
							// 发送满量消息
							for _, ddConfig := range ddConfigs {
								err = notify.DDNotifyWithConfig(ddConfig, content.String())
								if err != nil {
									sugared.Errorf("notify.DDNotifyWithConfig, Config: %v err: %v", ddConfig, err)
								}
								sugared.Infof("DD Send Url: %s, At: %v", ddConfig.API, ddConfig.At)
							}

							//result = append(result, bucketResult)
							sugared.Infof("Bundle: %s, Bucket: %d, MaxFlowNum: %d, Count: %d, SetMonitorMax Success,UpdateTime: %s", bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow, time.UnixMilli(unixTime).Format(time.RFC3339))
						}
					}
				}
				if expStateUpdate {
					if expsState := ga.getExperimentState(bucketConfigs); expsState != nil {
						_ = ga.pushExperimentsState(bundleId, expsState, PushExpStateTypeFlow)
					}
				}
			}
		}
	}
}

type BucketFullRequest struct {
	BundleId string `json:"bundleid"`
	BucketId string `json:"bucket"`
	LimitNum int    `json:"limit_num"`
	NowNum   int    `json:"now_num"`
	EndTime  string `json:"end_time"`
}

const BucketFullAPI = "https://admarket.youxi123.com/api/batchplan/bucketfull"

func sendBucketFull(bfr BucketFullRequest) {
	jsonData, err := json.Marshal(bfr)
	if err != nil {
		sugared.Errorf("json marshal err: %v", err)
		return
	}
	sugared.Infof("Sending BucketFullRequest: %s", jsonData)
	response, err := http.Post(BucketFullAPI, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		sugared.Errorf("http post err: %v", err)
		return
	}
	defer response.Body.Close()
	if response.StatusCode != 200 {
		sugared.Errorf("http post err: %v", response.Status)
	}
}

type PushExpsStateResp struct {
	Code int `json:"code"`
}

// pushExperimentsState 发布实验状态到BI后台
func (ga *GametesterAdmin) pushExperimentsState(bundleId string, experiments map[string]map[string]ExperimentState, pushType string) error {
	bundleState := map[string]map[string]map[string]ExperimentState{
		bundleId: experiments,
	}
	jsonData, err := json.Marshal(bundleState)
	if err != nil {
		sugared.Errorf("json.Marshal Error: %v", err)
		return err
	}
	expsStateCacheKey := fmt.Sprintf(ExperimentsStateCacheKey, bundleId)
	ga.prodRedis.Set(context.Background(), expsStateCacheKey, jsonData, time.Hour*24*7)
	api := ExperimentsStatePushAPI + "?type=" + pushType
	response, err := http.Post(api, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		sugared.Errorf("http.Post To %s, Error: %v", ExperimentsStatePushAPI, err)
		return err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		sugared.Errorf("PushExperimentsState StatusCode: %v", response.StatusCode)
		return fmt.Errorf("PushExperimentsState StatusCode: %v", response.StatusCode)
	}
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		sugared.Errorf("ReadAll response body Error: %v", err)
		return err
	}
	result := PushExpsStateResp{}
	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		sugared.Errorf("Unmarshal response body Error: %v", err)
		return err
	}
	if result.Code != 1 {
		sugared.Errorf("PushExperimentsState responseBody: %s", string(responseBody))
		return fmt.Errorf("PushExperimentsState responseBody: %s", string(responseBody))
	}
	return nil
}

func (ga *GametesterAdmin) GetExpsStateFromCache(bundleId string, isPush bool) map[string]map[string]map[string]ExperimentState {
	expsStateCacheKey := fmt.Sprintf(ExperimentsStateCacheKey, bundleId)
	jsonData, err := ga.prodRedis.Get(context.Background(), expsStateCacheKey).Bytes()
	if err != nil {
		sugared.Errorf("ProdRedis Get Error: %v", err)
		return nil
	}
	// 临时推送
	if isPush {
		if ess := ga.getExperimentState(ga.GetBucketConfigs(bundleId, testergame.EnvProd)); ess != nil {
			if err = ga.pushExperimentsState(bundleId, ess, PushExpStateTypeFlow); err != nil {
				sugared.Errorf("PushExperimentsState Error: %v", err)
			}
		}
	}
	expsState := make(map[string]map[string]map[string]ExperimentState)
	err = json.Unmarshal(jsonData, &expsState)
	if err != nil {
		sugared.Errorf("json Unmarshal Error: %v", err)
		return nil
	}
	return expsState
}

func (ga *GametesterAdmin) getBundleBucketConfigs(bundleId, env string) []testergame.BucketConfig {
	cacheKey := testergame.GetGameTesterCacheKeyWithBundleAndEnv(bundleId, env)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	var bRedisClient cache.RedisClient
	switch env {
	case testergame.EnvTesting:
		bRedisClient = ga.localRedis
	case testergame.EnvGray:
		bRedisClient = ga.grayRedis
	case testergame.EnvProd:
		bRedisClient = ga.prodRedis
	default:
		return nil
	}
	sugared.Infof("CacheKey: %s", cacheKey)
	jsonData, err := bRedisClient.Get(ctx, cacheKey).Bytes()
	if err != nil {
		sugared.Errorf("prodRedis Get Error: %v", err)
		return nil
	}
	bucketConfigs := make([]testergame.BucketConfig, 0)
	err = json.Unmarshal(jsonData, &bucketConfigs)
	if err != nil {
		sugared.Errorf("JSON Unmarshal Error: %v", err)
		return nil
	}
	return bucketConfigs
}

const (
	ExperimentTypeNew    string = "new"
	ExperimentTypeActive string = "active"
)

// getExperimentState 获取实验状态
func (ga *GametesterAdmin) getExperimentState(bucketConfigs []testergame.BucketConfig) map[string]map[string]ExperimentState {
	exps := make(map[string]map[string]ExperimentState)
	bucketFlow := &testergame.BucketsFlow{}
	for _, b := range bucketConfigs {
		expType := getExperimentTypeString(b.ExperimentType)
		if _, ok := exps[expType]; !ok {
			exps[expType] = make(map[string]ExperimentState)
		}
		// 处理实验列表中的方案
		for expId, _ := range b.ExperimentList {
			if expState, ok := exps[expType][expId]; ok {
				if expState.State != testergame.BucketStateOnline {
					expState.State = b.BucketState
				}
				if bucketFlow.Get(b.BundleId, b.BucketId) > b.MaxFlowNum {
					expState.State = testergame.BucketStateClose
				}
				if expState.ExperimentDays < b.ExpermentDays {
					expState.ExperimentDays = b.ExpermentDays
				}
				exps[expType][expId] = expState
			} else {
				expState = ExperimentState{
					ExperimentID:   expId,
					StartTime:      0,
					EndTime:        0,
					State:          b.BucketState,
					ExperimentDays: b.ExpermentDays,
					ExperimentType: b.ExperimentType,
					Bucket:         b.BucketId,
					BucektDesc:     b.BucketDesc,
				}
				if bucketFlow.Get(b.BundleId, b.BucketId) > b.MaxFlowNum {
					expState.State = testergame.BucketStateClose
				}
				exps[expType][expId] = expState
			}
		}
		// 处理释放配置中方案
		for expId, releaseConfig := range b.ReleaseExperiments {
			if releaseConfig.BucketId == testergame.ReleaseHoldBucket {
				if expState, ok := exps[expType][expId]; !ok {
					expState = ExperimentState{
						ExperimentID:   expId,
						StartTime:      0,
						EndTime:        0,
						State:          testergame.BucketStateClose,
						ExperimentDays: b.ExperimentType,
						Bucket:         b.BucketId,
						BucektDesc:     b.BucketDesc,
					}
					exps[expType][expId] = expState
				}
			}
		}
	}
	return exps
}

// mergeExperimentState 合并配置的方案状态
func (ga *GametesterAdmin) mergeExperimentState(oldExperimentStates, newExperimentStates map[string]map[string]ExperimentState) map[string]map[string]ExperimentState {
	// 新方案没有在旧方案中，则为新增方案，设置方案开始时间为时间
	for expType, expStates := range newExperimentStates {
		for expId, expState := range expStates {
			if _, ok := oldExperimentStates[expType][expId]; !ok {
				expState.StartTime = time.Now().Unix()
				newExperimentStates[expType][expId] = expState
			}
		}
	}
	// 新方案中缺失的旧方案被完全释放
	for expType, expStates := range oldExperimentStates {
		for expId, expState := range expStates {
			if _, ok := newExperimentStates[expType][expId]; !ok {
				expState.State = testergame.BucketStateOffline
				if expState.ExperimentDays < 60 {
					expState.EndTime = time.Now().Unix() + int64(expState.ExperimentDays)*24*60*60
				} else {
					expState.EndTime = time.Now().Unix() + 60*24*60*60
				}
				expState.EndTime = time.Now().Unix()
				newExperimentStates[expType][expId] = expState
			}
		}
	}
	return newExperimentStates
}

func getExperimentTypeString(expType int) string {
	if expType == testergame.ExperimentTypeNew {
		return ExperimentTypeNew
	} else {
		return ExperimentTypeActive
	}
}

func (ga *GametesterAdmin) getRedisClientByEnv(env string) cache.RedisClient {
	var gRedis cache.RedisClient
	switch env {
	case testergame.EnvTesting:
		gRedis = ga.localRedis
	case testergame.EnvGray:
		gRedis = ga.grayRedis
	case testergame.EnvProd:
		gRedis = ga.prodRedis
	default:
		gRedis = ga.prodRedis
	}
	return gRedis
}

func (ga *GametesterAdmin) AddWhiteUid(uid, evn string) {
	gRedis := ga.getRedisClientByEnv(evn)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	_, err := gRedis.HSet(ctx, testergame.UserWhiteListCacheKey, uid, 1).Result()
	if err != nil {
		sugared.Errorf("Get Error: %v", err)
		return
	}
}

func (ga *GametesterAdmin) GetWhiteUid(evn string) map[string]string {
	gRedis := ga.getRedisClientByEnv(evn)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	uids, err := gRedis.HGetAll(ctx, testergame.UserWhiteListCacheKey).Result()
	if err != nil {
		sugared.Errorf("Get Error: %v", err)
		return map[string]string{}
	}
	return uids
}

func (ga *GametesterAdmin) GetUserInfo(bundleId, uid, env string) *testergame.UserExperiment {
	var gRedis cache.RedisClient
	switch env {
	case testergame.EnvTesting:
		gRedis = ga.localRedis
	case testergame.EnvGray:
		gRedis = ga.grayRedis
	default:
		gRedis = ga.prodRedis
	}
	cacheKey := fmt.Sprintf(testergame.UserExperimentCacheKey, bundleId, uid)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	userJson, err := gRedis.Get(ctx, cacheKey).Bytes()
	if err != nil {
		sugared.Errorf("prodRedis Get Error: %v", err)
		return ga.GetUserInfoFromMysql(bundleId, uid, env)
	}
	ue := &testergame.UserExperiment{}
	err = json.Unmarshal(userJson, ue)
	if err != nil {
		sugared.Errorf("JSON Unmarshal Error: %v", err)
		return ga.GetUserInfoFromMysql(bundleId, uid, env)
	}
	return ue
}

func (ga *GametesterAdmin) GetUserInfoFromMysql(bundleId, uid, env string) *testergame.UserExperiment {
	var mysqlDB xorm.EngineInterface
	switch env {
	case testergame.EnvTesting:
		mysqlDB = ga.localMysql
	default:
		mysqlDB = ga.prodMysql
	}
	tableIndex := testergame.GetUserTableIndex(uid, 64)
	tableName := fmt.Sprintf(testergame.GtUserExperimentsTableName, tableIndex)
	gtUser := &testergame.GtUserExperiments{}
	ok, err := mysqlDB.Where("bundle_id=? AND uid=?", bundleId, uid).Table(tableName).Get(gtUser)
	if err != nil {
		sugared.Errorf("Get Error: %v", err)
		return nil
	}
	if !ok {
		sugared.Errorf("user info not found from table : %s where bundle_id=%s and uid=%s data", tableName, bundleId, uid)
		return nil
	}
	return &testergame.UserExperiment{
		BucketId:       gtUser.BucketId,
		ExperimentId:   gtUser.ExperimentId,
		ExperimentType: gtUser.ExperimentType,
		DistributeTime: gtUser.DistributeTime,
	}
}

func (ga *GametesterAdmin) GetBucketFlow(bundleId, env string, bucket string) map[string]any {
	totalKey := fmt.Sprintf(testergame.BucketCacheKey, bundleId)
	hourKey := fmt.Sprintf(testergame.BucketFlowHourCacheKey, bundleId, time.Now().UTC().Format("2006010215"))
	var bRedisClient cache.RedisClient
	switch env {
	case testergame.EnvTesting:
		bRedisClient = ga.localRedis
	case testergame.EnvProd:
		bRedisClient = ga.prodRedis
	default:
		bRedisClient = ga.localRedis
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	totalCount, err := bRedisClient.HGet(ctx, totalKey, bucket).Int()
	if err != nil {
		totalCount = 0
	}
	hourCount, err := bRedisClient.HGet(ctx, hourKey, bucket).Int()
	if err != nil {
		hourCount = 0
	}
	return map[string]any{
		"total": totalCount,
		"hour":  hourCount,
	}
}

func (ga *GametesterAdmin) GetBucketConfigs(bundleId, env string) []testergame.BucketConfig {
	cacheKey := testergame.GetGameTesterCacheKeyWithBundleAndEnv(bundleId, env)
	gRedis := ga.getRedisClientByEnv(env)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	jsonData, err := gRedis.Get(ctx, cacheKey).Bytes()
	if err != nil {
		sugared.Errorf("Get Error: %v", err)
		return nil
	}
	bucketConfigs := make([]testergame.BucketConfig, 0)
	err = json.Unmarshal(jsonData, &bucketConfigs)
	if err != nil {
		sugared.Errorf("JSON Unmarshal Error: %v", err)
		return nil
	}
	return bucketConfigs
}

func (ga *GametesterAdmin) SetBucketFlow(bundleId, flowType string, bucket, flow int) error {
	var key string
	switch flowType {
	case testergame.BucketFlowTypeTotal:
		key = fmt.Sprintf(testergame.BucketCacheKey, bundleId)
	case testergame.BucketFlowTypeHour:
		key = fmt.Sprintf(testergame.BucketFlowHourCacheKey, bundleId, time.Now().UTC().Format("2006010215"))
	default:
		return errors.New("no flowtype:" + flowType)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	err := ga.localRedis.HSet(ctx, key, strconv.Itoa(bucket), flow).Err()
	if err != nil {
		sugared.Errorf("HSet Error: %v", err)
		return err
	}
	ga.localRedis.Expire(ctx, key, time.Hour+time.Minute*5)
	return nil
}

func (ga *GametesterAdmin) ValidateBucketConfigs(bundleId string, bucketConfigs []testergame.BucketConfig) (result map[string]any, valid bool) {
	prodBucketConfigs := ga.GetBucketConfigs(bundleId, testergame.EnvProd)
	return testergame.ValidateSchemeFeatures(prodBucketConfigs, bucketConfigs, ga.adminConfig.Validate.LeastCount, ga.adminConfig.Validate.MiniDiffCount)
}

func (ga *GametesterAdmin) UploadToRefactor2(data []byte, env string) error {
	api := ""
	switch env {
	case testergame.EnvTesting:
		api = ga.adminConfig.Refactor2.TestingUploadApi
	case testergame.EnvProd:
		api = ga.adminConfig.Refactor2.ProdUploadApi
	default:
		sugared.Errorf("refactor2 not support env:%s", env)
		return nil
	}

	resp, err := http.Post(api, "application/json", bytes.NewBuffer(data))
	if err != nil {
		sugared.Errorf("Post Url: %s Error: %v", api, err)
		return err
	}
	switch resp.StatusCode {
	case http.StatusOK:
		return nil
	case http.StatusBadRequest:
		defer resp.Body.Close()
		jsonDecoder := json.NewDecoder(resp.Body)
		bodyContent := make(map[string]string)
		err = jsonDecoder.Decode(&bodyContent)
		if err != nil {
			sugared.Errorf("JSON Decode Error: %v", err)
			return err
		}
		return errors.New(bodyContent["error"])
	default:
		return fmt.Errorf("status code: %d", resp.StatusCode)
	}
}

func (ga *GametesterAdmin) SetUserDistribute(bundleId, uid string, bucket int, exp, expType string, installTime string) error {
	install, err := time.Parse("2006-01-02 15:04", installTime)
	if err != nil {
		return err
	}
	installUnix := install.UTC().UnixMilli()
	expTypeInt := testergame.ExperimentTypeNew
	switch expType {
	case "new":
		expTypeInt = testergame.ExperimentTypeNew
	case "active":
		expTypeInt = testergame.ExperimentTypeActive
	default:
		return errors.New("no expType:" + expType)
	}
	ue := testergame.UserExperiment{
		BucketId:       bucket,
		ExperimentId:   exp,
		ExperimentType: expTypeInt,
		DistributeTime: installUnix,
	}
	err = testergame.SaveUser(ga.localMysql, ga.localRedis, bundleId, uid, ue)
	if err != nil {
		sugared.Errorf("Save User Error: %v", err)
		return err
	}
	return nil
}

func (ga *GametesterAdmin) ServerDecrypt(bundleId, env string, deText string) (*testergame.SecretData, error) {
	if key, ok := ga.adminConfig.SecretKeys[env][bundleId]; ok {
		return testergame.DecryptSecretData(key, deText)
		//if data, err := encrypt.AesGCMDecrypt(string(deText), []byte(key)); err == nil {
		//	sd := &testergame.SecretData{}
		//	if err = gob.NewDecoder(bytes.NewBuffer(data)).Decode(sd); err == nil {
		//		return sd, nil
		//	}
		//	return nil, err
		//	//if err := json.Unmarshal(data, sd); err == nil {
		//	//	return sd, nil
		//	//} else {
		//	//	sugared.Errorf("Unmarshal Error: %v", err)
		//	//	return nil, err
		//	//}
		//} else {
		//	sugared.Errorf("encrypt.AesGCMDecrypt Error: %v", err)
		//	return nil, err
		//}
	}
	return nil, fmt.Errorf("no such env:%s bundleId:%s", env, bundleId)
}
