package models

import (
	"context"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models/distributes"
	"io"
	"reflect"
	"slices"
	"strings"
)

type planCsrAllot map[string]string

type SudoDynamicTraffic struct {
	*Traffic
}

func NewSudoDynamicTraffic(ctx context.Context, bundleConfig *BundleConfig, abWriter io.Writer) *SudoDynamicTraffic {
	return &SudoDynamicTraffic{
		Traffic: NewTraffic(ctx, bundleConfig, abWriter),
	}
}

func (t *SudoDynamicTraffic) GetTrafficConfig(params map[string]any) map[string]any {

	sugared.Infof("SudoDynamic Start Execute GetTrafficConfig")

	//研发批次号
	if _, ok := params["pici"].(string); !ok {
		params["pici"] = ""
	}
	//研发批次类型
	if _, ok := params["shunt_type"].(string); !ok {
		params["shunt_type"] = "new"
	}

	//商业化方案号
	if _, ok := params["adwaynum"].(string); !ok {
		params["adwaynum"] = ""
	}

	//初始化
	initConfig := make(map[string]any)
	initConfig["shunt_type"] = params["shunt_type"]

	//研发方案不为空,判断是否在批次方案列表中，不在则置为空重新分配
	initConfig["plan"] = ""
	if plan, ok := params["plan"].(string); ok && plan != "" {
		if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(params["pici"].(string), params["shunt_type"].(string)); err == nil && plan != "" {
			if IsExistence(plan, productSchemeConf.Scheme) {
				initConfig["plan"] = plan
			}
		}
	}

	//研发方案号为空，重置分流标识，重新生成
	if initConfig["plan"] == "" {
		params["shunt_index"] = ""
	}

	//分流标识：
	if shuntIndex, ok := params["shunt_index"].(string); !ok || shuntIndex == "" {
		params["shunt_index"] = GetShuntIdent(t, params)
	}
	sugared.Infof("Sudoku Dynamic GetTrafficConfig shuntType:%s ,piCi: %s, shunt_index: %s", params["shunt_type"].(string), params["pici"].(string), params["shunt_index"].(string))

	// 分流标识返回
	initConfig["shunt_index"] = params["shunt_index"]

	// 方案存在，并且请求参数中并未有方案号，则分配方案号
	if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(params["pici"].(string), params["shunt_type"].(string)); err == nil && initConfig["plan"] == "" {
		sugared.Infof("%s：Sudoku获取研发方案配置_%s", t.bundleConfig.BundleId, params["pici"].(string))
		group := fmt.Sprintf("get_product_pici_scheme_%s_%s_%s_round", t.bundleConfig.BundleId, params["pici"].(string), params["shunt_type"].(string))
		sudoDistributeProvider := distributeFactory.GetDistributeProvider(distributes.SudoDistributeProviderRobinInstance)
		plan := sudoDistributeProvider.Distribute(group, params["shunt_index"].(string), slices.Clone(productSchemeConf.Scheme))
		sugared.Infof("Product pici Distributed shunt_index : %s Scheme: %v", initConfig["shunt_index"], plan)
		initConfig["plan"] = plan.Name

		//每次分配结果上报prometheus
		labels := prometheus.Labels{
			"bundle_id":   t.bundleConfig.BundleId,
			"pici":        params["pici"].(string),
			"shunt_type":  params["shunt_type"].(string),
			"shunt_index": initConfig["shunt_index"].(string),
			"plan":        initConfig["plan"].(string),
		}
		metrics.SudoProduct.With(labels).Inc()
	}

	// 判断 "other_pici" 是否存在并且是一个数组
	if otherPici, exists := params["piciList"]; exists {
		otherPlanConfig := make(map[string]any)
		if reflect.TypeOf(otherPici).Kind() == reflect.Slice {
			// 如果是数组（切片），则遍历其中的元素
			for _, pici := range otherPici.([]interface{}) {

				other_params := params
				other_params["pici"] = pici

				// 方案存在，并且请求参数中并未有方案号，则分配方案号
				if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(pici.(string), params["shunt_type"].(string)); err == nil {
					sugared.Infof("%s：Sudoku获取研发方案配置_%s", t.bundleConfig.BundleId, pici.(string))
					//分流标识
					other_shunt_index := GetShuntIdent(t, other_params)

					group := fmt.Sprintf("get_product_pici_scheme_%s_%s_%s_round", t.bundleConfig.BundleId, pici.(string), params["shunt_type"].(string))
					sudoDistributeProvider := distributeFactory.GetDistributeProvider(distributes.SudoDistributeProviderRobinInstance)
					other_plan := sudoDistributeProvider.Distribute(group, other_shunt_index, slices.Clone(productSchemeConf.Scheme))
					sugared.Infof("Product pici Distributed shunt_index : %s Scheme: %v", other_shunt_index, other_plan)

					//返回信息
					otherPlanConfig[pici.(string)] = planCsrAllot{
						"shunt_type":  params["shunt_type"].(string),
						"shunt_index": other_shunt_index,
						"plan":        other_plan.Name,
					}

					//每次分配结果上报prometheus
					labels := prometheus.Labels{
						"bundle_id":   t.bundleConfig.BundleId,
						"pici":        pici.(string),
						"shunt_type":  params["shunt_type"].(string),
						"shunt_index": other_shunt_index,
						"plan":        other_plan.Name,
					}
					metrics.SudoProduct.With(labels).Inc()
				}
			}
		}
		initConfig["piciList"] = otherPlanConfig
	}
	return initConfig
}

// GetShuntIdent 生成分流标识
func GetShuntIdent(t *SudoDynamicTraffic, params map[string]any) string {

	sugared.Infof("Sudoku GenerateShuntIdent 准备生成分流标识 : piCi: %s, shuntType: %s", params["pici"].(string), params["shunt_type"].(string))

	if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(params["pici"].(string), params["shunt_type"].(string)); err == nil {

		//获取归档配置
		archiveConf := productSchemeConf.Config
		//归档查询指定顺序
		selectOrder := t.bundleConfig.NewFilingSequence
		if params["shunt_type"].(string) != "new" {
			selectOrder = t.bundleConfig.DynamicFilingSequence
		}

		//查寻判断条件
		var shuntIndex string
		for _, target := range selectOrder {
			if pigeonholeConf, ok := archiveConf[target]; ok {
				target = strings.Replace(target, "_group", "", -1)
				sugared.Infof("查寻判断条件: %s", target)
				gears := 1
				if conf, ok := pigeonholeConf.(map[string]interface{}); ok {
					if kind, ok := conf["kind"].(string); ok {
						switch kind {
						case "list":
							if targetValue, ok := params[target].(string); ok {
								targetValue = strings.ToLower(targetValue)
								gears = FindString(targetValue, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							} else if targetFloat, ok := params[target].(float64); ok {
								gears = FindFloat(targetFloat, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							}
						case "range":
							if targetNum, ok := params[target].(float64); ok {
								gears = LastLessThan(targetNum, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							}
						case "group":
							if groupValue, ok := params[target].(string); ok {
								groupValue = strings.ToLower(groupValue)
								gears = FindGroup(groupValue, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							} else if groupFloat, ok := params[target].(float64); ok {
								gears = FindFloatGroup(groupFloat, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							}
						}
					}
				}
				shuntIndex = fmt.Sprintf("%s_%d", shuntIndex, gears)
				sugared.Infof("Sudoku GenerateShuntIdent : piCi: %s, shuntType: %s, target: %s , gears: %d ,shuntIndex：%s", params["pici"].(string), params["shunt_type"].(string), target, gears, shuntIndex)
			}
		}
		shuntIndex = strings.Trim(shuntIndex, "_")
		sugared.Infof("Sudoku GenerateShuntIdent 分流标识 : piCi: %s, shuntType: %s, shuntIndex：%s", params["pici"].(string), params["shunt_type"].(string), shuntIndex)
		return shuntIndex
	}
	return ""
}
