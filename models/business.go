package models

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/Masterminds/semver/v3"
	"hungrystudio.com/datatester/models/business"
	"sync"
)

type Business struct {
	config     business.Config
	configs    *business.Configs
	schemeAlgo *business.SchemeAlgo
	qa         *business.Qa
}

func NewBusiness(ctx context.Context, config business.Config) *Business {
	b := &Business{
		config:     config,
		configs:    business.NewConfigs(config.UpdateInterval),
		schemeAlgo: business.NewSchemeAlgo(),
	}
	if config.QaEnable {
		b.qa = business.NewAq()
	}
	go b.Close(ctx)
	return b
}

type AdwayTypeResult struct {
	AdwayType string
	Data      map[string]any
}

func (b *Business) Init(req business.Request) (map[string]map[string]any, error) {
	if _, ok := CountryC16[req.CountryCode]; !ok {
		req.CountryCode = CountryCodeDefault
	}
	if req.Adways == nil {
		req.Adways = make(map[string]string)
	}
	if versionConfigs := b.configs.GetVersionConfigs(req.BundleId, req.ApiVersion); versionConfigs != nil {
		algoResult := make(chan AdwayTypeResult, len(versionConfigs))
		wg := sync.WaitGroup{}
		for adwayType, adwaySchemes := range versionConfigs {
			wg.Add(1)
			go func(adwayType string, adwaySchemes business.AdwaySchemes) {
				defer wg.Done()
				// qa功能
				if b.config.QaEnable {
					if qaConfig, ok := b.qa.Get(req.BundleId, req.DistinctId, req.ApiVersion, adwayType); ok {
						if qaConfig.ApiVersion == req.ApiVersion && qaConfig.AdwayType == adwayType {
							if bConfig, ok := adwaySchemes.Schemes[qaConfig.Adwaynum]; ok {
								sugared.Infof("Init Scheme From QA,AdwayType: %s, Data: %+v", adwayType, bConfig)
								algoResult <- AdwayTypeResult{adwayType, bConfig}
								return
							}
						}
					}
				}
				// 白名单功能
				if adWayNum, ok := adwaySchemes.WhiteList[req.DistinctId]; ok {
					if data := b.configs.GetWhiteConfig(req.BundleId, req.ApiVersion, adwayType, adWayNum); data != nil {
						algoResult <- AdwayTypeResult{
							AdwayType: adwayType,
							Data:      data,
						}
						return
					}
				}
				// 用户当前方案是否有效，有效不再重新分配
				if adwayNum, ok := req.Adways[adwayType]; ok {
					if _, ok = adwaySchemes.Schemes[adwayNum]; ok {
						sugared.Infof("Init Scheme From Client,AdwayType: %s, Data: %+v", adwayType, adwaySchemes.Schemes[adwayNum])
						algoResult <- AdwayTypeResult{
							AdwayType: adwayType,
							Data:      adwaySchemes.Schemes[adwayNum],
						}
						return
					}
				}
				if len(adwaySchemes.DisSchemes) > 0 {
					algoKey := "hs:business:" + req.BundleId + ":" + req.ApiVersion + ":" + adwayType
					algo := b.schemeAlgo.GetAlgo(algoKey, adwaySchemes.DisSchemes)
					adwayNum := algo.Next().Name
					sugared.Infof("Server distribute: AdwayType: %s,AdwayNum: %s, Data: %v", adwayType, adwayNum, adwaySchemes.Schemes[adwayNum])
					algoResult <- AdwayTypeResult{
						AdwayType: adwayType,
						Data:      adwaySchemes.Schemes[adwayNum],
					}
				}
			}(adwayType, adwaySchemes)
		}
		wg.Wait()
		result := make(map[string]map[string]any)
		close(algoResult)
		for adwayReuslt := range algoResult {
			result[adwayReuslt.AdwayType] = adwayReuslt.Data
		}
		return result, nil
	}
	sugared.Infof("bundle(%s) apiVersion:(%s) no valid schemes", req.BundleId, req.ApiVersion)
	return make(map[string]map[string]any), nil
}

func (b *Business) InitV2(req business.Request) (map[string]map[string]any, error) {
	if _, ok := CountryC16[req.CountryCode]; !ok {
		req.CountryCode = CountryCodeDefault
	}
	if req.Adways == nil {
		req.Adways = make(map[string]string)
	}
	if versionConfigs := b.configs.GetVersionConfigs(req.BundleId, req.ApiVersion); versionConfigs != nil {
		algoResult := make(chan AdwayTypeResult, len(versionConfigs))
		wg := sync.WaitGroup{}
		for adwayType, adwaySchemes := range versionConfigs {
			vcr, err := compareVersion(req.MiniVersion, adwaySchemes.MiniVersion)
			if err != nil {
				continue
			}
			if vcr < 0 {
				continue
			}
			wg.Add(1)
			go func(adwayType string, adwaySchemes business.AdwaySchemes) {
				defer wg.Done()
				// qa功能
				if b.config.QaEnable {
					if qaConfig, ok := b.qa.Get(req.BundleId, req.DistinctId, req.ApiVersion, adwayType); ok {
						if qaConfig.ApiVersion == req.ApiVersion && qaConfig.AdwayType == adwayType {
							if bConfig, ok := adwaySchemes.Schemes[qaConfig.Adwaynum]; ok {
								sugared.Infof("Init Scheme From QA,AdwayType: %s, Data: %+v", adwayType, bConfig)
								algoResult <- AdwayTypeResult{adwayType, bConfig}
								return
							}
						}
					}
				}
				// 白名单功能
				if adWayNum, ok := adwaySchemes.WhiteList[req.DistinctId]; ok {
					if data := b.configs.GetWhiteConfig(req.BundleId, req.ApiVersion, adwayType, adWayNum); data != nil {
						algoResult <- AdwayTypeResult{
							AdwayType: adwayType,
							Data:      data,
						}
						return
					}
				}
				// 用户当前方案是否有效，有效不再重新分配
				if adwayNum, ok := req.Adways[adwayType]; ok {
					if _, ok = adwaySchemes.Schemes[adwayNum]; ok {
						sugared.Infof("Init Scheme From Client,AdwayType: %s, Data: %+v", adwayType, adwaySchemes.Schemes[adwayNum])
						algoResult <- AdwayTypeResult{
							AdwayType: adwayType,
							Data:      adwaySchemes.Schemes[adwayNum],
						}
						return
					}
				}
				if len(adwaySchemes.DisSchemes) > 0 {
					algoKey := "hs:business:" + req.BundleId + ":" + req.ApiVersion + ":" + adwayType
					algo := b.schemeAlgo.GetAlgo(algoKey, adwaySchemes.DisSchemes)
					adwayNum := algo.Next().Name
					sugared.Infof("Server distribute: AdwayType: %s,AdwayNum: %s, Data: %v", adwayType, adwayNum, adwaySchemes.Schemes[adwayNum])
					algoResult <- AdwayTypeResult{
						AdwayType: adwayType,
						Data:      adwaySchemes.Schemes[adwayNum],
					}
				}
			}(adwayType, adwaySchemes)
		}
		wg.Wait()
		result := make(map[string]map[string]any)
		close(algoResult)
		for adwayReuslt := range algoResult {
			result[adwayReuslt.AdwayType] = adwayReuslt.Data
		}
		return result, nil
	}
	sugared.Infof("bundle(%s) apiVersion:(%s) no valid schemes", req.BundleId, req.ApiVersion)
	return make(map[string]map[string]any), nil
}

func (b *Business) SaveBusinessConfigToDB(jsonData []byte) error {
	return business.SaveBusinessConfigToDB(jsonData)
}

func (b *Business) GetBusinessConfigs(page, size int) ([]business.DBConfig, error) {
	return business.GetBusinessConfigsPagination(page, size)
}

func (b *Business) GetServerConfigs() map[string]map[string]map[string]business.AdwaySchemes {
	return b.configs.GetAllConfigs()
}

func (b *Business) GetServerWhiteConfigs() map[string]map[string]map[string]map[string]map[string]any {
	return b.configs.GetAllWhilteConfigs()
}

func (b *Business) AddQa(jsonData []byte) error {
	qaConfig := business.QAConfig{}
	err := json.Unmarshal(jsonData, &qaConfig)
	if err != nil {
		return err
	}
	if versionConfig := b.configs.GetVersionConfigs(qaConfig.BundleId, qaConfig.ApiVersion); versionConfig != nil {
		if config, ok := versionConfig[qaConfig.AdwayType]; ok {
			if _, ok = config.Schemes[qaConfig.Adwaynum]; ok {
				sugared.Infof("add qa: %+v", qaConfig)
				b.qa.Add(qaConfig)
				return nil
			}
			return fmt.Errorf("BundleId: %s, Version: %s,AdwayType: %s,AdwayNum: %s, not found", qaConfig.BundleId, qaConfig.ApiVersion, qaConfig.AdwayType, qaConfig.Adwaynum)
		}
		return fmt.Errorf("BundleId: %s, Version: %s,AdwayType: %s, not found", qaConfig.BundleId, qaConfig.ApiVersion, qaConfig.AdwayType)
	}
	return fmt.Errorf("BundleId: %s, Version: %s not found", qaConfig.BundleId, qaConfig.ApiVersion)
}

func (b *Business) Close(ctx context.Context) {
	<-ctx.Done()
	b.schemeAlgo.Close()
}

func compareVersion(versionA, versionB string) (int, error) {
	versionA1, err := semver.NewVersion(versionA)
	if err != nil {
		sugared.Errorf("version: %s, err: %v", versionA, err)
		return 0, err
	}
	versionB1, err := semver.NewVersion(versionB)
	if err != nil {
		sugared.Errorf("version: %s, err: %v", versionB, err)
		return 0, err
	}
	return versionA1.Compare(versionB1), nil
}
