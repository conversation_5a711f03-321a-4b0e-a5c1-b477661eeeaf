package testergame

import (
	"errors"
	"github.com/twmb/murmur3"
	"hungrystudio.com/datatester/models/distributes"
	"hungrystudio.com/datatester/models/release"
	"slices"
	"strconv"
)

// 释放类型
const (
	ReleaseTypeTraffic string = "traffic" // 按流量
	ReleaseTypeWeight  string = "weight"  // 按权重
)

const ReleaseCommon string = "common" // 通用配置

const TotalPercent uint32 = 100

func GetReleaseSetting(gameScheme string, settings map[string]*ReleaseSetting) *ReleaseSetting {
	if setting, ok := settings[gameScheme]; ok {
		return setting
	}
	if setting, ok := settings[ReleaseCommon]; ok {
		return setting
	}
	return nil
}

// ReleaseConfig 释放目标配置
type ReleaseConfig struct {
	BucketId       int    `json:"bucketId"`
	ExperimentId   string `json:"experimentId"`
	TrafficPercent int    `json:"trafficPercent"`
	Weight         int    `json:"weight"`
}

// ReleaseSetting 释放设置
type ReleaseSetting struct {
	ReleaseType string          `json:"releaseType"`
	Configs     []ReleaseConfig `json:"config"`
	Traffics    *release.Release
	Weights     []distributes.Scheme
}

func (rs *ReleaseSetting) makeTraffics() {
	if rs.ReleaseType == ReleaseTypeTraffic {
		traffics := &release.Release{
			TrafficAllocation: make([]release.TrafficAllocation, 0),
		}
		start := 0
		for index, rConfig := range rs.Configs {
			end := uint32(start + rConfig.TrafficPercent)
			if end >= TotalPercent {
				end = TotalPercent
			}
			traffics.TrafficAllocation = append(traffics.TrafficAllocation, release.TrafficAllocation{
				Begin:    uint32(start),
				End:      end,
				EntityId: strconv.Itoa(index),
			})
			start += rConfig.TrafficPercent
			if start > int(TotalPercent) {
				break
			}
		}
		rs.Traffics = traffics
	}
}

func (rs *ReleaseSetting) makeWeights() {
	if rs.ReleaseType == ReleaseTypeWeight {
		weights := make([]distributes.Scheme, 0)
		for index, config := range rs.Configs {
			if config.Weight > 0 {
				weights = append(weights, distributes.Scheme{
					Name:   strconv.Itoa(index),
					Weight: config.Weight,
				})
			}
		}
		rs.Weights = weights
	}
}

func (rs *ReleaseSetting) ExecTrafficAllocation(uid string) (ReleaseConfig, error) {
	h := murmur3.New32()
	_, _ = h.Write([]byte(uid))
	uHash := h.Sum32()
	entryId := rs.Traffics.EvaluateTraffics(uHash % TotalPercent)
	if entryId != "" {
		if index, err := strconv.Atoi(entryId); err == nil {
			return rs.Configs[index], nil
		}
	}
	return ReleaseConfig{}, errors.New("no traffic")
}

func (rs *ReleaseSetting) ExecWeightAllocation(bundleId string, originBucket int, algoProvider *DistributeAlgoProvider) (ReleaseConfig, error) {
	algoKey := "hs:gametester:release:" + bundleId + ":" + strconv.Itoa(originBucket)
	algo := algoProvider.GetDistributeAlgo(AlgoTypeBucket, algoKey, slices.Clone(rs.Weights))
	if algo != nil {
		index, err := strconv.Atoi(algo.Next().Name)
		if err == nil {
			return rs.Configs[index], nil
		}
	}
	return ReleaseConfig{}, errors.New("no weight schemes")
}
