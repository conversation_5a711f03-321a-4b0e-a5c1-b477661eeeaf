package testergame

import (
	"context"
	"sync"

	"hungrystudio.com/datatester/models/distributes"
)

type DistributeAlgoProvider struct {
	algos map[string]distributes.DistributeAlgo
	mux   sync.Mutex
}

const (
	AlgoTypeBucket     string = "bucket"
	AlgoTypeGameScheme string = "game-scheme"
)

func NewDistributeAlgoProvider() *DistributeAlgoProvider {
	return &DistributeAlgoProvider{
		algos: make(map[string]distributes.DistributeAlgo),
	}
}

func (dap *DistributeAlgoProvider) GetDistributeAlgo(algoType, key string, schemes []distributes.Scheme) distributes.DistributeAlgo {
	if schemes == nil {
		sugared.Errorf("GetDistributeAlgo: schemes is nil")
		return nil
	}
	//sugared.Infof("Schemes: %v", schemes)
	if len(schemes) == 0 {
		sugared.Infof("algo: %s, no schemes:%v", key, schemes)
		return nil
	}
	dap.mux.Lock()
	defer dap.mux.Unlock()
	if algo, ok := dap.algos[key]; ok {
		sugared.Infof("exist algo key:%s,exec ResetSchemes: %v", key, schemes)
		algo.ResetSchemes(schemes)
		return algo
	} else {
		return dap.newAlgo(algoType, key, schemes)
	}
}

func (dap *DistributeAlgoProvider) newAlgo(algoType, key string, schemes []distributes.Scheme) distributes.DistributeAlgo {

	if len(schemes) == 0 {
		sugared.Errorf("newAlgo schemes is nil")
		return nil
	}
	var algo distributes.DistributeAlgo
	switch algoType {
	case AlgoTypeBucket:
		algo = distributes.NewWeightRoundRobin(key, schemes)
	case AlgoTypeGameScheme:
		algo = distributes.NewSudokuRoundRobin(key, schemes)
	default:
		sugared.Errorf("algo type %s is not supported", algoType)
	}
	if algo != nil {
		dap.algos[key] = algo
	}
	return algo
}

func (dap *DistributeAlgoProvider) Close(ctx context.Context) {
	<-ctx.Done()
	dap.mux.Lock()
	defer dap.mux.Unlock()
	for key, algo := range dap.algos {
		sugared.Infof("Close algo: %s", key)
		algo.Close()
		delete(dap.algos, key)
	}
}
