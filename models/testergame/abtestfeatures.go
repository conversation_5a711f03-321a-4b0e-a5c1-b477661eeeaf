package testergame

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	client2 "hungrystudio.com/core/client"
	"hungrystudio.com/core/config"
	"hungrystudio.com/datatester/metrics"
)

type ABTestRequest struct {
	ProjectKey string         `json:"project_key"`
	Uid        string         `json:"uid"`
	BundleId   string         `json:"bundle_id"`
	Attributes map[string]any `json:"attributes"`
	Debug      int32          `json:"debug"`
	LayerIds   []int          `json:"layer_ids"`
}

type ABTestData struct {
	HitSample    map[string]any `json:"hit_sample"`
	Hits         []LayerHitInfo `json:"hits"`
	Params       map[string]any `json:"params"`
	CsrIndentity []string       `json:"csr_indentity"`
	Debug        any            `json:"debug,omitempty"`
}

func (data *ABTestData) CompressHit() []map[string]any {
	list := []map[string]any{}
	for _, hit := range data.Hits {
		list = append(list, map[string]any{
			"id":   hit.GroupId,
			"name": hit.GroupName,
			"t":    hit.ExpUType,
			"lid":  hit.LayerId,
			"eid":  hit.ExpId,
		})
	}
	return list
}

type ABTestResponse struct {
	Data    ABTestData `json:"data"`
	TraceId string     `json:"trace_id"`
	Code    int        `json:"code"`
}
type LayerHitInfo struct {
	LayerId   int    `json:"layer_id"`
	ExpId     int    `json:"exp_id"`
	GroupId   int    `json:"group_id"`
	GroupName string `json:"group_name"`
	ExpUType  int    `json:"exp_utype"`
}

type ABTestFeatures struct {
	configs map[string]ABTestFeature
	client  *http.Client
}

func NewABTestFeatures(configs map[string]ABTestFeature, clientConfig config.HttpClientConfig) *ABTestFeatures {
	client := client2.NewHttpClient(clientConfig)
	return &ABTestFeatures{
		configs: configs,
		client:  client,
	}
}

func (ab *ABTestFeatures) Request(req ABTestRequest) (ABTestResponse, error) {

	if _, ok := ab.configs[req.BundleId]; !ok {
		return ABTestResponse{}, errors.New("bundle: " + req.BundleId + " not found")
	}
	req.Debug = ab.configs[req.BundleId].Debug
	jsonData, err := json.Marshal(req)
	if err != nil {
		sugared.Errorf("ABTestReatures.Request json marshal failed: %v", err)
		return ABTestResponse{}, err
	}
	request, err := http.NewRequest(http.MethodPost, ab.configs[req.BundleId].APIUrl, bytes.NewReader(jsonData))
	if err != nil {
		sugared.Errorf("ABTestReatures.Request http.NewRequest failed: %v", err)
		return ABTestResponse{}, err
	}
	now := time.Now()
	request.Header.Set("Content-Type", "application/json")
	ctx0, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	response, err := ab.client.Do(request.WithContext(ctx0))
	go func(useTime float64) {
		metrics.ABTestRequestExecTime.Set(useTime)
	}(float64(time.Now().Sub(now).Microseconds()))
	if err != nil {
		sugared.Errorf("ABTestReatures.Request Do failed: %v", err)
		return ABTestResponse{}, err
	}
	go func(status string) {
		metrics.ABTestRequestStatus.With(prometheus.Labels{"status": status}).Inc()
	}(strconv.Itoa(response.StatusCode))
	if response.StatusCode == http.StatusOK {
		defer response.Body.Close()
		decoder := json.NewDecoder(response.Body)
		abResp := ABTestResponse{}
		err = decoder.Decode(&abResp)
		if err != nil {
			sugared.Errorf("ABTestReatures.Request json decode failed: %v", err)
			return ABTestResponse{}, err
		}
		return abResp, nil
	}
	return ABTestResponse{}, errors.New("require status: " + response.Status)
}

type LeaveApiResponse struct {
	Code    int    `json:"code"`
	TraceId string `json:"trace_id"`
	Data    struct {
		DeleteCount int `json:"delete_count"`
	} `json:"data"`
}

// RequestLeaveApi 调用 清空用户进组缓存接口
func (ab *ABTestFeatures) RequestLeaveApi(req ABTestRequest) error {
	bodyMap := map[string]any{
		"project_key": req.ProjectKey,
		"uid":         req.Uid,
	}
	jsonData, err := json.Marshal(bodyMap)
	if err != nil {
		sugared.Errorf("ABTestReatures.Request json marshal failed: %v", err)
		return err
	}
	resp, err := ab.client.Post(ab.configs[req.BundleId].LeaveAPI, "application/json", bytes.NewReader(jsonData))
	if err != nil {
		sugared.Errorf("ABTestReatures.Request POST failed: %v", err)
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		sugared.Errorf("ABTestReatures.RequestLeaveApi Body:%v POST failed: %v", bodyMap, resp.Status)
		return errors.New("require status: " + resp.Status)
	}
	defer resp.Body.Close()
	respData := &LeaveApiResponse{}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		sugared.Errorf("ABTestReatures.RequestLeaveApi ReadAll failed: %v", err)
		return err
	}
	err = json.Unmarshal(body, respData)
	if err != nil {
		sugared.Errorf("ABTestReatures.RequestLeave body: %s, json decode failed: %v", string(body), err)
		return err
	}
	if respData.Code != 0 {
		sugared.Errorf("ABTestReatures.RequestLeave respData.traceId:%s", respData.TraceId)
		return errors.New("require status: " + resp.Status)
	}
	sugared.Infof("ABTestReatures.RequestLeaveApi Resp traceId: %s, deleteCount: %d", respData.TraceId, respData.Data.DeleteCount)
	return nil
}

const (
	UTypeNew    = 1
	UTypeActive = 2
)

func ParseGameWayNum3(utype int, resp ABTestResponse) (GameWayNum string, csr string) {
	GameWayNum = "1"
	if utype == UTypeActive { // 活跃
		GameWayNum = "1_0" // 兼容客户端逻辑
	}
	csr = strings.Join(resp.Data.CsrIndentity, "|")

	if len(resp.Data.Hits) == 1 {
		expId := resp.Data.Hits[0].ExpId
		// 历史实验逻辑，实验关闭后可删除代码
		// http://abtest.youxi123.com/#/experimentManage/experimentDetail?exp_id=3&projectId=2
		// http://abtest.youxi123.com/#/experimentManage/experimentDetail?exp_id=4&projectId=2
		if expId == 3 || expId == 4 {
			GameWayNum = resp.Data.Hits[0].GroupName
		}
	}
	return
}
