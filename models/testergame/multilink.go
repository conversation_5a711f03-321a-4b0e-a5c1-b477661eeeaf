package testergame

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/exp/maps"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/encrypt"
	"os"
	"slices"
	"sync"
	"time"
)

type WhiteList map[string]int // 白名单
type LinkParams struct {
	CustomParams map[string]any `json:"customParams"`
	Link         string         `json:"link"`
	Urls         []string       `json:"urls"`
	Version      string         `json:"version"`
} // 链路参数

type SchemeLink map[string]string       // 方案=>链路参数
type LinksMapping map[string]LinkParams // 链路ID =》 链路参数

const LinksDataCacheKey = "hs:gametester:multilink:%s:%s"
const LinksDataCacheExpire = time.Hour * 24 * 7
const (
	WhiteLink   = "white"
	ReleaseLink = "release"
)

const LinkParams0 = "0"

type LinksData struct {
	WhiteList      []string                `json:"white_list"`
	GlobalParams   map[string]LinkParams   `json:"global_params"`
	LinksParams    map[string]SchemeLink   `json:"links_params"`
	LatestUpdate   int64                   `json:"latest_update"`
	LatestPublish  int64                   `json:"latest_publish"`
	PublishVersion string                  `json:"publish_version"`
	LinksMappings  map[string]LinksMapping `json:"links_mappings"`

	bundleId    string
	env         string
	eTag        string
	mux         sync.RWMutex
	redisClient cache.RedisClient
}

func linksDataCacheKey(bundleId, env string) string {
	return fmt.Sprintf(LinksDataCacheKey, bundleId, env)
}

// loadLinksData 载入链路数据
func loadLinksData(redis cache.RedisClient, bundleId, env, etag string) (*LinksData, string) {
	ld := &LinksData{}
	key := linksDataCacheKey(bundleId, env)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	body, err := redis.Get(ctx, key).Bytes()
	if err == nil {
		rETag := encrypt.Md5(body)
		if rETag != "" && rETag != etag {
			if err = json.Unmarshal(body, &ld); err == nil {
				redis.Expire(ctx, key, LinksDataCacheExpire)
				return ld, rETag
			} else {
				sugared.Errorw("json.Umarshal err", "err", err, "body", string(body))
				return nil, etag
			}
		} else {
			return nil, etag
		}
	} else {
		sugared.Errorw("获取远程LinksData错误", "err", err.Error(), "key", key)
		return nil, etag
	}
}

func uploadLinksData(redis cache.RedisClient, linksData *LinksData, env string) error {
	key := linksDataCacheKey(linksData.bundleId, env)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	body, err := json.Marshal(linksData)
	if err != nil {
		return err
	}
	err = redis.Set(ctx, key, body, LinksDataCacheExpire).Err()
	if err != nil {
		return err
	}
	return nil
}

func NewLinksData(redis cache.RedisClient, bundleId, env string) *LinksData {
	ld, etag := loadLinksData(redis, bundleId, env, "")
	if ld != nil {
		ld.bundleId = bundleId
		ld.env = env
		ld.eTag = etag
		ld.redisClient = redis
		ld.mux = sync.RWMutex{}
	} else {
		sugared.Errorw("loadLinksDataFailed", "bundleId", bundleId, "env", env)
		ld = &LinksData{
			WhiteList:     make([]string, 0),
			GlobalParams:  make(map[string]LinkParams),
			LinksParams:   make(map[string]SchemeLink),
			LinksMappings: make(map[string]LinksMapping),

			bundleId:    bundleId,
			env:         env,
			redisClient: redis,
			mux:         sync.RWMutex{},
		}
	}
	return ld
}

func (ld *LinksData) GetPulished() *LinksData {
	linkData, _ := loadLinksData(ld.redisClient, ld.bundleId, ld.env, "")
	return linkData
}

func (ld *LinksData) Sync(linksData *LinksData) error {
	return uploadLinksData(ld.redisClient, linksData, ld.env)
}

func (ld *LinksData) Update(ctx context.Context) {
	sugared.Infof("Start Update LinksData, BundleId: %s,Env: %s", ld.bundleId, ld.env)
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			sugared.Infof("Stop LinksData BundleId(%s),ENV(%s) Update", ld.bundleId, ld.env)
			return
		case <-ticker.C:
			rld, rEtag := loadLinksData(ld.redisClient, ld.bundleId, ld.env, ld.eTag)
			if rld != nil {
				ld.mux.Lock()
				ld.WhiteList = rld.WhiteList
				ld.LinksParams = rld.LinksParams
				ld.LinksMappings = rld.LinksMappings
				ld.LatestUpdate = rld.LatestUpdate
				ld.LatestPublish = rld.LatestPublish
				ld.GlobalParams = rld.GlobalParams
				ld.PublishVersion = rld.PublishVersion
				ld.eTag = rEtag
				ld.mux.Unlock()
				sugared.Infow("LinksDataUpdated", "white", ld.WhiteList, "linksParams", ld.LinksParams, "GlobalPramams", ld.GlobalParams, "LinksMappings", ld.LinksMappings)
			}
		}
	}
}

type UploadLinksRequest struct {
	Links   LinkParams `json:"links"`
	Schemes []string   `json:"schemes"`
}

// UploadLinks 上传链路
func (ld *LinksData) UploadLinks(links []UploadLinksRequest) map[string][]string {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	result := make(map[string][]string)
	for _, link := range links {
		if _, ok := ld.LinksParams[WhiteLink]; !ok {
			ld.LinksParams[WhiteLink] = make(SchemeLink)
		}
		if _, ok := ld.LinksMappings[WhiteLink]; !ok {
			ld.LinksMappings[WhiteLink] = make(LinksMapping)
		}
		ld.LinksMappings[WhiteLink][link.Links.Link] = link.Links
		if _, ok := result[link.Links.Link]; !ok {
			result[link.Links.Link] = make([]string, 0)
		}
		for _, scheme := range link.Schemes {
			ld.LinksParams[WhiteLink][scheme] = link.Links.Link
			result[link.Links.Link] = append(result[link.Links.Link], scheme)
		}
	}

	if len(result) > 0 {
		ld.LatestUpdate = time.Now().Unix()
		return result
	}
	return nil
}

// UploadLink 上传链路
func (ld *LinksData) UploadLink(data map[string]LinkParams) error {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	if _, ok := ld.LinksParams[WhiteLink]; !ok {
		ld.LinksParams[WhiteLink] = make(SchemeLink)
	}
	if _, ok := ld.LinksMappings[WhiteLink]; !ok {
		ld.LinksMappings[WhiteLink] = make(LinksMapping)
	}
	for k, v := range data {
		ld.LinksParams[WhiteLink][k] = v.Link
		ld.LinksMappings[WhiteLink][v.Link] = v
		ld.LatestUpdate = time.Now().Unix()
	}
	return nil
}

// UploadGlobalParams 上传全局参数
func (ld *LinksData) UploadGlobalParams(params LinkParams) error {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	//if _, ok := ld.GlobalParams[WhiteLink]; !ok {
	//	ld.GlobalParams[WhiteLink] = make(LinkParams)
	//}
	ld.GlobalParams[WhiteLink] = params
	ld.LatestUpdate = time.Now().Unix()
	return nil
}

// AddWhite 添加白名单
func (ld *LinksData) AddWhite(uids []string) {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	for _, uid := range uids {
		if !slices.Contains(ld.WhiteList, uid) {
			ld.WhiteList = append(ld.WhiteList, uid)
			ld.LatestUpdate = time.Now().Unix()
		}
	}
}

// RemoveWhite 移出白名单
func (ld *LinksData) RemoveWhite(uids []string) {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	whiteList := make([]string, 0)
	for _, uid := range ld.WhiteList {
		if slices.Contains(uids, uid) {
			continue
		}
		whiteList = append(whiteList, uid)
		ld.LatestUpdate = time.Now().Unix()
	}
	ld.WhiteList = whiteList
}

const (
	SwitchLinkNotInWhiteLinkMappings = iota - 1 // 白名单链路管理中不存在
	SwitchLinkNotExist                          // 方案不存在
	SwitchLinkAppend                            // 方案追加
	SwitchLinkUpdate                            // 方案更新
)

// SwitchLinks 切换方案链路为正式链路
//func (ld *LinksData) SwitchLinks(exps []string) (map[string]int, error) {
//	ld.mux.Lock()
//	defer ld.mux.Unlock()
//	result := make(map[string]int)
//	if whiteLinks, ok := ld.LinksParams[WhiteLink]; ok && len(whiteLinks) > 0 {
//		for _, exp := range exps {
//			if val, ok := ld.LinksParams[WhiteLink][exp]; ok {
//				if _, ok := ld.LinksParams[ReleaseLink]; !ok {
//					ld.LinksParams[ReleaseLink] = make(SchemeLink)
//				}
//				if _, ok := ld.LinksMappings[ReleaseLink]; !ok {
//					ld.LinksMappings[ReleaseLink] = make(LinksMapping)
//				}
//				if _, ok := ld.LinksParams[ReleaseLink][exp]; ok {
//					result[exp] = SwitchLinkUpdate
//				} else {
//					result[exp] = SwitchLinkAppend
//				}
//
//				if lp, ok := ld.LinksMappings[WhiteLink][val]; ok {
//					ld.LinksMappings[ReleaseLink][val] = lp
//					delete(ld.LinksMappings[WhiteLink], val)
//				} else {
//					sugared.Errorw("LinkParams Not In White LinkMappings", "exp", exp, "link", val)
//					result[exp] = SwitchLinkNotInWhiteLinkMappings
//				}
//				ld.LinksParams[ReleaseLink][exp] = val
//				delete(ld.LinksParams[WhiteLink], exp)
//				ld.LatestUpdate = time.Now().Unix()
//			} else {
//				result[exp] = SwitchLinkNotExist
//			}
//		}
//	} else {
//		return nil, fmt.Errorf(`没有可发布的白名单链路`)
//	}
//	return result, nil
//}

type SwitchLinksRequest struct {
	Link    string   `json:"link"`
	Schemes []string `json:"schemes"`
}

// SwitchLinks 切换方案链路为正式链路
func (ld *LinksData) SwitchLinks(links []SwitchLinksRequest) map[string][]string {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	result := make(map[string][]string)
	for _, link := range links {
		if val, ok := ld.LinksMappings[WhiteLink][link.Link]; ok {
			if _, ok = ld.LinksMappings[ReleaseLink]; !ok {
				ld.LinksMappings[ReleaseLink] = make(LinksMapping)
			}
			ld.LinksMappings[ReleaseLink][link.Link] = val
			delete(ld.LinksMappings[WhiteLink], link.Link)
			result[link.Link] = make([]string, 0)
		}
		for _, scheme := range link.Schemes {
			if _, ok := ld.LinksParams[ReleaseLink]; !ok {
				ld.LinksParams[ReleaseLink] = make(SchemeLink)
			}
			ld.LinksParams[ReleaseLink][scheme] = link.Link
			result[link.Link] = append(result[link.Link], scheme)
			delete(ld.LinksMappings[WhiteLink], scheme)
		}
	}
	if len(result) > 0 {
		ld.LatestUpdate = time.Now().Unix()
		return result
	}
	return nil
}

// SwitchGlobalParams 切换全局参数
func (ld *LinksData) SwitchGlobalParams() error {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	if val, ok := ld.GlobalParams[WhiteLink]; ok {
		ld.GlobalParams[ReleaseLink] = val
		ld.LatestUpdate = time.Now().Unix()
		return nil
	}
	return fmt.Errorf("没有可切换的全局参数")
}

const (
	DownLinkNotExist = iota // 方案线路不存在
	DownLinkSuccess         // 成功下线
)

// 增加白名单线路下线，解绑
// 线路映射，白名单和release是否区分，还是用一个线路映射。白名单线路映射失效，则取release线路映射

// DownWhiteLinks 白名单线路下线
func (ld *LinksData) DownWhiteLinks(linkIds []string) map[string][]string {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	result := make(map[string][]string)
	for _, linkId := range linkIds {
		if _, ok := result[linkId]; !ok {
			result[linkId] = make([]string, 0)
		}
		if _, ok := ld.LinksMappings[WhiteLink][linkId]; ok {
			for scheme, link := range ld.LinksParams[WhiteLink] {
				if link == linkId {
					result[linkId] = append(result[linkId], scheme)
					delete(ld.LinksParams[WhiteLink], scheme)
				}
			}
			delete(ld.LinksMappings[WhiteLink], linkId)
			ld.LatestUpdate = time.Now().Unix()
		}
	}
	if len(result) > 0 {
		return result
	}
	return nil
}

func (ld *LinksData) DownLinks(linkType string, linkIds []string) map[string][]string {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	if !slices.Contains([]string{WhiteLink, ReleaseLink}, linkType) {
		sugared.Errorw("Invalid LinkType", "linkType", linkType)
		return nil
	}
	result := make(map[string][]string)
	for _, linkId := range linkIds {
		if _, ok := result[linkId]; !ok {
			result[linkId] = make([]string, 0)
		}
		if _, ok := ld.LinksMappings[linkType][linkId]; ok {
			for scheme, link := range ld.LinksParams[linkType] {
				if link == linkId {
					result[linkId] = append(result[linkId], scheme)
					delete(ld.LinksParams[linkType], scheme)
				}
			}
			delete(ld.LinksMappings[linkType], linkId)
			ld.LatestUpdate = time.Now().Unix()
		}
	}
	if len(result) > 0 {
		return result
	}
	return nil
}

// DownLink 线路下线
func (ld *LinksData) DownLink(exps []string) (map[string]int, error) {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	result := make(map[string]int)
	if releaseLinks, ok := ld.LinksParams[ReleaseLink]; ok && len(releaseLinks) > 0 {
		for _, exp := range exps {
			if _, ok = ld.LinksParams[ReleaseLink][exp]; ok {
				result[exp] = DownLinkSuccess
				delete(ld.LinksParams[ReleaseLink], exp)
				ld.LatestUpdate = time.Now().Unix()
			} else {
				result[exp] = DownLinkNotExist
			}
		}
		return result, nil
	} else {
		return nil, fmt.Errorf("正式线路为空")
	}
}

func (ld *LinksData) DownGlobalWhiteLink() {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	ld.GlobalParams[WhiteLink] = LinkParams{}
	ld.LatestUpdate = time.Now().Unix()
}

// Publish 发布链路数据
func (ld *LinksData) Publish(isForce bool) error {
	ld.mux.Lock()
	defer ld.mux.Unlock()
	if !isForce && ld.LatestUpdate <= ld.LatestPublish {
		return fmt.Errorf("当前无更新，无需发布")
	}
	err := uploadLinksData(ld.redisClient, ld, ld.env)
	if err != nil {
		return err
	}
	now := time.Now()
	ld.LatestPublish = now.Unix()
	local, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return err
	}
	ld.PublishVersion = now.In(local).Format("20060102.15.04")

	return nil
}

func (ld *LinksData) getLink0(isWhite bool) *LinkParams {
	if isWhite {
		if val, ok := ld.LinksMappings[WhiteLink][LinkParams0]; ok {
			return &val
		}
	}
	if val, ok := ld.LinksMappings[ReleaseLink][LinkParams0]; ok {
		return &val
	}
	return nil
}

func (ld *LinksData) GetLinK(gameRequest GameInitRequest, scheme string) (lp *LinkParams, err error) {
	ld.mux.RLock()
	defer ld.mux.RUnlock()
	isWhite := false
	// 白名单优先权 优先拿白名单数据，没有，那线上release数据，链路参数和全局参数都走这个规则

	if len(ld.WhiteList) > 0 && slices.Contains(ld.WhiteList, gameRequest.DeviceId) {
		isWhite = true
	}
	sugared.Infow("GetLink", "uid", gameRequest.DeviceId, "deviceId", gameRequest.DeviceId, "scheme", scheme, "isWhite", isWhite)
	// 获取自定配置，主要用以非生产环境
	lp = ld.getCustom(isWhite, gameRequest.CustomPlan, gameRequest.CustomLine)
	if lp == nil && scheme != "" {
		if isWhite && len(ld.LinksParams[WhiteLink]) > 0 {
			if linkId, ok := ld.LinksParams[WhiteLink][scheme]; ok {
				if linkParams, ok := ld.LinksMappings[WhiteLink][linkId]; ok {
					lp = &linkParams
					sugared.Infow("WhiteLinkParams", "LinkId", linkId, "Params", lp)
				}
			}
		}

		if lp == nil {
			if linkId, ok := ld.LinksParams[ReleaseLink][scheme]; ok {
				if linkParams, ok := ld.LinksMappings[ReleaseLink][linkId]; ok {
					lp = &linkParams
					sugared.Infow("ReleaseLinkParams", "LinkId", linkId, "Params", lp)
				}
			}
		}

	}

	if lp == nil {
		lp = ld.getLink0(isWhite)
		if lp != nil {
			sugared.Infow("LinkParam0", "Params", lp)
		}
	}

	sugared.Infof("Scheme: %s, LinkPrams:%+v", scheme, lp)

	// 全局参数 白名单找不到线路，取线上线路
	var globalParams *LinkParams
	if isWhite {
		if glp, ok := ld.GlobalParams[WhiteLink]; ok && glp.CustomParams != nil {
			globalParams = &glp
		}
	}
	if globalParams == nil {
		if glp, ok := ld.GlobalParams[ReleaseLink]; ok {
			globalParams = &glp
		}
	}
	// 合并数据
	if lp == nil {
		sugared.Errorw("linkParams is nil", "uid", gameRequest.Uid, "scheme", scheme, "isWhite", isWhite)
		if globalParams != nil {
			return globalParams, nil
		} else {
			return nil, fmt.Errorf("no linkParams")
		}
	} else {
		if globalParams != nil {
			lpDst := &LinkParams{
				CustomParams: maps.Clone(globalParams.CustomParams),
				Link:         lp.Link,
				Urls:         slices.Clone(lp.Urls),
				Version:      lp.Version,
			}
			for k, v := range lp.CustomParams {
				lpDst.CustomParams[k] = v
			}
			return lpDst, nil
		} else {
			return lp, nil
		}
	}
}

// getCustom 获取自定配置 用以非生产环境
func (ld *LinksData) getCustom(isWhite bool, customPlan, customLine string) *LinkParams {
	if os.Getenv("ENV") == "prod" {
		return nil
	}
	if customPlan == "" && customLine == "" {
		return nil
	}
	if isWhite {
		if val, ok := ld.LinksParams[WhiteLink][customPlan]; ok {
			if linkParams, ok := ld.LinksMappings[WhiteLink][val]; ok {
				return &linkParams
			}
		}
		if val, ok := ld.LinksMappings[WhiteLink][customLine]; ok {
			return &val
		}
	}
	if val, ok := ld.LinksParams[ReleaseLink][customPlan]; ok {
		if linkParams, ok := ld.LinksMappings[ReleaseLink][val]; ok {
			return &linkParams
		}

	}
	if linksParam, ok := ld.LinksMappings[ReleaseLink][customLine]; ok {
		return &linksParam
	}
	return nil
}

// Get 获取当前链路配置
func (ld *LinksData) Get() ([]byte, error) {
	body, err := json.Marshal(ld)
	if err != nil {
		return nil, err
	}
	return body, nil
}

type MultiLink struct {
	//whiteList           WhiteList
	//releaseGlobalParams LinkParams
	//whiteGlobalPrams    LinkParams
	//whiteLinks          SchemeLink
	//releaseLinks        SchemeLink
	//defaultLink         LinkParams

	env           string
	linksData     *LinksData
	linksDataHash string
	bundleId      string

	mux *sync.RWMutex
}

func NewMultiLink(bundleId, env string, redisClient cache.RedisClient) *MultiLink {
	linksData := NewLinksData(redisClient, bundleId, env)
	ml := &MultiLink{
		linksData: linksData,
		bundleId:  bundleId,
		env:       env,
	}
	return ml
}

func (ml *MultiLink) GetBundleId() string {
	return ml.bundleId
}

// Update 更新链路数据
func (ml *MultiLink) Update(ctx context.Context) error {
	ml.linksData.Update(ctx)
	return nil
}

// GetLink 获取方案链路
// uid 用来判断白名单用户，如果是白名单用户则获取白名单链路
// scheme 指定方案如果没有配置链路，则获取默认链路
func (ml *MultiLink) GetLink(scheme string, gameRequest GameInitRequest) (*LinkParams, error) {
	return ml.linksData.GetLinK(gameRequest, scheme)
}
