package testergame

import (
	"fmt"
	"hungrystudio.com/core/encrypt"
	"os"
	"strconv"
	"strings"
	"time"
)

type SecretData struct {
	RequestId  int64  `json:"reqId"`
	BundleId   string `json:"bundle"`
	Uid        string `json:"uid"`
	Bucket     int    `json:"bucket"`
	Experiment string `json:"exp"`
	Timestamp  int64  `json:"ts"`
	Hostname   string `json:"hostname"`
	SIndex     string `json:"sindex"`
}

var secretBundleMapping = map[string]int{
	"com.block.juggle":       1,
	"com.blockpuzzle.us.ios": 2,
}

func MakeServerSecret(key, bundleId, uid, exp string, reqId int64, bucketId int) (string, error) {
	hostname := ""
	hostnameSlices := strings.Split(os.Getenv("EXT_HOSTNAME"), ".")
	if len(hostnameSlices) > 0 {
		hostname = hostnameSlices[0]
	}
	sIndex := os.Getenv("SINDEX")
	data := fmt.Sprintf("%d,%d,%s,%d,%s,%d,%s,%s", reqId, secretBundleMapping[bundleId], uid, bucketId, exp, time.Now().UnixMilli(), hostname, sIndex)
	return encrypt.AesGCMEncrypt([]byte(data), []byte(key))
}

func DecryptSecretData(key, innerText string) (*SecretData, error) {
	if sourceData, err := encrypt.AesGCMDecrypt(innerText, []byte(key)); err == nil {
		sourceSlices := strings.Split(string(sourceData), ",")
		reqId, _ := strconv.ParseInt(sourceSlices[0], 10, 64)
		bundleId, _ := strconv.Atoi(sourceSlices[1])
		bundle := ""
		for k, v := range secretBundleMapping {
			if v == bundleId {
				bundle = k
			}
		}
		uid := sourceSlices[2]
		bucketId, _ := strconv.Atoi(sourceSlices[3])
		exp := sourceSlices[4]
		ts, _ := strconv.ParseInt(sourceSlices[5], 10, 64)
		hostname := sourceSlices[6]
		sindex := sourceSlices[7]
		return &SecretData{
			RequestId:  reqId,
			BundleId:   bundle,
			Uid:        uid,
			Bucket:     bucketId,
			Experiment: exp,
			Timestamp:  ts,
			Hostname:   hostname,
			SIndex:     sindex,
		}, nil
	} else {
		return nil, err
	}
}
