package testergame

type GameInitRequest struct {
	RequestId        int64          `json:"requestId"`
	Uid              string         `json:"uid"`
	DeviceId         string         `json:"deviceId"`
	InstallTime      int64          `json:"installTime"`
	GameVersion      string         `json:"resVersion"`
	SdkVersion       string         `json:"sdkVersion"`
	BundleId         string         `json:"bundleId"`
	UserType         string         `json:"userType"`
	GameWayNum       string         `json:"gameWayNum"`
	IsClientPanel    bool           `json:"isClientPanel"`
	OldBucket        int            `json:"oldBucket"`
	IsUpgrade        bool           `json:"isUpgrade"`
	Country          string         `json:"country"`
	CsrParams        map[string]any `json:"csrParams"`
	CustomPlan       string         `json:"customPlan"`
	CustomLine       string         `json:"customLine"`
	UserExperiment   *UserExperiment
	OneWayReleaseTag int64 `json:"oneWayReleaseTag"`
	LayerIds         []int `json:"layer_ids"`
}

func (req GameInitRequest) GetBucketEntryConditions() map[string]any {
	return map[string]any{
		"resVersion":       req.GameVersion,
		"sdkVersion":       req.SdkVersion,
		"country":          req.Country,
		"oneWayReleaseTag": req.OneWayReleaseTag,
	}
}

type ExperimentResult struct {
	RequestId        int64              `json:"requestId"`
	Uid              string             `json:"uid"`
	BundleId         string             `json:"bundleId"`
	GameWayNum       string             `json:"gameWayNum"`
	BucketId         int                `json:"bucketId"`
	ExperimentType   int                `json:"experimentType"`
	ExperimentConfig map[string]any     `json:"configData"`
	CSRIndentity     string             `json:"csrIndentity,omitempty"`
	CSRParamOrigin   CSRParamOriginType `json:"csrParamOrigin,omitempty"`
	ABTestResult     []map[string]any   `json:"abtestResult,omitempty"`
	LinkData         *LinkParams        `json:"linkData,omitempty"`
}
