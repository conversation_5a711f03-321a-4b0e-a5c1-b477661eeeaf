package testergame

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"hungrystudio.com/core/cache"
	"xorm.io/xorm"

	"github.com/twmb/murmur3"
	"hungrystudio.com/datatester/metrics"
)

const UserExperimentCacheKey string = "hs:gametester:userexperiment:%s:%s"
const UserExpiration = 24 * time.Hour * 7
const GtUserExperimentsTableName = "gt_user_experiments_%d"
const GtUserTabelCount = 64
const UserWhiteListCacheKey string = "hs:gametester:userwhitelist"

type UserExperiment struct {
	BucketId       int    `json:"bucketId"`
	ExperimentId   string `json:"experimentId"`
	ExperimentType int    `json:"experimentType"`
	DistributeTime int64  `json:"distributeTime"`
}

type GtUserExperiments struct {
	BundleId       string `xorm:"'bundle_id' not null pk comment('包名') VARCHAR(128)"`
	Uid            string `xorm:"'uid' not null pk comment('用户访客ID') VARCHAR(64)"`
	BucketId       int    `xorm:"'bucket_id' not null comment('桶ID') INT"`
	ExperimentId   string `xorm:"'experiment_id' not null comment('实验ID') VARCHAR(32)"`
	ExperimentType int    `xorm:"'experiment_type' not null comment('实验类型') INT"`
	DistributeTime int64  `xorm:"'distribute_time' not null comment('分配时间') BIGINT"`
}

func (ue *UserExperiment) GetUser(bundleId, distinctId string) (UserExperiment, bool) {
	metrics.GameTesterCacheStatics.WithLabelValues("get").Inc()
	cacheKey := ue.cacheKey(bundleId, distinctId)
	startTime := time.Now().UnixNano()
	ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*50)
	defer cancel()
	userJson, err := redisClient.Get(ctx, cacheKey).Result()
	useTimes := time.Now().UnixNano() - startTime
	if useTimes >= int64(time.Second) {
		sugared.Errorf("GetUser useTimes: %d ns", useTimes)
		metrics.GameTesterDBExecTime.WithLabelValues("GetUser").Inc()
	}
	if err != nil {
		//if errors.Is(err, redis.Nil) {
		//	if userExp, ok := ue.GetUserFromDB(bundleId, distinctId); ok {
		//		return userExp, true
		//	}
		//}
		if userExp, ok := ue.GetUserFromDB(bundleId, distinctId); ok {
			return userExp, true
		}
		sugared.Errorf("redisClient.Get error: %v", err)
		return UserExperiment{}, false
	}
	var user UserExperiment
	err = json.Unmarshal([]byte(userJson), &user)
	if err != nil {
		sugared.Errorf("json.Unmarshal error: %v", err)
		return UserExperiment{}, false
	}
	go func() {
		ctxExpire, cancelExpire := context.WithTimeout(context.Background(), time.Second*2)
		defer cancelExpire()
		err = redisClient.Expire(ctxExpire, cacheKey, UserExpiration).Err()
		if err != nil {
			sugared.Errorf("redisClient.Expire error: %v", err)
		}
		sugared.Infof("GetUser, uid: %s, GetUser: %v", distinctId, user)
	}()
	return user, true
}

func (ue *UserExperiment) GetUserFromDB(bundleId, distinctId string) (UserExperiment, bool) {
	if gameTesterDBEngine == nil {
		return UserExperiment{}, false
	}
	entity := GtUserExperiments{}
	tableName := ue.tableName(distinctId)
	sugared.Infof("GetUserFromDB, uid: %s, tableName: %s", distinctId, tableName)
	startSelect := time.Now().UnixNano()
	ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*200)
	defer cancel()
	session := gameTesterDBEngine.Context(ctx)
	ok, err := session.Where("bundle_id = ? AND uid = ?", bundleId, distinctId).Table(tableName).Get(&entity)
	useTimes := time.Now().UnixNano() - startSelect
	if useTimes >= int64(time.Second) {
		sugared.Errorf("GetUserFromDB, uid: %s, useTimes: %ds", distinctId, useTimes)
		metrics.GameTesterDBExecTime.WithLabelValues("select").Inc()
	}
	if err != nil {
		sugared.Errorf("GetUserFromDB, uid: %s, error: %v", distinctId, err)
		return UserExperiment{}, false
	}

	if !ok {
		return UserExperiment{}, false
	}
	sugared.Infof("GetUserFromDB, uid: %s, entity: %+v", distinctId, entity)
	metrics.GameTesterDBStatics.WithLabelValues("select").Inc()
	return UserExperiment{
		BucketId:       entity.BucketId,
		ExperimentId:   entity.ExperimentId,
		ExperimentType: entity.ExperimentType,
		DistributeTime: entity.DistributeTime,
	}, true
}

func (ue *UserExperiment) tableName(distinctId string) string {
	return fmt.Sprintf(GtUserExperimentsTableName, ue.tableIndex(distinctId, GtUserTabelCount))
}

func (ue *UserExperiment) SaveUserToDB(bundleId, distinctId string, user UserExperiment) error {
	defer func() {
		if r := recover(); r != nil {
			sugared.Errorf("panic err: %v", r)
		}
	}()
	if gameTesterDBEngine == nil {
		return errors.New("gameTesterDBEngine is nil")
	}
	entity := GtUserExperiments{
		BundleId:       bundleId,
		Uid:            distinctId,
		BucketId:       user.BucketId,
		ExperimentId:   user.ExperimentId,
		ExperimentType: user.ExperimentType,
		DistributeTime: user.DistributeTime,
	}
	tableName := ue.tableName(distinctId)
	sugared.Infof("SaveUserToDB, tableName: %s,entity:%+v", tableName, entity)
	ctx1, cancel1 := context.WithTimeout(context.Background(), time.Second)
	defer cancel1()
	hasN, err := gameTesterDBEngine.Context(ctx1).Where("bundle_id = ? AND uid = ?", bundleId, distinctId).Table(tableName).Count(&GtUserExperiments{})
	metrics.GameTesterDBStatics.WithLabelValues("count").Inc()
	if hasN > 0 && err == nil {
		ctx2, cancel2 := context.WithTimeout(context.Background(), time.Second)
		defer cancel2()
		n, err := gameTesterDBEngine.Context(ctx2).Where("bundle_id = ? AND uid = ?", bundleId, distinctId).Table(tableName).Update(entity)
		if err != nil {
			sugared.Errorf("SaveUserToDB update error: %v", err)
			return err
		}
		metrics.GameTesterDBStatics.WithLabelValues("update").Inc()
		sugared.Infof("SaveUserToDB, tableName: %s,entity:%+v, update: %d", tableName, entity, n)
		return nil
	}
	ctx3, cancel3 := context.WithTimeout(context.Background(), time.Second)
	defer cancel3()
	_, err = gameTesterDBEngine.Context(ctx3).Table(tableName).Insert(&entity)
	if err != nil {
		sugared.Errorf("SaveUserToDB error: %v", err)
		return err
	}
	metrics.GameTesterDBStatics.WithLabelValues("insert").Inc()
	return nil
}

func GetUserTableIndex(distinctId string, tableCount int) int {
	hash := murmur3.Sum32([]byte(distinctId))
	return int(hash) % tableCount
}

func (ue *UserExperiment) tableIndex(distinctId string, tableCount int) int {
	hash := murmur3.Sum32([]byte(distinctId))
	return int(hash) % tableCount
}

func (ue *UserExperiment) SaveUser(bundleId, distinctId string, user UserExperiment) {
	go func() {
		_ = ue.SaveUserToDB(bundleId, distinctId, user)
		sugared.Infof("SaveUser, uid: %s", distinctId)
	}()
	userJson, err := json.Marshal(user)
	if err != nil {
		sugared.Errorf("json.Marshal error: %v", err)
		return
	}
	metrics.GameTesterCacheStatics.WithLabelValues("set").Inc()
	cacheKey := ue.cacheKey(bundleId, distinctId)
	ctx0, cancel := context.WithTimeout(context.Background(), time.Second/10)
	defer cancel()
	_, err = redisClient.Set(ctx0, cacheKey, string(userJson), UserExpiration).Result()
	if err != nil {
		sugared.Errorf("redisClient.Set error: %v", err)
	}
	if backupRedisClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
		defer cancel()
		err = backupRedisClient.Set(ctx, cacheKey, string(userJson), UserExpiration).Err()
		if err != nil {
			sugared.Errorf("redisClient.Set error: %v", err)
		}
	}
}

func (ue *UserExperiment) SaveUserToBackupCache(bundleId, distinctId string, user UserExperiment) error {
	userJson, err := json.Marshal(user)
	if err != nil {
		sugared.Errorf("json.Marshal error: %v", err)
		return err
	}
	cacheKey := ue.cacheKey(bundleId, distinctId)
	if backupRedisClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
		defer cancel()
		_, err = backupRedisClient.Set(ctx, cacheKey, string(userJson), UserExpiration).Result()
		if err != nil {
			sugared.Errorf("redisClient.Set error: %v", err)
			return err
		}
	}
	return nil
}

func (ue *UserExperiment) cacheKey(bundleId, distinctId string) string {
	return fmt.Sprintf(UserExperimentCacheKey, bundleId, distinctId)
}

func userExperimenttableName(distinctId string) string {
	return fmt.Sprintf(GtUserExperimentsTableName, GetUserTableIndex(distinctId, GtUserTabelCount))
}

func userExperimentCacheKey(bundleId, distinctId string) string {
	return fmt.Sprintf(UserExperimentCacheKey, bundleId, distinctId)
}

func SaveUser(mysqlDB xorm.EngineInterface, redis cache.RedisClient, bundleId, uid string, user UserExperiment) error {
	entity := GtUserExperiments{
		BundleId:       bundleId,
		Uid:            uid,
		BucketId:       user.BucketId,
		ExperimentId:   user.ExperimentId,
		ExperimentType: user.ExperimentType,
		DistributeTime: user.DistributeTime,
	}
	tableName := userExperimenttableName(uid)
	count, err := mysqlDB.Table(tableName).Where("bundle_id=? AND uid=?", bundleId, uid).Count()
	if err != nil {
		sugared.Errorf("mysqlDB.Table(tableName) error: %v", err)
		return err
	}
	if count == 0 {
		_, err = mysqlDB.Table(tableName).Insert(&entity)
		if err != nil {
			sugared.Errorf("SaveUser to mysql error: %v", err)
			return err
		}
	} else {
		_, err = mysqlDB.Table(tableName).Where("bundle_id=? AND uid=?", bundleId, uid).Update(entity)
		if err != nil {
			sugared.Errorf("SaveUser to mysql error: %v", err)
			return err
		}
	}

	cacheKey := userExperimentCacheKey(bundleId, uid)
	data, err := json.Marshal(user)
	if err != nil {
		sugared.Errorf("json.Marshal error: %v", err)
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	_, err = redis.Set(ctx, cacheKey, string(data), UserExpiration).Result()
	if err != nil {
		sugared.Errorf("redisClient.Set error: %v", err)
		return err
	}
	return nil
}
