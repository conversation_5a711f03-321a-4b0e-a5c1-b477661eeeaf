package testergame

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
)

func ExecHttpRequest(apiUrl string, method string, requestData []byte) ([]byte, error) {
	req, err := http.NewRequest(method, apiUrl, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status code: %d", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}
