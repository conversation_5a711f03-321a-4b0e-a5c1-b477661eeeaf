package testergame

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"strconv"
	"time"
)

// BucketsFlow Bucket流量控制
type BucketsFlow struct {
}

const BucketCacheKey = "hs:gametester:bucketcahe:%s"
const BucketCacheExpiration time.Duration = 24 * time.Hour * 7 * 365
const BucketFlowsMonintorCacheKey = "hs:gametester:bucketflowmonitor:%s:%d"
const HourBucketFlowsMonintorCacheKey = "hs:gametester:hourbucketflowmonitor:%s:%d:%s"

const BucketFlowHourCacheKey = "hs:gametester:bucketflowhour:%s:%s"

const (
	BucketFlowTypeTotal string = "total"
	BucketFlowTypeHour  string = "hour"
)

func NewBucketsFlow() *BucketsFlow {
	return &BucketsFlow{}
}

func (bf *BucketsFlow) Get(bundleId string, bucketId int) int {
	//bf.mux.RLock()
	//defer bf.mux.RUnlock()
	return bf.getFromCache(bundleId, bucketId)
}

func (bf *BucketsFlow) GetWithFlowType(flowType string, bundleId string, bucketId int) (int, error) {
	key, expiration, err := bf.getKeyAndExpirationWithFlowType(flowType, bundleId)
	if err != nil {
		return 0, err
	}
	field := strconv.Itoa(bucketId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	countStr, err := redisClient.HGet(ctx, key, field).Result()
	if err != nil {
		sugared.Errorf("redis hget error: %v", err)
		redisClient.HSet(context.Background(), key, field, 0)
		return 0, err
	}
	count, err := strconv.Atoi(countStr)
	if err != nil {
		sugared.Errorf("redisClient.HGet error: %v", err)
		return 0, err
	}
	go func() {
		ctxExpire, cancelExpire := context.WithTimeout(context.Background(), time.Second*2)
		defer cancelExpire()
		_, err = redisClient.Expire(ctxExpire, key, expiration).Result()
		if err != nil {
			sugared.Errorf("redisClient.Expire error: %v", err)
		}
	}()
	return count, nil
}

func (bf *BucketsFlow) GetBundle(bundleId string) map[int]int {
	//bf.mux.RLock()
	//defer bf.mux.RUnlock()
	return bf.getFromCacheWithBundle(bundleId)
}

func (bf *BucketsFlow) Remove(bucket int) {
	//bf.mux.Lock()
	//defer bf.mux.Unlock()
	//delete(bf.configs, bucket)
}

func (bf *BucketsFlow) cacheKey(bundleId string) string {
	return fmt.Sprintf(BucketCacheKey, bundleId)
}

func (bf *BucketsFlow) flowHourCacheKey(bundleId string) string {
	hourStr := time.Now().Format("2006010215")
	return fmt.Sprintf(BucketFlowHourCacheKey, bundleId, hourStr)
}

func (bf *BucketsFlow) monitorFlowsCacheKey(bundleId string, bucketId int, isHourFlow bool) string {
	if isHourFlow {
		return fmt.Sprintf(HourBucketFlowsMonintorCacheKey, bundleId, bucketId, time.Now().Format("2006010215"))
	}
	return fmt.Sprintf(BucketFlowsMonintorCacheKey, bundleId, bucketId)
}

func (bf *BucketsFlow) getFromCache(bundleId string, bucketId int) int {
	key := bf.cacheKey(bundleId)
	field := strconv.Itoa(bucketId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	countStr, err := redisClient.HGet(ctx, key, field).Result()
	if err != nil {
		sugared.Errorf("redis hget error: %v", err)
		redisClient.HSet(context.Background(), key, field, 0)
		return 0
	}
	count, err := strconv.Atoi(countStr)
	if err != nil {
		sugared.Errorf("redisClient.HGet error: %v", err)
		return 0
	}
	go func() {
		ctxExpire, cancelExpire := context.WithTimeout(context.Background(), time.Second*2)
		defer cancelExpire()
		_, err = redisClient.Expire(ctxExpire, key, BucketCacheExpiration).Result()
		if err != nil {
			sugared.Errorf("redisClient.Expire error: %v", err)
		}
	}()
	return count
}

func (bf *BucketsFlow) getFromCacheWithBundle(bundleId string) map[int]int {
	key := bf.cacheKey(bundleId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	bundleFlowsStr, err := redisClient.HGetAll(ctx, key).Result()
	if err != nil {
		sugared.Errorf("redis hgetall error: %v", err)
		return nil
	}
	bundleFlows := make(map[int]int)
	for bucketIdStr, flowStr := range bundleFlowsStr {
		bucketId, err := strconv.Atoi(bucketIdStr)
		if err != nil {
			sugared.Errorf("redis hgetall error: %v", err)
			continue
		}
		flowCount, err := strconv.Atoi(flowStr)
		if err != nil {
			sugared.Errorf("redis hgetall error: %v", err)
			continue
		}
		bundleFlows[bucketId] = flowCount
	}

	return bundleFlows
}

func (bf *BucketsFlow) getKeyAndExpirationWithFlowType(flowType, bundleId string) (string, time.Duration, error) {
	var key string
	var expiration time.Duration
	switch flowType {
	case BucketFlowTypeTotal:
		key = bf.cacheKey(bundleId)
		expiration = BucketCacheExpiration
	case BucketFlowTypeHour:
		key = bf.flowHourCacheKey(bundleId)
		expiration = time.Hour + time.Minute*5
	default:
		sugared.Errorf("invalid bucket flow type: %s", flowType)
		return "", 0, errors.New("invalid bucket flow type: " + flowType)
	}
	return key, expiration, nil
}

func (bf *BucketsFlow) Increase(bundleId string, bucket int) {
	//bf.mux.Lock()
	go bf.increaseBucketFlow(BucketFlowTypeTotal, bundleId, bucket)
	go bf.increaseBucketFlow(BucketFlowTypeHour, bundleId, bucket)
}

func (bf *BucketsFlow) increaseBucketFlow(flowType string, bundleId string, bucket int) {
	key, expiration, err := bf.getKeyAndExpirationWithFlowType(flowType, bundleId)
	if err != nil {
		return
	}
	field := strconv.Itoa(bucket)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	_, err = redisClient.HIncrBy(ctx, key, field, 1).Result()
	if err != nil {
		sugared.Errorf("redisClient.HIncrBy error: %v", err)
		return
	}
	_, err = redisClient.Expire(ctx, key, expiration).Result()
	if err != nil {
		sugared.Errorf("redisClient.Expire error: %v", err)
	}
}

func (bf *BucketsFlow) Minus(bundleId string, bucket int) {
	go bf.minusBucketFlow(BucketFlowTypeTotal, bundleId, bucket)
	go bf.minusBucketFlow(BucketFlowTypeHour, bundleId, bucket)
}

func (bf *BucketsFlow) minusBucketFlow(flowType, bundleId string, bucket int) {
	key, expiration, err := bf.getKeyAndExpirationWithFlowType(flowType, bundleId)
	if err != nil {
		return
	}
	field := strconv.Itoa(bucket)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	cCount, err := redisClient.HGet(ctx, key, field).Int()
	if err == nil && cCount > 0 {
		_, err = redisClient.HIncrBy(ctx, key, field, -1).Result()
		if err != nil {
			sugared.Errorf("redisClient.HIncrBy error: %v", err)
			return
		}
		_, err = redisClient.Expire(ctx, key, expiration).Result()
		if err != nil {
			sugared.Errorf("redisClient.Expire error: %v", err)
		}
	}
}

type MonitorBucketMax struct {
	Max    int   `json:"max"`
	Count  int   `json:"count"`
	Update int64 `json:"update"`
}

func (bf *BucketsFlow) getMonitorKeyAndExpirationWithFlowType(flowType, bundleId string, bucketId int) (string, time.Duration, error) {
	var key string
	var expiration time.Duration
	switch flowType {
	case BucketFlowTypeTotal:
		key = fmt.Sprintf(BucketFlowsMonintorCacheKey, bundleId, bucketId)
		expiration = BucketCacheExpiration
	case BucketFlowTypeHour:
		key = fmt.Sprintf(HourBucketFlowsMonintorCacheKey, bundleId, bucketId, time.Now().UTC().Format("2006010215"))
		expiration = time.Hour + time.Minute*5
	default:
		sugared.Errorf("invalid bucket flow type: %s", flowType)
		return "", 0, errors.New("invalid bucket flow type: " + flowType)
	}
	return key, expiration, nil
}

func (bf *BucketsFlow) SetMonitorMax(bundleId string, bucket, max int, flowType string) (int64, error) {
	count, err := bf.GetWithFlowType(flowType, bundleId, bucket)
	if err != nil {
		sugared.Errorf("bf.GetWithFlowType error: %v", err)
		return 0, err
	}
	key, expiration, err := bf.getMonitorKeyAndExpirationWithFlowType(flowType, bundleId, bucket)
	if err != nil {
		return 0, err
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	// 为了测试，暂时先删除
	//sugared.Infof("Del MonitorMax Key : %s", key)
	//redisClient.Del(ctx, key)
	//return 0, nil
	now := time.Now().UnixMilli()
	value, err := redisClient.Get(ctx, key).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			if count >= max {
				mbm := MonitorBucketMax{Max: max, Count: count, Update: now}
				jsonValue, _ := json.Marshal(mbm)
				_, err = redisClient.Set(ctx, key, jsonValue, expiration).Result()
				if err != nil {
					sugared.Errorf("redisClient.Set error: %v", err)
				}
				return now, nil
			}

		}
		return 0, err
	}
	mbm := MonitorBucketMax{}
	err = json.Unmarshal(value, &mbm)
	if err != nil {
		sugared.Errorf("json.Unmarshal error: %v", err)
		return 0, err
	}
	if max > mbm.Max && count > max {
		mbm.Max = max
		mbm.Count = count
		mbm.Update = now
		jsonValue, _ := json.Marshal(mbm)
		_, err = redisClient.Set(ctx, key, jsonValue, expiration).Result()
		if err != nil {
			if !errors.Is(redis.Nil, err) {
				sugared.Errorf("redisClient.Set error: %v", err)
			}
			return 0, err
		}
		return now, nil
	}
	return 0, nil
}

func (bf *BucketsFlow) GetMonitorMax(buckets []BucketConfig) map[int]MonitorBucketMax {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	result := make(map[int]MonitorBucketMax)
	for _, bucket := range buckets {
		key := bf.monitorFlowsCacheKey(bucket.BundleId, bucket.BucketId, false)
		mbm := MonitorBucketMax{}
		value, err := redisClient.Get(ctx, key).Bytes()
		if err != nil {
			if !errors.Is(err, redis.Nil) {
				sugared.Errorf("redisClient.Get error: %v", err)
			}
			continue
		}
		err = json.Unmarshal(value, &mbm)
		if err != nil {
			sugared.Errorf("json.Unmarshal error: %v", err)
			continue
		}
		result[bucket.BucketId] = mbm
	}
	return result
}
