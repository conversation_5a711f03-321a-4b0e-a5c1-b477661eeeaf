package testergame

import (
	"golang.org/x/exp/maps"
	"slices"

	"strconv"
	"strings"
	"sync"
)

type ValidateConfig struct {
	LeastCount    int `yaml:"leastCount" json:"leastCount"`
	MiniDiffCount int `yaml:"miniDiffCount" json:"miniDiffCount"`
}

// 针对特性进行校验
// 1. 桶内每个方案的特性不能低于一定的数量
// 2. 上传新配置和线上配置特性比较
//  1. 上传配置桶在线上配置存在，则对比上传配置桶方案特性与线上配置桶对应的方案相同
//  2. 上传配置桶在线上配置中不存在，则对比线上相邻桶（比上传配置桶-1）的桶的模板方案相同，也就是桶内最小方案的特征
//
// 关于方案ID,有两种形式
//  1. 纯数字，例如：11501，11502，11510
//  2. 下划线链接的数字，例如：142_1,142_10

// ValidateSchemeFeatures 验证配置特征
// 结果具体数据结构参照 FormatValidateResult
func ValidateSchemeFeatures(prodBucketConfigs, upBucketConfigs []BucketConfig, leastFeatureCount, miniDiffCount int) (result map[string]any, valid bool) {
	//featureCountResult := make(map[int]map[string]int)
	lock := sync.Mutex{}
	wg := sync.WaitGroup{}
	wg.Add(1)
	// 验证上传所有方案的特征数量
	go func() {
		defer wg.Done()
		if featureCountResults := ValidateFeatureCount(upBucketConfigs, leastFeatureCount); featureCountResults != nil {
			lock.Lock()
			if result == nil {
				result = make(map[string]any)
			}
			valid = false
			result["featureCountResults"] = featureCountResults
			lock.Unlock()
		}
	}()
	// 验证上传配置和线上配置
	prodBucketConfigMaps := make(map[int]BucketConfig)
	for _, bucketConfig := range prodBucketConfigs {
		prodBucketConfigMaps[bucketConfig.BucketId] = bucketConfig
	}
	//bWg := sync.WaitGroup{}
	sameResults := make(map[int]map[string]bool)
	nearResults := make([]NearResult, 0)
	for _, bucketConfig := range upBucketConfigs {
		// 验证对应桶配置
		if onlineBucketConfig, ok := prodBucketConfigMaps[bucketConfig.BucketId]; ok {
			wg.Add(1)
			go func(nBucket, oBucket BucketConfig) {
				defer wg.Done()
				if sameResult := ValidateSameBucket(nBucket, oBucket); sameResult != nil {
					lock.Lock()
					sameResults[bucketConfig.BucketId] = sameResult
					valid = false
					lock.Unlock()
				}
			}(bucketConfig, onlineBucketConfig)
		} else {
			// 验证相邻桶配置
			if onlineBucketConfig, ok = prodBucketConfigMaps[bucketConfig.BucketId-1]; ok {
				wg.Add(1)
				go func(nBucket, oBucket BucketConfig) {
					defer wg.Done()
					if neareResult, ok := ValidateNearBucket(nBucket, oBucket, miniDiffCount); ok {
						lock.Lock()
						nearResults = append(nearResults, neareResult)
						valid = false
						lock.Unlock()
					}
				}(bucketConfig, onlineBucketConfig)
			} else {
				continue
			}
		}

	}
	wg.Wait()

	if len(sameResults) > 0 {
		result["sameResults"] = sameResults
	}
	if len(nearResults) > 0 {
		result["nearResults"] = nearResults
	}
	return result, valid
}

// FormatValidateResult 格式化验证结果为字符串
func FormatValidateResult(result map[string]any) string {
	builder := strings.Builder{}
	if featureCountResult, ok := result["featureCountResults"]; ok && featureCountResult != nil {
		featureCountResultAny := featureCountResult.(map[int]map[string]int)
		if len(featureCountResultAny) > 0 {
			builder.WriteString("验证特征数结果:\n")
			for bucketId, bucketResultAny := range featureCountResultAny {
				builder.WriteString("\t桶: " + strconv.Itoa(bucketId) + ":\n")
				for expId, count := range bucketResultAny {
					builder.WriteString("\t\t实验: " + expId + ", 特征数:" + strconv.Itoa(count) + "\n")
				}
			}
		}
	}
	if sameResultsAny, ok := result["sameResults"]; ok && sameResultsAny != nil {
		sameResults := sameResultsAny.(map[int]map[string]bool)
		if len(sameResults) > 0 {
			builder.WriteString("验证线上同一桶结果:\n")
			for bucketId, bucketResults := range sameResults {
				builder.WriteString("\t桶: " + strconv.Itoa(bucketId) + ":\n")
				for expId, isSame := range bucketResults {
					builder.WriteString("\t\t实验: " + expId + ", 特征数是否一致:")
					if isSame {
						builder.WriteString("一致")
					} else {
						builder.WriteString("不一致")
					}
					builder.WriteString("\n")
				}
			}
		}
	}
	if nearResultsAny, ok := result["nearResults"]; ok && nearResultsAny != nil {
		nearResults := nearResultsAny.([]NearResult)
		if len(nearResults) > 0 {
			builder.WriteString("验证线上相邻桶结果:\n")
			for _, nearResult := range nearResults {
				builder.WriteString("\t\t上传桶:" + strconv.Itoa(nearResult.OriginBucket) + ",")
				builder.WriteString("实验:" + nearResult.OriginExpId + ",")
				builder.WriteString("与线上桶:" + strconv.Itoa(nearResult.CmpBucket) + ",")
				builder.WriteString("实验:" + nearResult.CmpExpId + ",")
				builder.WriteString("特征差异:" + strconv.Itoa(nearResult.DiffCount) + "\n")
			}
		}
	}
	return builder.String()
}

// ValidateFeatureCount 检查桶实验的特征数量是否满足最小特征数条件
// 返回不满足条件的特征数量：map[bucketId]map[expId]featureCount
func ValidateFeatureCount(bucketConfigs []BucketConfig, leastFeatureCount int) (result map[int]map[string]int) {
	lock := sync.Mutex{}
	bWg := sync.WaitGroup{}
	for _, bucketConfig := range bucketConfigs {
		bWg.Add(1)
		go func(bucketConfig BucketConfig) {
			defer bWg.Done()
			eWg := sync.WaitGroup{}
			for expId, exp := range bucketConfig.ExperimentList {
				eWg.Add(1)
				go func(bucketId int, expId string, exp map[string]any) {
					defer eWg.Done()
					features, ok := exp["features"].([]any)
					if ok {
						lock.Lock()
						featureCount := len(features)
						if featureCount < leastFeatureCount {
							if result == nil {
								result = make(map[int]map[string]int)
							}
							if _, ok = result[bucketId]; !ok {
								result[bucketId] = make(map[string]int)
							}
							result[bucketId][expId] = featureCount
						}
						lock.Unlock()
					}

				}(bucketConfig.BucketId, expId, exp)
			}
			eWg.Wait()
		}(bucketConfig)
	}
	bWg.Wait()
	return result
}

// ValidateSameBucket 验证统一
func ValidateSameBucket(upBucketConfig, onlineBucketConfig BucketConfig) (result map[string]bool) {
	lock := sync.Mutex{}
	wg := sync.WaitGroup{}
	for upExpId, upExp := range upBucketConfig.ExperimentList {
		if onlineExp, ok := onlineBucketConfig.ExperimentList[upExpId]; ok {
			wg.Add(1)
			go func(expId string, nFeatures, oFeatures any) {
				defer wg.Done()
				if !CompareSameFeatures(nFeatures, oFeatures) {
					lock.Lock()
					if result == nil {
						result = make(map[string]bool)
					}
					result[expId] = false
					lock.Unlock()
				}
			}(upExpId, onlineExp["features"], upExp["features"])

		}
	}
	wg.Wait()
	return
}

// CompareSameFeatures 比较特征是否一致
func CompareSameFeatures(newFeatures, oldFeatures any) bool {
	if newFeatures == nil || oldFeatures == nil {
		return true
	}
	newFeaturesAny := newFeatures.([]any)
	oldFeaturesAny := oldFeatures.([]any)
	nLen := len(newFeaturesAny)
	oLen := len(oldFeaturesAny)
	if nLen != oLen {
		return false
	}
	ids := make(map[int]struct{})
	addId := func(featuresAny []any) {
		for _, featureAny := range featuresAny {
			features := featureAny.(map[string]any)
			idFloat := features["id"].(float64)
			idInt := int(idFloat)
			if _, ok := ids[idInt]; !ok {
				ids[idInt] = struct{}{}
			}
		}
	}
	addId(newFeaturesAny)
	addId(oldFeaturesAny)
	return nLen == len(ids)
}

// CompareNearFeatures 比较特征是否一致
func CompareNearFeatures(newFeatures, oldFeatures any) int {
	newFeaturesAny := newFeatures.([]map[string]any)
	oldFeaturesAny := oldFeatures.([]map[string]any)
	nLen := len(newFeaturesAny)
	oLen := len(oldFeaturesAny)
	if nLen != oLen {
		if nLen > oLen {
			return nLen - oLen
		} else {
			return oLen - oLen
		}
	}
	ids := make(map[int]struct{})
	addId := func(featuresAny []map[string]any) {
		for _, featureAny := range featuresAny {
			idFloat := featureAny["id"].(float64)
			idInt := int(idFloat)
			if _, ok := ids[idInt]; !ok {
				ids[idInt] = struct{}{}
			}
		}
	}
	addId(newFeaturesAny)
	addId(oldFeaturesAny)
	return len(ids) - nLen
}

type NearResult struct {
	OriginBucket int    `json:"originBucket"`
	OriginExpId  string `json:"originExpId"`
	CmpBucket    int    `json:"cmpBucket"`
	CmpExpId     string `json:"cmpExpId"`
	DiffCount    int    `json:"diffCount"`
}

// ValidateNearBucket 验证相邻桶
func ValidateNearBucket(upBucketConfig, onlineBucketConfig BucketConfig, miniDiffCount int) (NearResult, bool) {
	upLeastExpId := GetLeastExpId(upBucketConfig.ExperimentList)
	onlineLeastExpId := GetLeastExpId(onlineBucketConfig.ExperimentList)
	upFeaturesAny := upBucketConfig.ExperimentList[upLeastExpId]["features"]
	onlineFeaturesAny := onlineBucketConfig.ExperimentList[onlineLeastExpId]["features"]
	diffCount := CompareNearFeatures(upFeaturesAny, onlineFeaturesAny)
	if diffCount > miniDiffCount {
		return NearResult{
			OriginBucket: upBucketConfig.BucketId,
			OriginExpId:  upLeastExpId,
			CmpBucket:    onlineBucketConfig.BucketId,
			CmpExpId:     onlineLeastExpId,
			DiffCount:    diffCount,
		}, true
	} else {
		return NearResult{}, false
	}
}

// GetLeastExpId 获取最小实验ID
func GetLeastExpId(expList map[string]map[string]any) string {
	if len(expList) == 0 {
		return ""
	}
	keys := maps.Keys(expList)
	slices.SortFunc(keys, func(a, b string) int {
		aInt, err := strconv.Atoi(a)
		if err != nil {
			aInt, _ = strconv.Atoi(strings.Split(a, "_")[1])
			bInt, _ := strconv.Atoi(strings.Split(b, "_")[1])
			return aInt - bInt
		}
		bInt, _ := strconv.Atoi(b)
		return aInt - bInt
	})
	return keys[0]
}
