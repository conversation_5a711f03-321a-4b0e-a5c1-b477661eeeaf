package testergame

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/datatester/models/release"
	"os"
	"strings"
	"sync"
	"time"
)

const (
	OverScoreInterceptorName = "overScore"
)

type InterceptorInterface interface {
	ValidInterceptor(req *GameInitRequest) bool
	ExecRedirectBucket(req *GameInitRequest, bConfigs *BucketConfigs) (BucketConfig, error)
	InterceptorFinish(req *GameInitRequest)
}

type InterceptorConfig struct {
	Name           string           `yaml:"name" json:"name"`
	Enabled        bool             `yaml:"enabled" json:"enabled"`
	Release        *release.Release `yaml:"release" json:"release"`
	RedirectBucket RedirectBucket   `yaml:"redirectBucket" json:"redirectBucket"`
}

type Interceptor struct {
	Name           string           `json:"name"`
	Enabled        bool             `json:"enabled"`
	Release        *release.Release `json:"release,omitempty"` // 条件触发
	RedirectBucket RedirectBucket   `json:"redirectBucket,omitempty"`
	//RedirectApi    RedirectApi      `json:"redirectApi,omitempty"`

	mux sync.Mutex
}

type RedirectBucket struct {
	Bucket     int    `json:"bucket"`
	Experiment string `json:"experiment,omitempty"`
}

type RedirectApi struct {
	ApiUrl string
}

const overScorePrefixCacheKey string = "hs:gametester:interceptor:overScore:%s"

type OverScoreInterceptor struct {
	*Interceptor
	redisClient cache.RedisClient
}

func OverScreCacheKey(bundleId string) string {
	return fmt.Sprintf(overScorePrefixCacheKey, bundleId)
}

func NewOverScoreInterceptor(incepterConfig *InterceptorConfig, redisClient cache.RedisClient) *OverScoreInterceptor {
	return &OverScoreInterceptor{
		Interceptor: &Interceptor{
			Name:           incepterConfig.Name,
			Enabled:        incepterConfig.Enabled,
			Release:        incepterConfig.Release,
			RedirectBucket: incepterConfig.RedirectBucket,
		},
		redisClient: redisClient,
	}
}

func (o *OverScoreInterceptor) ValidInterceptor(req *GameInitRequest) bool {
	//TODO implement me
	if !o.Enabled {
		return false
	}
	if o.redisClient == nil {
		return false
	}
	if req.UserExperiment != nil {
		if req.UserExperiment.BucketId == o.RedirectBucket.Bucket {
			return false
		}
	}
	key := OverScreCacheKey(req.BundleId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	ok, err := o.redisClient.SIsMember(ctx, key, req.Uid).Result()
	if err != nil {
		sugared.Errorf("overScoreInterceptorRedisClient.SIsMember err: %v", err)
		return false
	}
	return ok
}

func (o *OverScoreInterceptor) ExecRedirectBucket(req *GameInitRequest, bConfigs *BucketConfigs) (BucketConfig, error) {
	//TODO implement me
	if o.RedirectBucket.Bucket > 0 {
		if bucketConfig, ok := bConfigs.GetBucketConfig(req.BundleId, o.RedirectBucket.Bucket); ok {
			attr := map[string]any{
				"resVersion": req.GameVersion,
				"sdkVersion": req.SdkVersion,
				"country":    req.Country,
			}
			if bucketConfig.Conditions.EvaluateFilters(attr) {
				return bucketConfig, nil
			}
		}

	}
	return BucketConfig{}, errors.New("overScore interceptor no redirect bucket")
}

func (o *OverScoreInterceptor) InterceptorFinish(req *GameInitRequest) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	key := OverScreCacheKey(req.BundleId)
	_, err := o.redisClient.SRem(ctx, key, req.Uid).Result()
	if err != nil {
		sugared.Errorf("overScoreInterceptorRedisClient.SRem err: %v", err)
	}
	size, err := o.redisClient.SCard(ctx, key).Result()
	if err != nil {
		sugared.Errorf("overScoreInterceptorRedisClient.SCard err: %v", err)
	} else {
		if size == 0 {
			o.mux.Lock()
			o.Enabled = false
			o.mux.Unlock()
		}
	}
}

func PushOverScoreUser(bundleId, filename string, maxCount int, redisClient cache.RedisClient) {
	file, err := os.Open(filename)
	if err != nil {
		panic(err)
	}
	defer file.Close()
	key := OverScreCacheKey(bundleId)
	count := 0
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		line = strings.TrimRight(line, "\t\n")
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
		err = redisClient.SAdd(ctx, key, line).Err()
		if err != nil {
			panic(err)
		}
		cancel()
		fmt.Printf("Push User: %s To Redis %s Set\n", line, key)
		count++
		if maxCount > 0 && count >= maxCount {
			break
		}
	}
	fmt.Printf("Push User Count: %d \n", count)
}
