package testergame

import (
	"gopkg.in/yaml.v2"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/models/distributes"
	"math/rand"
	"slices"
	"sync"
	"testing"
)

type logConfig struct {
	Logger []log.ZapLoggerConfig `yaml:"logger"`
}

var logC = logConfig{}

func init() {
	logConfigYaml := `
logger:
  - level: -1
    lj_logger:
      filename: "./logs/datatester.log"
      maxsize: 500
      maxage: 7
      maxbackups: 10
      localtime: true
      compress: true
  - level: 2
    lj_logger:
      filename: "./logs/datatester-errors.log"
      maxsize: 500
      maxage: 14
      maxbackups: 20
      localtime: true
      compress: true
`
	//logC := logConfig{}
	err := yaml.Unmarshal([]byte(logConfigYaml), &logC)
	if err != nil {
		panic(err)
	}
}

func BenchmarkNewAlgo(b *testing.B) {

	logger := log.NewLogger(logC.Logger)
	sugared := logger.Sugar()
	distributes.SetSugaredLogger(sugared)
	SetSugaredLogger(sugared)
	schems := []distributes.Scheme{
		{
			Name:   "139_1",
			Weight: 0,
		},
		{
			Name:   "139_2",
			Weight: 0,
		},
		{
			Name:   "139_3",
			Weight: 0,
		},
		{
			Name:   "139_4",
			Weight: 0,
		},
		{
			Name:   "139_5",
			Weight: 0,
		},
		{
			Name:   "139_6",
			Weight: 0,
		},
		{
			Name:   "139_7",
			Weight: 0,
		},
		{
			Name:   "139_8",
			Weight: 0,
		},
		{
			Name:   "139_9",
			Weight: 0,
		},
		{
			Name:   "139_10",
			Weight: 0,
		},
		{
			Name:   "139_11",
			Weight: 0,
		},
		{
			Name:   "139_12",
			Weight: 0,
		},
		{
			Name:   "139_13",
			Weight: 0,
		},
		{
			Name:   "139_14",
			Weight: 0,
		},
		{
			Name:   "139_15",
			Weight: 0,
		},
		{
			Name:   "139_16",
			Weight: 0,
		},
		{
			Name:   "139_17",
			Weight: 0,
		},
		{
			Name:   "139_18",
			Weight: 0,
		},
		{
			Name:   "139_19",
			Weight: 0,
		},
		{
			Name:   "139_20",
			Weight: 0,
		},
		{
			Name:   "139_21",
			Weight: 0,
		},
		{
			Name:   "139_22",
			Weight: 0,
		},
		{
			Name:   "139_23",
			Weight: 0,
		},
		{
			Name:   "139_24",
			Weight: 0,
		},
		{
			Name:   "139_25",
			Weight: 0,
		},
		{
			Name:   "139_26",
			Weight: 0,
		},
		{
			Name:   "139_27",
			Weight: 0,
		},
		{
			Name:   "139_28",
			Weight: 0,
		},
		{
			Name:   "139_29",
			Weight: 0,
		},
		{
			Name:   "139_30",
			Weight: 0,
		},
		{
			Name:   "139_31",
			Weight: 0,
		},
		{
			Name:   "139_32",
			Weight: 0,
		},
		{
			Name:   "139_33",
			Weight: 0,
		},
		{
			Name:   "139_34",
			Weight: 0,
		},
		{
			Name:   "139_35",
			Weight: 0,
		},
		{
			Name:   "139_36",
			Weight: 0,
		},
		{
			Name:   "139_37",
			Weight: 0,
		},
		{
			Name:   "139_38",
			Weight: 0,
		},
		{
			Name:   "139_39",
			Weight: 0,
		},
		{
			Name:   "139_40",
			Weight: 0,
		},
		{
			Name:   "139_41",
			Weight: 0,
		},
		{
			Name:   "139_42",
			Weight: 0,
		},
		{
			Name:   "139_43",
			Weight: 0,
		},
		{
			Name:   "139_44",
			Weight: 0,
		},
	}
	keys := []string{"1_1_1_1", "1_1_2_2", "1_1_3_3", "1_1_4_4", "1_1_5_5"}
	statics := make(map[string]map[string]int)
	staticKeys := make(map[string]int)
	provider := NewDistributeAlgoProvider()
	wg := sync.WaitGroup{}
	mu := sync.Mutex{}
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			wg.Add(1)
			key := keys[rand.Intn(len(keys))]
			mu.Lock()
			if _, ok := statics[key]; !ok {
				statics[key] = make(map[string]int)
			}
			algo := provider.GetDistributeAlgo(AlgoTypeGameScheme, key, slices.Clone(schems))
			algo.ResetSchemes(schems)

			scheme := algo.Next()
			staticKeys[scheme.Name]++
			statics[key][scheme.Name]++
			mu.Unlock()

			wg.Done()
		}
	})
	//for i := 0; i < b.N; i++ {
	//	wg.Add(1)
	//	go func(index int) {
	//		//mu.Lock()
	//		//defer mu.Unlock()
	//		algo := provider.GetDistributeAlgo(AlgoTypeGameScheme, "aaaaa", schems)
	//		if index%10 == 0 {
	//			algo.ResetSchemes(schems)
	//		}
	//		scheme := algo.Next()
	//		mu.Lock()
	//		statics[scheme.Name]++
	//		mu.Unlock()
	//
	//		defer wg.Done()
	//		//algo = provider.GetDistributeAlgo(AlgoTypeGameScheme, "aaaaa", schems)
	//
	//	}(i)
	//
	//}
	wg.Wait()
	b.Log("按key统计")
	for key, keyCounts := range statics {
		for name, count := range keyCounts {
			b.Logf("Key: %s,Name: %s, Count: %d\n", key, name, count)
		}
	}
	b.Log("按方案统计")
	for name, count := range staticKeys {
		b.Logf("Name: %s, Count: %d\n", name, count)
	}
}
