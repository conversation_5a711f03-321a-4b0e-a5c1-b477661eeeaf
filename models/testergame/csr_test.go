package testergame

import (
	"hungrystudio.com/core/config"
	"hungrystudio.com/core/uuid"
	"testing"
)

var appConfig TesterTrafficConfig

func init() {
	appConfig = TesterTrafficConfig{}
	err := config.ParseYamlOrJsonFile("../../configs/gametester.json", &appConfig)
	if err != nil {
		panic(err)
	}
}

func TestGetCSRIndentity(t *testing.T) {
	bundleId := "com.block.juggle"
	csrApplyType := CSRApplyTypeNew
	uid := uuid.NewUUID()
	csrParams := map[string]any{
		"ecpm":    0.00011,
		"adnum":   0.21,
		"gamenum": 2.2,
		"aday":    3,
	}
	csr := NewCsr(appConfig.CSR)
	csrIndentity, originType, err := csr.GetCSRIndentity(bundleId, csrApplyType, uid, csrParams)
	if err != nil {
		t.Error(err)
	} else {
		t.Logf("csr indentity: %v, originType: %v", csrIndentity, originType)
	}
}

func BenchmarkGetCSRIndentity(b *testing.B) {
	bundleId := "com.block.juggle"
	csrApplyType := CSRApplyTypeNew
	uid := uuid.NewUUID()
	csrParams := map[string]any{
		"ecpm":    0.0537825,
		"adnum":   405.86,
		"gamenum": 1534.71,
		"aday":    7,
	}
	csr := NewCsr(appConfig.CSR)
	for i := 0; i < b.N; i++ {
		_, _, err := csr.GetCSRIndentity(bundleId, csrApplyType, uid, csrParams)
		if err != nil {
			b.Error(err)
		}
	}
}
