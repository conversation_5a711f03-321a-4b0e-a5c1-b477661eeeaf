package testergame

import (
	"fmt"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/uuid"
	"math/rand"
	"os"
	"testing"
	"time"
)

func init() {
	redisConfig := cache.RedisConfig{
		Addr:      []string{"127.0.0.1:6379"},
		DB:        0,
		IsCluster: false,
	}
	client := cache.NewRedisClient(redisConfig)
	SetRedisClient(client)
	_ = os.Setenv("EXT_HOSTNAME", "test")
	_ = os.Setenv("SINDEX", "1")
}

func TestMakeDistributeLog(t *testing.T) {

	for i := 0; i < 10; i++ {
		uexp := UserExperiment{
			BucketId:       i + 1 + rand.Intn(10),
			ExperimentId:   fmt.Sprintf("154_%d", i+1+rand.Intn(10)),
			ExperimentType: rand.Intn(2),
			DistributeTime: time.Now().UnixMilli(),
		}
		csr := fmt.Sprintf("1_1_%d_%d", i+rand.Intn(10), i+rand.Intn(10))
		AddUserDistributeLog("com.block.juggle", uuid.NewUUID(), csr, uexp)
	}
}
