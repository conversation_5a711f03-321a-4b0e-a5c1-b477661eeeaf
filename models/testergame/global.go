package testergame

import (
	"go.uber.org/zap"
	"hungrystudio.com/core/cache"
	"xorm.io/xorm"
)

var redisClient cache.RedisClient

func SetRedisClient(c cache.RedisClient) {
	redisClient = c
}

var backupRedisClient cache.RedisClient

func SetBackupRedisClient(c cache.RedisClient) {
	backupRedisClient = c
}

var csrRedisClient cache.RedisClient

func SetCsrRedisClient(c cache.RedisClient) {
	csrRedisClient = c
}

var sugared *zap.SugaredLogger

func SetSugaredLogger(l *zap.SugaredLogger) {
	sugared = l
}

var gameTesterDBEngine xorm.EngineInterface

func SetGameTesterDBEngine(e xorm.EngineInterface) {
	gameTesterDBEngine = e
}

const VirtualBucket int = 0
