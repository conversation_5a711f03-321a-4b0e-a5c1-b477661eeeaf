package testergame

import (
	"encoding/json"
	"os"
	"slices"
	"testing"
)

var originBucketConfigs []BucketConfig

func init() {
	jsonFile, err := os.Open("../../data/an_flow.json")
	if err != nil {
		panic(err)
	}
	jsonDecoder := json.NewDecoder(jsonFile)
	if err = jsonDecoder.Decode(&originBucketConfigs); err != nil {
		panic(err)
	}
}

func TestValidateFeatureCount(t *testing.T) {
	result := ValidateFeatureCount(originBucketConfigs, 30)
	t.Logf("result:%+v", result)
}

func makeMockBucketConfigs() []BucketConfig {
	mockBucketConfigs := slices.Clone(originBucketConfigs)
	return mockBucketConfigs
}

func TestValidateSchemeFeatures(t *testing.T) {
	mockBucketConfigs := makeMockBucketConfigs()
	if result, ok := ValidateSchemeFeatures(mockBucketConfigs, originBucketConfigs, 30, 10); !ok {
		t.Log(FormatValidateResult(result))
	}
}
