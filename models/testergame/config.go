package testergame

import "hungrystudio.com/core/config"

type TesterTrafficConfig struct {
	BundleId                  string                     `yaml:"bundleId" json:"bundleId"`
	QA                        bool                       `yaml:"qa" json:"qa"`
	UpdateInterval            int64                      `yaml:"updateInterval" json:"updateInterval"`
	UserRDB                   string                     `yaml:"userRDB" json:"userRDB"`
	UserCache                 string                     `yaml:"userCache" json:"userCache"`
	UseUserBackupCache        bool                       `yaml:"useUserBackupCache" json:"useUserBackupCache"`
	UserBackupCache           string                     `yaml:"userBackupCache" json:"userBackupCache"`
	UseCSR                    bool                       `yaml:"useCSR" json:"useCSR"`
	CSRRedis                  string                     `yaml:"csrRedis" json:"csrRedis"`
	CSR                       map[string]BundleCsrConfig `yaml:"csr" json:"csr"`
	AIFeautures               map[string]AIFeature       `yaml:"aiFeautures" json:"aiFeautures"`
	ABTestEnabled             bool                       `yaml:"abTestEnabled" json:"abTestEnabled"`
	AbtestFeatures            map[string]ABTestFeature   `yaml:"abtestFeatures" json:"abtestFeatures"`
	HttpClientConfig          config.HttpClientConfig    `yaml:"httpClientConfig" json:"httpClientConfig"`
	Dispatch                  Dispatch                   `yaml:"dispatch" json:"dispatch"`
	UseInterceptor            bool                       `yaml:"useInterceptor" json:"useInterceptor"`
	OverScoreInterceptorRedis string                     `yaml:"overScoreInterceptorRedis" json:"overScoreInterceptorRedis"`
	Interceptors              []*InterceptorConfig       `yaml:"interceptors" json:"interceptors"`
	MultiLinkEnabled          bool                       `yaml:"multiLinkEnabled" json:"multiLinkEnabled"`
	MultiLinks                []MultiLinkConfig          `yaml:"multiLinks" json:"multiLinks"`
	ServerSecretKey           map[string]string          `yaml:"serverSecretKey" json:"serverSecretKey"`
}

type AIFeature struct {
	CacheKeyPrefix string `yaml:"cacheKeyPrefix" json:"cacheKeyPrefix"`
}

type ABTestFeature struct {
	ProjectId  int    `yaml:"projectId" json:"projectId"`
	ProjectKey string `yaml:"projectKey" json:"projectKey"`
	APIUrl     string `yaml:"apiUrl" json:"apiUrl"`
	LeaveAPI   string `yaml:"leaveAPI" json:"leaveAPI"`
	Debug      int32
}

const DispatchMod uint32 = 100000

type Dispatch struct {
	Enabled bool   `yaml:"enabled" json:"enabled"`
	Ratio   int    `yaml:"ratio" json:"ratio"`
	Api     string `yaml:"api" json:"api"`
}

type MultiLinkConfig struct {
	BunldeId string `yaml:"bunldeId" json:"bunldeId"`
}
