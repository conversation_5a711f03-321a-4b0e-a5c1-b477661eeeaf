package testergame

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spaolacci/murmur3"
	"regexp"
	"time"
)

const (
	AIFeatureMinus = iota
	AIFeatureAdd
)

type userAICacheData struct {
	AIFeatures []map[string]int `json:"strategy"`
}

func GetUserAIFeatures(uid, cachePrefixKey string) (map[string]int, error) {
	key := cachePrefixKey + uid
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	aiFeatureStr, err := csrRedisClient.Get(ctx, key).Bytes()
	if err != nil {
		sugared.Errorf("redis.Get(%q): %v", key, err)
		return nil, err
	}
	cacheData := userAICacheData{}
	err = json.Unmarshal(aiFeatureStr, &cacheData)
	if err != nil {
		sugared.Errorf("json.Unmarshal: %v", err)
		return nil, err
	}
	aiLen := len(cacheData.AIFeatures)
	if aiLen > 0 {
		// 根据用户hash值取模方式来选择所要进入的特征方案
		hUid := hashUid(uid)
		index := int(hUid % uint64(aiLen))
		return cacheData.AIFeatures[index], nil
		//featuresJson := convertAIFeatureStr(cacheData.AIFeatures[index])
		//featuresJson := cacheData.AIFeatures[index]
		//sugared.Infof("featrueJson: %s", featuresJson)
		//features := make(map[string]int)
		//err = json.Unmarshal([]byte(featuresJson), &features)
		//if err != nil {
		//	sugared.Errorf("index: %d,json.Unmarshal(%s): %v", index, featuresJson, err)
		//	return nil, err
		//}
		//return features, nil
	}

	return nil, fmt.Errorf("user: %s no ai features", uid)
}

func hashUid(uid string) uint64 {
	h := murmur3.New64()
	_, _ = h.Write([]byte(uid))
	return h.Sum64()
}

func convertAIFeatureStr(jsonData string) string {
	re := regexp.MustCompile(`(\b\d*\b):`)
	return re.ReplaceAllString(jsonData, `"$1":`)
}
