package testergame

import (
	"context"
	"encoding/json"
	"fmt"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/datatester/services"
	"os"
	"strconv"
	"sync"
	"time"
	"xorm.io/xorm"
)

type UserDistributes struct {
	Id             int    `xorm:"'id' not null pk autoincr INT" json:"id,omitempty"`
	BundleId       string `xorm:"'bundle_id' comment('包名') index(bundle_buckdet_exp) VARCHAR(64)" json:"bundleId,omitempty"`
	Uid            string `xorm:"'uid' not null VARCHAR(64)" json:"uid,omitempty"`
	BucketId       int    `xorm:"'bucket_id' not null comment('桶ID') index(bundle_buckdet_exp) INT" json:"bucketId,omitempty"`
	Exp            string `xorm:"'exp' not null comment('实验方案') index(bundle_buckdet_exp) VARCHAR(32)" json:"exp,omitempty"`
	ExpType        int    `xorm:"'exp_type' comment('实验类型') TINYINT" json:"expType,omitempty"`
	CsrIndentity   string `xorm:"'csr_indentity' comment('csr分流标识，活跃实验有') VARCHAR(32)" json:"csrIndentity,omitempty"`
	InstallTime    int64  `xorm:"'install_time' not null comment('安装时间') index BIGINT" json:"installTime,omitempty"`
	DistributeTime int64  `xorm:"'distribute_time' comment('方案下发时间') BIGINT" json:"distributeTime,omitempty"`
	DistributeDate int    `xorm:"'distribute_date' not null comment('分配日期') index INT" json:"distributeDate,omitempty"`
	Hostname       string `xorm:"'hostname' comment('主机名称') index(hostname_sindex) VARCHAR(64)" json:"hostname,omitempty"`
	SIndex         int    `xorm:"'s_index' comment('主机服务索引') index(hostname_sindex) INT" json:"SIndex,omitempty"`
}

const UserDistributeCacheListPrefixKey string = "hs:gametester:dslog:%s"

const UserDistributeCreateTableTempalte string = `
CREATE TABLE %s (
  id int NOT NULL AUTO_INCREMENT,
  bundle_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '包名',
  uid varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  bucket_id int NOT NULL COMMENT '桶ID',
  exp varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实验方案',
  exp_type tinyint DEFAULT NULL COMMENT '实验类型',
  csr_indentity varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'csr分流标识，活跃实验有',
  install_time bigint NOT NULL COMMENT '安装时间',
  distribute_time bigint DEFAULT NULL COMMENT '方案下发时间',
  distribute_date int NOT NULL COMMENT '分配日期',
  hostname varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主机名称',
  s_index int DEFAULT NULL COMMENT '主机服务索引',
  PRIMARY KEY (id),
  KEY bundle_buckdet_exp (bundle_id,bucket_id,exp),
  KEY user_distributes_bundle_id_uid_index (bundle_id,uid)
  KEY date (distribute_date),
  KEY hostname_sindex (hostname,s_index),
  KEY install_time (install_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户实验分配日志';
`

func AddUserDistributeLog(bundleId, uid, csrIndentiy string, userExp UserExperiment) {
	sugared.Infof("UserDistributeLog Start Write: bundle(%s),Uid(%s),Csr(%s),UserExp: %+v", bundleId, uid, csrIndentiy, userExp)
	defer func() {
		sugared.Infof("UserDistributeLog Over Write: bundle(%s),Uid(%s)", bundleId, uid)
	}()
	// 测试减去一个月
	now := time.Now()
	dData, _ := strconv.Atoi(now.Format("20060102"))
	sIndexStr := os.Getenv("SINDEX")
	sIndex := 0
	if sIndexStr != "" {
		index, err := strconv.Atoi(sIndexStr)
		if err == nil {
			sIndex = index
		}
	}
	hostname := os.Getenv("EXT_HOSTNAME")
	if hostname == "" {
		sugared.Errorf("env var EXT_HOSTNAME is empty")
	}
	ud := UserDistributes{
		BundleId:       bundleId,
		Uid:            uid,
		BucketId:       userExp.BucketId,
		Exp:            userExp.ExperimentId,
		ExpType:        userExp.ExperimentType,
		CsrIndentity:   csrIndentiy,
		InstallTime:    userExp.DistributeTime,
		DistributeTime: now.UnixMilli(),
		DistributeDate: dData,
		Hostname:       os.Getenv("EXT_HOSTNAME"),
		SIndex:         sIndex,
	}
	cacheKey := distributeLogKey(bundleId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	data, err := json.Marshal(ud)
	if err != nil {
		sugared.Errorf("json marshal err: %v", err)
		return
	}
	_, err = redisClient.LPush(ctx, cacheKey, data).Result()
	if err != nil {
		sugared.Errorf("redis LPush err: %v", err)
	}
}

func distributeLogKey(bundleId string) string {
	return fmt.Sprintf(UserDistributeCacheListPrefixKey, bundleId)
}

type WriteLogConfig struct {
	Bundle string `yaml:"bundle" json:"bundle"`
	Redis  string `yaml:"redis" json:"redis"`
	Mysql  string `yaml:"mysql" json:"mysql"`
}

type WriteLog struct {
	Bundle      string
	RedisClient cache.RedisClient
	MysqlClient xorm.EngineInterface
}

func NewWriteLogs(wlcs []WriteLogConfig) []*WriteLog {
	wls := make([]*WriteLog, len(wlcs))
	for i, wlc := range wlcs {
		wl := &WriteLog{
			Bundle: wlc.Bundle,
		}
		if redis := services.GetRedisClient(wlc.Redis); redis != nil {
			wl.RedisClient = redis
		} else {
			panic("redis client: " + wlc.Redis + " not found")
		}
		if mysql := services.GetMysqlClient(wlc.Mysql); mysql != nil {
			wl.MysqlClient = mysql
		} else {
			panic("mysql client: " + wlc.Mysql + " not found")
		}
		wls[i] = wl
	}
	return wls
}

func (wl *WriteLog) Write() {
	// 写distributes日志
	wdCount := wl.distributeCount()
	if wdCount > 0 {
		var i int64
		for i = 0; i < wdCount; i++ {
			wl.WriteDistribute()
		}
	}
}

func (wl *WriteLog) getDistributeTableName(unixMilli int64) (string, error) {
	local, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		sugared.Errorf("time.LoadLocation(Asia/Shanghai) err: %v", err)
		return "", err
	}
	tableSuffix := time.UnixMilli(unixMilli).In(local).Format("200601")
	tableName := "user_distributes_" + tableSuffix
	rows, err := wl.MysqlClient.Query("SHOW TABLES LIKE '" + tableName + "'")
	if err != nil {
		sugared.Errorf("query table err: %v", err)
		return "", err
	}
	if len(rows) > 0 {
		return tableName, nil
	}

	createtabeSql := fmt.Sprintf(UserDistributeCreateTableTempalte, tableName)
	_, err = wl.MysqlClient.Exec(createtabeSql)
	if err != nil {
		sugared.Errorf("create table err: %v", err)
		return "", err
	}
	return tableName, nil
}

func (wl *WriteLog) WriteDistribute() {
	key := distributeLogKey(wl.Bundle)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	if data, err := wl.RedisClient.RPop(ctx, key).Bytes(); err == nil {
		ud := &UserDistributes{}
		err := json.Unmarshal(data, ud)
		if err != nil {
			sugared.Errorf("json unmarshal data: %s, err: %v", string(data), err)
			return
		}
		tableName, err := wl.getDistributeTableName(ud.DistributeTime)
		if err != nil {
			sugared.Errorf("get distribute err: %v", err)
			return
		}
		affact, err := wl.MysqlClient.Table(tableName).Insert(ud)
		if err != nil {
			sugared.Errorf("mysql insert err: %v", err)
			return
		}
		if affact != 1 {
			sugared.Errorf("mysql insert affact: %v", affact)
		}
	}
}

func (wl *WriteLog) distributeCount() int64 {
	key := distributeLogKey(wl.Bundle)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	if count, err := wl.RedisClient.LLen(ctx, key).Result(); err == nil {
		return count
	} else {
		sugared.Errorf("redis LLen key:%s, err: %v", key, err)
		return 0
	}
}

func MonitorWriteUserLog(ctx context.Context, wlcs []WriteLogConfig) {
	wls := NewWriteLogs(wlcs)
	ticker := time.NewTicker(5 * time.Second)
	wg := sync.WaitGroup{}
	for _, wl := range wls {
		wg.Add(1)
		go func(wl *WriteLog) {
			defer wg.Done()
			for {
				select {
				case <-ctx.Done():
					sugared.Infof("WriteLog : %s Stop", wl.Bundle)
					return
				case <-ticker.C:
					wl.Write()
				}
			}
		}(wl)
	}
	wg.Wait()
	sugared.Infof("UserLog Write Over")
}
