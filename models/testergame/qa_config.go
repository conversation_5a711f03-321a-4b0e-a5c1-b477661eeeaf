package testergame

import (
	"context"
	"encoding/json"
	"sync"
	"time"
)

type QAConfig struct {
	BucketId     int    `json:"bucket"`
	ExperimentId string `json:"experiment"`
	State        bool   `json:"state"`
	ActiveTime   int64  `json:"active_time"`
}

type DBTestergameQaConfig struct {
	Id         int    `xorm:"'id' not null pk autoincr INT" json:"id,omitempty"`
	Bundleid   string `xorm:"'bundleid' not null unique(bundleid_uid) VARCHAR(64)" json:"bundleid,omitempty"`
	Uid        string `xorm:"'uid' not null unique(bundleid_uid) VARCHAR(64)" json:"uid,omitempty"`
	Bucket     int    `xorm:"'bucket' not null INT" json:"bucket,omitempty"`
	Experiment string `xorm:"'experiment' not null VARCHAR(32)" json:"experiment,omitempty"`
	ActiveTime int64  `xorm:"'active_time' not null INT" json:"active_time,omitempty"`
	State      int    `xorm:"'state' not null index TINYINT" json:"state,omitempty"`
}

type UserMappingQaConfig struct {
	Uid            string         `json:"uid,omitempty"`
	GameWayNum     string         `json:"gameWayNum,omitempty"`
	Experiment     map[string]any `json:"experiment,omitempty"`
	ExperimentType int            `json:"experimentType,omitempty"`
	State          bool           `json:"state,omitempty"`
}

func (m *DBTestergameQaConfig) TableName() string {
	return "testergame_qa_config"
}

type QAConfigs struct {
	configs            map[string]map[string]QAConfig
	userMappingConfigs map[string]UserMappingQaConfig
	mux                sync.RWMutex
}

func NewQAConfigs() *QAConfigs {
	qa := &QAConfigs{
		configs:            make(map[string]map[string]QAConfig),
		userMappingConfigs: make(map[string]UserMappingQaConfig),
	}
	//qa.loadConfigs()
	//go qa.update()
	return qa
}

const QAConfigsCacheKey = "hs:testergame:qa:list"
const QAConfigsUserMappingCacheKey = "hs:testergame:qa:usermapping"
const QAConfigsCacheTTL = time.Hour * 24 * 2
const QAUpdateInterval = time.Minute * 1

//func (qa *QAConfigs) loadConfigs() {
//	qa.mux.Lock()
//	defer qa.mux.Unlock()
//	configs := make(map[string]map[string]QAConfig)
//	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
//	defer cancel()
//	resultJson, err := redisClient.Get(ctx, QAConfigsCacheKey).Result()
//	if err != nil {
//		sugared.Errorf("failed to load qa configs from redis error: %v", err)
//		return
//	}
//	go func() {
//		redisClient.Expire(ctx, QAConfigsCacheKey, QAConfigsCacheTTL)
//	}()
//	dbConfigs := make([]DBTestergameQaConfig, 0)
//	err = json.Unmarshal([]byte(resultJson), &dbConfigs)
//	if err != nil {
//		sugared.Errorf("failed to load qa configs from json error: %v", err)
//		return
//	}
//	for _, dbConfig := range dbConfigs {
//		if _, ok := configs[dbConfig.Bundleid]; !ok {
//			configs[dbConfig.Bundleid] = make(map[string]QAConfig)
//		}
//		if dbConfig.State == 1 {
//			configs[dbConfig.Bundleid][dbConfig.Uid] = QAConfig{
//				BucketId:     dbConfig.Bucket,
//				ExperimentId: dbConfig.Experiment,
//				State:        true,
//				ActiveTime:   time.Now().Unix(),
//			}
//		}
//	}
//	qa.configs = configs
//	userMappingConfigs := make(map[string]UserMappingQaConfig)
//	userMappingJson, err := redisClient.Get(ctx, QAConfigsUserMappingCacheKey).Bytes()
//	if err != nil {
//		sugared.Errorf("failed to load qa configs from redis error: %v", err)
//		return
//	}
//	err = json.Unmarshal(userMappingJson, &userMappingConfigs)
//	if err != nil {
//		sugared.Errorf("failed to load qa configs from json error: %v", err)
//		return
//	}
//	qa.userMappingConfigs = userMappingConfigs
//	go func() {
//		redisClient.Expire(ctx, QAConfigsUserMappingCacheKey, QAConfigsCacheTTL)
//	}()
//}

//func (qa *QAConfigs) update() {
//	ticker := time.NewTicker(QAUpdateInterval)
//	defer ticker.Stop()
//	for range ticker.C {
//		qa.loadConfigs()
//	}
//}

func (qa *QAConfigs) AddQAConfig(bundleId string, dbQaConfig DBTestergameQaConfig) error {
	//qa.mux.Lock()
	//defer qa.mux.Unlock()
	//
	//if _, ok := qa.configs[dbQaConfig.Bundleid]; !ok {
	//	qa.configs[dbQaConfig.Bundleid] = make(map[string]QAConfig)
	//}
	//qa.configs[dbQaConfig.Bundleid][dbQaConfig.Uid] = QAConfig{
	//	BucketId:     dbQaConfig.Bucket,
	//	ExperimentId: dbQaConfig.Experiment,
	//	State:        true,
	//	ActiveTime:   time.Now().Unix(),
	//}
	//go qa.UpdateQAConfigs()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*1)
	defer cancel()
	key := QAConfigsCacheKey + ":" + bundleId + ":" + dbQaConfig.Uid
	jsonData, err := json.Marshal(dbQaConfig)
	if err != nil {
		return err
	}
	redisClient.Set(ctx, key, jsonData, QAConfigsCacheTTL)
	return nil
}

func (qa *QAConfigs) AddQAUserMappingConfig(bundleId string, userMapping UserMappingQaConfig) error {
	//qa.mux.Lock()
	//defer qa.mux.Unlock()
	//if userMapping.State {
	//	qa.userMappingConfigs[userMapping.Uid] = userMapping
	//} else {
	//	delete(qa.userMappingConfigs, userMapping.Uid)
	//}
	//go qa.UpdateQAConfigs()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*1)
	defer cancel()
	key := QAConfigsUserMappingCacheKey + ":" + bundleId + ":" + userMapping.Uid
	jsonData, err := json.Marshal(userMapping)
	if err != nil {
		return err
	}
	redisClient.Set(ctx, key, jsonData, QAConfigsCacheTTL)
	return nil
}

//func (qa *QAConfigs) UpdateQAConfigs() {
//	qa.mux.RLock()
//	defer qa.mux.RUnlock()
//	cacheConfigs := make([]DBTestergameQaConfig, 0)
//	for bundleId, bundleConfigs := range qa.configs {
//		for uid, qaConfig := range bundleConfigs {
//			state := 0
//			if qaConfig.State {
//				state = 1
//			}
//			cacheConfigs = append(cacheConfigs, DBTestergameQaConfig{
//				Bundleid:   bundleId,
//				Uid:        uid,
//				Bucket:     qaConfig.BucketId,
//				Experiment: qaConfig.ExperimentId,
//				State:      state,
//				ActiveTime: qaConfig.ActiveTime,
//			})
//		}
//	}
//	jsonBytes, err := json.Marshal(cacheConfigs)
//	if err != nil {
//		sugared.Errorf("failed to marshal qa configs to json error: %v", err)
//		return
//	}
//	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
//	defer cancel()
//	err = redisClient.Set(ctx, QAConfigsCacheKey, jsonBytes, QAConfigsCacheTTL).Err()
//	if err != nil {
//		sugared.Errorf("failed to update qa configs cache: %v", err)
//	}
//	// 更新userMappingConfigs
//	userMappingJson, err := json.Marshal(qa.userMappingConfigs)
//	if err != nil {
//		sugared.Errorf("failed to marshal qa configs to json error: %v", err)
//		return
//	}
//	err = redisClient.Set(ctx, QAConfigsUserMappingCacheKey, userMappingJson, QAConfigsCacheTTL).Err()
//	if err != nil {
//		sugared.Errorf("failed to update qa configs cache: %v", err)
//		return
//	}
//}
//
//func (qa *QAConfigs) SaveQAConfigs(data []byte) error {
//	if ok := json.Valid(data); !ok {
//		return errors.New("invalid json")
//	}
//	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
//	defer cancel()
//	err := redisClient.Set(ctx, QAConfigsCacheKey, string(data), QAConfigsCacheTTL).Err()
//	if err != nil {
//		sugared.Errorf("failed to save qa configs to redis error: %v", err)
//		return err
//	}
//	return nil
//}

func (qa *QAConfigs) GetQAConfig(bundleId, uid string) (QAConfig, error) {
	//qa.mux.RLock()
	//defer qa.mux.RUnlock()
	//if bunldeConfig, ok := qa.configs[bundleId]; ok {
	//	if qaConfig, ok := bunldeConfig[uid]; ok {
	//		if qaConfig.State {
	//			return qaConfig, nil
	//		}
	//	}
	//}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*1)
	defer cancel()
	key := QAConfigsCacheKey + ":" + bundleId + ":" + uid
	jsonData, err := redisClient.Get(ctx, key).Bytes()
	if err != nil {
		sugared.Errorf("failed to get qa config from redis error: %v", err)
		return QAConfig{}, err
	}
	dbQaConfig := QAConfig{}
	err = json.Unmarshal(jsonData, &dbQaConfig)
	if err != nil {
		sugared.Errorf("failed to unmarshal qa config from json error: %v", err)
		return QAConfig{}, err
	}
	return dbQaConfig, nil
}

func (qa *QAConfigs) GetQAUserMapping(bundleId, uid string) (UserMappingQaConfig, error) {
	//qa.mux.RLock()
	//defer qa.mux.RUnlock()
	//if qaConfig, ok := qa.userMappingConfigs[uid]; ok {
	//	if qaConfig.State {
	//		return qaConfig, nil
	//	}
	//}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*1)
	defer cancel()
	key := QAConfigsUserMappingCacheKey + ":" + bundleId + ":" + uid
	sugared.Infof("userMapping cache key: %s", key)
	jsonData, err := redisClient.Get(ctx, key).Bytes()
	if err != nil {
		sugared.Errorf("failed to get qa config from redis error: %v", err)
		return UserMappingQaConfig{}, err
	}
	qaUserMappingConfig := UserMappingQaConfig{}
	err = json.Unmarshal(jsonData, &qaUserMappingConfig)
	if err != nil {
		sugared.Errorf("failed to unmarshal qa config from json error: %v", err)
		return UserMappingQaConfig{}, err
	}
	//redisClient.Del(ctx, key)
	return qaUserMappingConfig, nil
}

//func (qa *QAConfigs) GetQAConfigs() map[string]map[string]QAConfig {
//	qa.mux.RLock()
//	defer qa.mux.RUnlock()
//	return qa.configs
//}

//func (qa *QAConfigs) GetQAMapping() map[string]UserMappingQaConfig {
//	qa.mux.RLock()
//	defer qa.mux.RUnlock()
//	return qa.userMappingConfigs
//}
