package testergame

import (
	"context"
	"encoding/json"
	"errors"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models/csr"
	"strconv"
	"time"
)

const CSRParamCacheKeyPrefix string = "CSRBBGP:"

type CSRApplyType string

const (
	CSRApplyTypeNew    CSRApplyType = "new"
	CSRApplyTypeActive CSRApplyType = "active"
)

type CSRParamOriginType string

const (
	CSRParamOriginTypeClient CSRParamOriginType = "client"
	CSRParamOriginTypeServer CSRParamOriginType = "server"
)

var CsrConfigNotFound error = errors.New("CSR not found in config")

type CSRCacheData struct {
	ActiveDays   int     `json:"activeDays"`
	AvgGameTime  float64 `json:"avgGameTime"`
	AvgAds       float64 `json:"avgAds"`
	AvgGames     float64 `json:"avgGames"`
	AvgAdRevenue float64 `json:"avgAdRevenue"`
	Ecpm         float64 `json:"eCPM"`
}

type BundleCsrConfig struct {
	CSRParamCacheKeyPrefix string                     `yaml:"csrParamCacheKeyPrefix" json:"csrParamCacheKeyPrefix"`
	CsrConfigs             map[CSRApplyType]CsrConfig `yaml:"csrConfigs" json:"csrConfigs"`
}

type CsrConfig struct {
	IndentityKeys    []string            `yaml:"indentityKeys" json:"indentityKeys"`
	IndentityConfigs CsrIndentityConfigs `yaml:"indentityConfigs" json:"indentityConfigs"`
}

type CsrIndentityConfig struct {
	Kind  string `yaml:"kind" json:"kind"`
	Value []any  `yaml:"value" json:"value"`
}

type CsrIndentityConfigs map[string]CsrIndentityConfig

type Csr struct {
	configs map[string]BundleCsrConfig
}

func NewCsr(csrConfigs map[string]BundleCsrConfig) *Csr {
	return &Csr{configs: csrConfigs}
}

func (c *Csr) GetCSRIndentity(bundleId string, csrApplyType CSRApplyType, uid string, csrParams map[string]any) (indentity string, originType CSRParamOriginType, err error) {
	if csrBundleConfig, ok := c.configs[bundleId]; ok {
		if csrConfig, ok := csrBundleConfig.CsrConfigs[csrApplyType]; ok {
			switch csrApplyType {
			case CSRApplyTypeNew:
				indentity, err = ParseCsrIndentityFromCsrParams(csrConfig, csrParams)
				if err != nil {
					sugared.Errorf("Error parsing indentity from csr params: %v", err)
					return "", "", err
				}
				return indentity, CSRParamOriginTypeClient, nil
			case CSRApplyTypeActive:
				var cacheData CSRCacheData
				cacheData, err = GetCSRParamCache(csrBundleConfig.CSRParamCacheKeyPrefix, uid)
				if err != nil {
					indentity, err = ParseCsrIndentityFromCsrParams(csrConfig, csrParams)
					if err != nil {
						sugared.Errorf("Error parsing indentity from csr params: %v", err)
						return "", "", err
					}
					return indentity, CSRParamOriginTypeClient, nil
				}
				indentity, err = ParseCsrIndentityFromCsrCacheData(csrConfig, cacheData)
				if err == nil {
					return indentity, CSRParamOriginTypeServer, nil
				} else {
					indentity, err = ParseCsrIndentityFromCsrParams(csrConfig, csrParams)
					if err != nil {
						sugared.Errorf("Error parsing indentity from csr params: %v", err)
						return "", "", err
					}
					return indentity, CSRParamOriginTypeClient, nil
				}
			default:
				return "", "", errors.New(string(csrApplyType) + " invalid csr apply type")
			}
		} else {
			sugared.Errorf("bundle(%s) CsrType(%s) not found", bundleId, csrApplyType)
			return "", "", CsrConfigNotFound
		}
	}
	return "", "", CsrConfigNotFound
}

func GetCSRParamCache(prefixKey, uid string) (CSRCacheData, error) {
	key := prefixKey + uid
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	startTime := time.Now().UnixNano()
	resultStr, err := csrRedisClient.Get(ctx, key).Result()
	useTimes := time.Now().UnixNano() - startTime
	if useTimes >= int64(time.Second) {
		sugared.Errorf("GetCSRParamCache useTimes: %ds", useTimes)
		metrics.GameTesterDBExecTime.WithLabelValues("GetCSRParamCache").Inc()
	}
	if err != nil {
		sugared.Errorf("redisClient.Get(%q): %v", key, err)
		return CSRCacheData{}, err
	}
	cacheData := CSRCacheData{}
	err = json.Unmarshal([]byte(resultStr), &cacheData)
	if err != nil {
		sugared.Errorf("json.Unmarshal(%s): %v", resultStr, err)
		return CSRCacheData{}, err
	}
	return cacheData, nil
}

func ParseCsrIndentityFromCsrParams(csrConfig CsrConfig, csrParams map[string]any) (string, error) {
	indentity := ""
	for i, k := range csrConfig.IndentityKeys {
		paramValue, ok := csrParams[k]
		if !ok {
			return "", errors.New("csrParam: " + k + " not found")
		}
		kind := csrConfig.IndentityConfigs[k].Kind
		values := csrConfig.IndentityConfigs[k].Value
		if i != 0 {
			indentity += "_"
		}
		indentity += strconv.Itoa(csr.ParseIndentity(kind, paramValue, values))
	}
	return indentity, nil
}

func ParseCsrIndentityFromCsrCacheData(csrConfig CsrConfig, cacheData CSRCacheData) (string, error) {
	indentity := ""
	for i, k := range csrConfig.IndentityKeys {
		paramValue, err := getCsrValueFromCacheData(k, cacheData)
		if err != nil {
			sugared.Errorf("getCsrValueFromCacheData(%s): %v", k, err)
			return "", err
		}
		kind := csrConfig.IndentityConfigs[k].Kind
		values := csrConfig.IndentityConfigs[k].Value
		if i != 0 {
			indentity += "_"
		}
		indentity += strconv.Itoa(csr.ParseIndentity(kind, paramValue, values))
	}
	return indentity, nil
}

func getCsrValueFromCacheData(indentityKey string, cacheData CSRCacheData) (any, error) {
	switch indentityKey {
	case csr.IndentityKeyEcpm:
		return cacheData.Ecpm, nil
	case csr.IndentityKeyAdNum:
		return cacheData.AvgAds, nil
	case csr.IndentityKeyGameNum:
		return cacheData.AvgGames, nil
	case csr.IndentityKeyADay:
		return cacheData.ActiveDays, nil
	default:
		return nil, errors.New("indentityKey: " + indentityKey + " not found")
	}
}
