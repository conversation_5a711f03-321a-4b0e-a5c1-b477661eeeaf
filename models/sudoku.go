package models

import (
	"context"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models/adconfigs"
	"hungrystudio.com/datatester/models/distributes"
	"io"
	"slices"
	"strconv"
	"strings"
)

type SudokuTraffic struct {
	*Traffic
}

// setVersionRoundRate 版本分流配置方案列表
func setVersionRoundRate() []distributes.Scheme {
	schemes := make([]distributes.Scheme, 0)

	for i := 1; i <= 100; i++ {
		scheme := distributes.Scheme{
			Name:   fmt.Sprintf("%d", i),
			Weight: 0,
		}
		schemes = append(schemes, scheme)
	}

	//打乱顺序
	schemes = distributes.RandomOrder(schemes)

	return schemes
}

// 纯净桶方案列表
func setSudoRoundRate() []distributes.Scheme {
	schemes := make([]distributes.Scheme, 0)

	for i := 101; i <= 120; i++ {
		scheme := distributes.Scheme{
			Name:   fmt.Sprintf("%d", i),
			Weight: 0,
		}
		schemes = append(schemes, scheme)
	}

	return schemes
}

func NewSudokuTraffic(ctx context.Context, bundleConfig *BundleConfig, abWriter io.Writer) *SudokuTraffic {
	return &SudokuTraffic{
		Traffic: NewTraffic(ctx, bundleConfig, abWriter),
	}
}

func (t *SudokuTraffic) GetTrafficConfig(params map[string]any) map[string]any {
	sugared.Infof("Start Execute GetTrafficConfig")
	var apiVersion = params["api_version"].(string)
	var appVersion = params["appVersion"].(string)
	var serverCountryCode = params[ParamNameServerCountryCode].(string)
	var serverIp = params[ParamNameServerIP].(string)

	//研发批次号
	if _, ok := params["pici"].(string); !ok {
		params["pici"] = ""
	}
	//研发批次类型
	if _, ok := params["shunt_type"].(string); !ok {
		params["shunt_type"] = "new"
	}
	//商业化方案号
	if _, ok := params["adwaynum"].(string); !ok {
		params["adwaynum"] = ""
	}
	//实际分配版本
	if _, ok := params["allot_version"].(string); !ok {
		params["allot_version"] = ""
	}
	//设备号
	if _, ok := params["device_id"].(string); !ok {
		params["device_id"] = ""
	}

	//初始化
	initConfig := make(map[string]any)

	//研发方案不为空,判断是否在批次方案列表中，不在则置为空重新分配
	initConfig["plan"] = ""
	if plan, ok := params["plan"].(string); ok && plan != "" {
		if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(params["pici"].(string), params["shunt_type"].(string)); err == nil && plan != "" {
			if IsExistence(plan, productSchemeConf.Scheme) {
				initConfig["plan"] = plan
			}
		}
	}

	//研发方案号为空，重置分流标识，重新生成
	if initConfig["plan"] == "" {
		params["shunt_index"] = ""
	}

	//分流标识：
	if shuntIndex, ok := params["shunt_index"].(string); !ok || shuntIndex == "" {
		params["shunt_index"] = GenerateShuntIdent(t, params)
	}

	sugared.Infof("Sudoku GetTrafficConfig apiVersion: %v, shuntType:%s ,adwaynum: %s, thinduid: %s, piCi: %s, shunt_index: %s", apiVersion, params["shunt_type"].(string), params["adwaynum"].(string), params["thinkuid"].(string), params["pici"].(string), params["shunt_index"].(string))

	// 分流标识返回
	initConfig["shunt_index"] = params["shunt_index"]

	// 是否是纯净桶用户，不为空则不再重新分配，5%纯净版，20个方案依次轮询，101方案时判断为纯净桶
	if pureSchemeValue, ok := params["pure_scheme"].(string); ok && pureSchemeValue != "" {
		initConfig["pure_scheme"] = pureSchemeValue
	} else {
		sudoDistributeProvider := distributeFactory.GetDistributeProvider(distributes.SudoDistributeProviderRobinInstance)
		if sudoDistributeProvider != nil {
			group := fmt.Sprintf("chunjing_%s_%s_%s_%s_round", t.bundleConfig.BundleId, apiVersion, params["pici"].(string), params["shunt_type"].(string))
			var randSchemes = setSudoRoundRate()
			scheme := sudoDistributeProvider.Distribute(group, params["shunt_index"].(string), randSchemes)
			sugared.Infof("ChunJing Distributed Scheme: %v", scheme)
			//5%进入纯净桶，
			params["pure_scheme"] = scheme.Name
			initConfig["pure_scheme"] = scheme.Name
		}
	}

	// 获取AdConfigs、分配版本号、是否参与分流
	adConfigs, allotVersion, isDistributed := t.GetAdConfigs(params)
	initConfig["allot_version"] = allotVersion

	// 获取所有开关变量值,依据apiVersion
	if apiVersionSwitchConfig := t.switchConfigs.getSwitch(apiVersion); apiVersionSwitchConfig != nil {
		for k, v := range apiVersionSwitchConfig {
			initConfig[k] = v
		}
	}

	// 不是纯净桶用户，未分配方案，获取研发方案配置进行分配
	if params["pure_scheme"] != "101" && params["pici"].(string) != "" && initConfig["plan"] == "" {
		if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(params["pici"].(string), params["shunt_type"].(string)); err == nil {
			sugared.Infof("%s：获取研发方案配置_%s_%s", t.bundleConfig.BundleId, apiVersion, params["pici"].(string))
			group := fmt.Sprintf("product_pici_scheme_%s_%s_%s_%s_round", t.bundleConfig.BundleId, apiVersion, params["pici"].(string), params["shunt_type"].(string))
			sudoDistributeProvider := distributeFactory.GetDistributeProvider(distributes.SudoDistributeProviderRobinInstance)
			plan := sudoDistributeProvider.Distribute(group, params["shunt_index"].(string), slices.Clone(productSchemeConf.Scheme))
			sugared.Infof("Product pici Distributed Scheme: %v", plan)
			initConfig["plan"] = plan.Name

			//研发策略每次分配结果上报prometheus
			labels := prometheus.Labels{
				"bundle_id":   t.bundleConfig.BundleId,
				"pici":        params["pici"].(string),
				"shunt_type":  params["shunt_type"].(string),
				"shunt_index": initConfig["shunt_index"].(string),
				"plan":        initConfig["plan"].(string),
			}
			metrics.SudoProduct.With(labels).Inc()
		}
	}

	// 设置开关变量
	if switchConfig := t.setSwitch(apiVersion, appVersion, serverCountryCode, serverIp, adConfigs.Switch); switchConfig != nil {
		for k, v := range switchConfig {
			initConfig[k] = v
		}
	}

	// 过审状态
	if guoShentype := t.guoShenConfigs.getGuoShenType(appVersion); guoShentype >= 0 {
		initConfig["is_reviewing"] = "1"
	}

	// 选择符合条件的AdConfig
	var adInfo map[string]any
	adInfo = adConfigs.GetAdInfo(params, t.bundleConfig.AdsConfigFilters)
	if adInfo == nil {
		adInfo = make(map[string]any)
	}

	// 是否要参加火山AB实验，参加则取回火山AB实验配置的AdConfig
	if isDistributed && adConfigs.CheckAbTester() {
		sugared.Infof("参与火山分流")
		if validExperimentVars := t.abTesterConfigs.getExperimentVars(apiVersion); validExperimentVars != nil {
			//sugared.Infof("ApiVersion: %s, ValidExperimentVars: %v", apiVersion, validExperimentVars)
			if abAdInfo, vid, ok := t.appDataTester.Execute(t.abTesterConfigs.getExperimentVars(apiVersion), params); ok {
				//sugared.Infof("HuoShanAbTester AbConfig: %v", abConfig)
				for k, v := range abAdInfo {
					adInfo[k] = v
				}
				initConfig["vid"] = vid
			}
		}
	}

	var thinkUid = params["thinkuid"].(string)
	// 用户是否当天首次请求
	if isFirst, ok := adInfo["is_first_status"].(string); ok && isFirst == "open" {
		sugared.Infof("%s 判断用户是否当天首次请求_%s", t.bundleConfig.BundleId, thinkUid)
		if CheckDayFirst(t.bundleConfig.BundleId, thinkUid) {
			adInfo["is_first"] = "1"
		} else {
			adInfo["is_first"] = "0"
		}
		delete(adInfo, "is_first_status")
	}

	// 配置平台
	if adPlatform, ok := adInfo["adplatform"].(string); ok && AdfConf[adPlatform] != "" {
		adInfo["ad_platform"] = AdfConf[adPlatform]
		delete(adInfo, "adplatform")
	}

	// 插屏广告信息配置
	if insertAdunit, ok := adInfo["insert_adunit"].(string); ok {
		adInfo["insert"] = map[string]interface{}{
			"adunit":   insertAdunit,
			"keywords": []string{},
		}
		delete(adInfo, "insert_adunit")
	}

	// 激励广告信息配置
	if rewardAdunit, ok := adInfo["reward_adunit"].(string); ok {
		adInfo["reward"] = map[string]interface{}{
			"adunit":   rewardAdunit,
			"keywords": []string{},
		}
		delete(adInfo, "reward_adunit")
	}

	// banner告信息配置
	if bannerAdunit, ok := adInfo["banner_adunit"].(string); ok {

		// banner告信息配置
		bannerInterval := []float64{}
		if bannerIntervalVal, ok := adInfo["banner_interval"].([]interface{}); ok {
			for _, val := range bannerIntervalVal {
				if value, ok := val.(float64); ok {
					bannerInterval = append(bannerInterval, value)
				}
			}
			delete(adInfo, "banner_interval")
		}

		// banner告信息配置
		bannerEvents := ""
		if bannerEventVar, ok := adInfo["banner_events"].(string); ok {
			bannerEvents = bannerEventVar
		}

		adInfo["banner"] = map[string]interface{}{
			"adunit":         bannerAdunit,
			"keywords":       []string{},
			"bannerInterval": bannerInterval,
			"banner_events":  bannerEvents,
		}
		delete(adInfo, "banner_adunit")
		delete(adInfo, "banner_interval")
	}

	//sugared.Infof("adConfig.resetIsFirst AdConfig: %v", adConfig)
	initConfig["ad_info"] = adInfo
	return initConfig
}

// GetAdConfigs 数独CRS平均分配分配 返回分配后的AdConfigs、使用的策略版本号、是否参与了分流
func (t *SudokuTraffic) GetAdConfigs(params map[string]any) (adconfigs.AdConfigs, string, bool) {
	var apiVersion = params["api_version"].(string)
	var allotVersion = params["allot_version"].(string)
	var thinkUid = params["thinkuid"].(string)
	var adWayNum = params["adwaynum"].(string)
	var appVersion = params["appVersion"].(string)
	var deviceId = params["device_id"].(string)

	//高低端机类型类型
	var opt = ""
	if Opt, ok := params["opt"].(string); ok {
		opt = Opt
	}

	var adConfigs adconfigs.AdConfigs
	var err error
	sugared.Infof("Sudoku GetAdConfigs apiVersion: %v,shuntType: %s, pure_schemem: %s,adwaynum: %s, thinduid: %s", apiVersion, params["shunt_type"].(string), params["pure_scheme"].(string), adWayNum, thinkUid)

	// 过审开启
	if guoShentype := t.guoShenConfigs.getGuoShenType(appVersion); guoShentype >= 0 {
		guoShenAdWayNum := fmt.Sprintf("9999%d", guoShentype)
		// 查找过审对应的方案和配置【params["shunt_type"].(string)默认取新增 new】
		if adConfigs, err = t.llConfigs.GetAdConfigs("new", "v0", guoShenAdWayNum); err == nil {
			return adConfigs, apiVersion, false
		}
		// 返回配置中的默认总兜底
		sugared.Infof("过审开启，未找到对应的方案和配置")
		return adconfigs.NewAdConfigsWithAdInfo(t.bundleConfig.AdInfoDefault), apiVersion, false
	}

	// 5%纯净版，20个方案依次轮询，101方案时判断为纯净桶
	if pureScheme, ok := params["pure_scheme"].(string); ok && pureScheme == "101" {
		if adConfigs, err = t.chunJingConfigs.getAdConfigs(apiVersion); err == nil {
			sugared.Infof("round Scheme %s return chunjing adConfigs: %v", params["pure_scheme"], adConfigs)
			return adConfigs, apiVersion, false
		}
	}

	// 低端机型判断
	if adConfigs, err = t.noNormalConfigs.GetNoNormalAdConfigs(opt); err == nil {
		return adConfigs, apiVersion, false
	}

	// 检查是否为qa用户，如果是，则重置adWayNum为qa用户指定adWayNum
	//t.qaConfigs.resetQaAdWayNum(&adWayNum, thinkUid)
	t.csrQaConfigs.resetCsrQaAdWayNum(&adWayNum, &allotVersion, thinkUid, deviceId)

	// 用户指定配置
	if adConfigs, err = t.specialConfigs.getConfig(thinkUid); err == nil {
		return adConfigs, apiVersion, false
	}

	//验证、确定最终分流使用的api_version版本
	apiVersion = VerifiedVersion(t, allotVersion, params)

	// 检查用户的apiVersion和adWayNum是否存在，存在用户无需继续继续分流【params["shunt_type"].(string)默认取新增 new】
	if adConfigs, err = t.llConfigs.GetAdConfigs("new", apiVersion, adWayNum); err == nil {
		sugared.Infof("用户无需分流")
		return adConfigs, apiVersion, false
	}

	//获取csr分流器
	sudoDistributeProvider := distributeFactory.GetDistributeProvider(distributes.SudoDistributeProviderRobinInstance)

	// 轮询平均分流方式【params["shunt_type"].(string) 默认取新增 new】
	if adWayNums := t.llConfigs.GetAllAdWayNum("new", apiVersion); adWayNums != nil {
		// Rate > 0 参与分组
		schemes := make([]distributes.Scheme, 0)
		for k, v := range adWayNums {
			sugared.Infof("AdwayNum: %s,Rate: %f", k, v.Rate)
			if v.Rate > 0 {
				scheme := distributes.Scheme{
					Name:   k,
					Weight: 0,
				}
				schemes = append(schemes, scheme)
			}
		}

		// 轮询平均分流
		if sudoDistributeProvider != nil {
			group := fmt.Sprintf("syh_polling_average_%s_%s_%s_%s_round", t.bundleConfig.BundleId, apiVersion, params["pici"].(string), "new") //params["shunt_type"].(string) 默认取新增 new】
			scheme := sudoDistributeProvider.Distribute(group, params["shunt_index"].(string), schemes)
			sugared.Infof("Syh Polling average Distributed Scheme: %v", scheme)
			if v, ok := adWayNums[scheme.Name]; ok {

				//商业化策略每次分配结果上报prometheus
				labels := prometheus.Labels{
					"bundle_id":     t.bundleConfig.BundleId,
					"api_version":   params["api_version"].(string),
					"allot_version": apiVersion,
					"shunt_index":   params["shunt_index"].(string),
					"plan":          scheme.Name,
				}
				metrics.SudoSyh.With(labels).Inc()
				return v.Config, apiVersion, true
			}
		}
	}

	// apiVersion兜底配置
	if adConfigs, err = t.douDiConfigs.getDouDi(apiVersion); err == nil {
		sugared.Infof("getDouDi: %v", adConfigs)
		return adConfigs, apiVersion, false
	}

	// 返回配置中的默认总兜底
	sugared.Infof("get Default Doudi")
	return adconfigs.NewAdConfigsWithAdInfo(t.bundleConfig.AdInfoDefault), apiVersion, false
}

// GenerateShuntIdent 生成分流标识
func GenerateShuntIdent(t *SudokuTraffic, params map[string]any) string {

	sugared.Infof("Sudoku GenerateShuntIdent 准备生成分流标识 : piCi: %s, shuntType: %s", params["pici"].(string), params["shunt_type"].(string))

	if productSchemeConf, err := t.productPiciSchemeConfig.getProductPiciSchemeConfigs(params["pici"].(string), params["shunt_type"].(string)); err == nil {

		//获取归档配置
		archiveConf := productSchemeConf.Config
		//归档查询指定顺序
		selectOrder := t.bundleConfig.NewFilingSequence
		if params["shunt_type"].(string) != "new" {
			selectOrder = t.bundleConfig.DynamicFilingSequence
		}
		//查寻判断条件
		var shuntIndex string
		for _, target := range selectOrder {
			if pigeonholeConf, ok := archiveConf[target]; ok {
				target = strings.Replace(target, "_group", "", -1)
				sugared.Infof("查寻判断条件: %s", target)
				gears := 1
				if conf, ok := pigeonholeConf.(map[string]interface{}); ok {
					if kind, ok := conf["kind"].(string); ok {
						switch kind {
						case "list":
							if targetValue, ok := params[target].(string); ok { //字符串分组
								targetValue = strings.ToLower(targetValue)
								gears = FindString(targetValue, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							} else if targetFloat, ok := params[target].(float64); ok { //数值分组
								gears = FindFloat(targetFloat, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							}
						case "range":
							if targetNum, ok := params[target].(float64); ok {
								gears = LastLessThan(targetNum, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							}
						case "group":
							if groupValue, ok := params[target].(string); ok { //字符串分组
								groupValue = strings.ToLower(groupValue)
								gears = FindGroup(groupValue, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							} else if groupFloat, ok := params[target].(float64); ok { //数值分组
								gears = FindFloatGroup(groupFloat, pigeonholeConf.(map[string]interface{})["value"].([]interface{}))
							}
						}
					}
				}
				shuntIndex = fmt.Sprintf("%s_%d", shuntIndex, gears)
				sugared.Infof("Sudoku GenerateShuntIdent : piCi: %s, shuntType: %s, target: %s , gears: %d ,shuntIndex：%s", params["pici"].(string), params["shunt_type"].(string), target, gears, shuntIndex)
			}
		}
		shuntIndex = strings.Trim(shuntIndex, "_")
		sugared.Infof("Sudoku GenerateShuntIdent 分流标识 : piCi: %s, shuntType: %s, shuntIndex：%s", params["pici"].(string), params["shunt_type"].(string), shuntIndex)
		return shuntIndex
	}
	return ""
}

// VerifiedVersion 验证版本，返回有效版本
func VerifiedVersion(t *SudokuTraffic, allotVersion string, params map[string]any) string {

	var apiVersion = params["api_version"].(string)

	//allotVersion存在，并且  apiVersion == allotVersion时，无须验证，直接返回
	if allotVersion != "" && apiVersion == allotVersion {
		return apiVersion
	}

	//csr分流器
	sudoDistributeProvider := distributeFactory.GetDistributeProvider(distributes.SudoDistributeProviderRobinInstance)
	//分流器获取失败
	if sudoDistributeProvider == nil {
		return apiVersion
	}

	//版本已分流配置，
	if versionGatherConfigs, vErr := t.versionGatherConfigs.getVersionGather(apiVersion); vErr == nil {

		//allotVersion存在，并且!=apiVersion时，验证allotVersion是否在api版本分流配置中，是则返回allotVersion
		if allotVersion != "" && allotVersion != apiVersion {
			if _, ok := versionGatherConfigs[allotVersion]; ok {
				sugared.Infof("allotVersion存在,并且是否在api：%s 版本分流配置中，是则返回allotVersion：%s", apiVersion, allotVersion)
				return allotVersion
			}
		}

		//allotVersion不存在、或者在当前版本分流中不存在，重新分流
		// 获取版本分流号1-100
		var versionSchemes = setVersionRoundRate()
		//版本分流桶：//params["shunt_type"].(string) 默认取新增 new】
		group := fmt.Sprintf("syh_version_gather_average_%s_%s_%s_%s_round", t.bundleConfig.BundleId, apiVersion, params["pici"].(string), "new")
		scheme := sudoDistributeProvider.Distribute(group, params["shunt_index"].(string), versionSchemes)
		//版本分流号转float64
		floatValue, strErr := strconv.ParseFloat(scheme.Name, 64)
		if strErr == nil {
			for key, vGConf := range versionGatherConfigs {
				//符合条件重置apiVersion
				if vGConf.MinNumber <= floatValue && vGConf.MaxNumber >= floatValue {
					sugared.Infof("apiVersion：%s 版本分流配置命中，重置参数：%s ", apiVersion, key)
					return key
				}
			}
		}
	}

	//无分流配置，返回apiVersion
	return apiVersion
}
