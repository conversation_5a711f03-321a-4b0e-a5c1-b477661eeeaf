package models

import (
	"awdb-go/awdb"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"
	"time"
)

type IPData struct {
	sync.RWMutex
	url          string
	awdbFilename string
	awdbReader   *awdb.AwdbReader
	ticker       *time.Ticker
}

func NewIPData(ctx context.Context, url string) *IPData {
	id := &IPData{
		url: url,
	}
	err := id.UpdateIPData()
	if err != nil {
		panic(err)
	}
	go func() {
		resetDuration := true
		id.ticker = time.NewTicker(id.getUpdateDuration())
		for range id.ticker.C {
			sugared.Infof("Ticker Update")
			if resetDuration {
				sugared.Infof("Ticker Update Set 24h.")
				id.ticker.Reset(time.Hour * 24)
				resetDuration = false
			}
			_ = id.UpdateIPData()
		}
	}()
	go id.Close(ctx)
	return id
}

// downloadIPDataFile 下载ip数据库文件
func (id *IPData) downloadIPDataFile() (string, error) {
	file, err := os.CreateTemp(os.TempDir(), "ip-data")
	if err != nil {
		return "", err
	}
	defer file.Close()
	response, err := http.Get(id.url)
	if err != nil {
		return "", nil
	}
	defer response.Body.Close()
	_, err = io.Copy(file, response.Body)
	if err != nil {
		return "", nil
	}
	return file.Name(), nil
}

// UpdateIPData 更新IP数据库文件
func (id *IPData) UpdateIPData() error {
	id.Lock()
	defer id.Unlock()
	filename, err := id.downloadIPDataFile()
	if err != nil {
		sugared.Errorf("IPData.downloadIPDataFile Error: %s", err.Error())
		return err
	}
	sugared.Infof("IpdataFilepath: %s", filename)
	awdbReader, err := awdb.Openfile(filename)
	if err != nil {
		sugared.Errorf("IPdata.awdb.OpenFile Error: %s", err.Error())
		return err
	}
	if id.awdbReader != nil {
		id.awdbReader.Closefile()
		id.awdbReader = awdbReader
		_ = os.Remove(id.awdbFilename)
	} else {
		id.awdbReader = awdbReader
	}
	id.awdbFilename = filename
	sugared.Infof("Update IPDatat %s Success", id.awdbFilename)
	return nil
}

// GetCountry 获取国家信息
func (id *IPData) GetCountry(ip string) (string, error) {
	id.RLock()
	defer id.RUnlock()
	if id.awdbReader == nil {
		return "", errors.New("awdbRead nil")
	}
	err, awdbData := id.awdbReader.SearchIP(ip)
	if err != nil {
		return "", err
	}
	country, ok := awdbData["areacode"]
	if ok {
		return country.(string), nil
	}
	return "", errors.New(fmt.Sprintf("country not found by ip:%s", ip))
}

// GetGeo 获取地理信息 目前并没有统一处理
func (id *IPData) GetGeo(ip string) (map[string]any, error) {
	id.RLock()
	defer id.RUnlock()
	if id.awdbReader == nil {
		return nil, errors.New("awdbReader is nil")
	}
	err, awdbData := id.awdbReader.SearchIP(ip)
	if err != nil {
		return nil, err
	}
	return awdbData, nil
}

// getIPDataUpdateDuration 获取下次更新IP的时间间隔
func (id *IPData) getUpdateDuration() time.Duration {
	now := time.Now()
	if now.Hour() < 15 {
		return time.Date(now.Year(), now.Month(), now.Day(), 15, 0, 0, 0, time.Local).Sub(now)
	} else if now.Hour() > 15 {
		nextDay := now.Add(time.Hour * 24)
		return time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 15, 0, 0, 0, time.Local).Sub(now)
	} else {
		return time.Hour * 24
	}
}

// Close 关闭IPData
func (id *IPData) Close(ctx context.Context) {
	<-ctx.Done()
	id.ticker.Stop()
	id.awdbReader.Closefile()
	os.Remove(id.awdbFilename)
}
