package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"slices"
	"strconv"
	"time"

	"github.com/twmb/murmur3"

	"golang.org/x/exp/maps"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models/distributes"
	"hungrystudio.com/datatester/models/testergame"
	"hungrystudio.com/datatester/whiteuid"
)

type TesterTraffic struct {
	teserTrafficConfig testergame.TesterTrafficConfig
	bucketConfigs      *testergame.BucketConfigs
	bucketsFlow        *testergame.BucketsFlow
	userExperiment     *testergame.UserExperiment
	algoProvider       *testergame.DistributeAlgoProvider
	qaConfigs          *testergame.QAConfigs
	csr                *testergame.Csr
	abtest             *testergame.ABTestFeatures
	interceptors       []testergame.InterceptorInterface
	multiLinks         map[string]*testergame.MultiLink
}

func NewTesterTraffic(ctx context.Context, ttConfig testergame.TesterTrafficConfig) *TesterTraffic {
	tt := &TesterTraffic{
		teserTrafficConfig: ttConfig,
		bucketConfigs:      testergame.NewBucketConfigs(ttConfig.BundleId, ttConfig.UpdateInterval),
		bucketsFlow:        testergame.NewBucketsFlow(),
		userExperiment:     &testergame.UserExperiment{},
		algoProvider:       testergame.NewDistributeAlgoProvider(),
	}
	if tt.teserTrafficConfig.QA {
		sugared.Infof("Used QA")
		tt.qaConfigs = testergame.NewQAConfigs()
	}
	if tt.teserTrafficConfig.UseCSR {
		sugared.Infof("Used CSR")
		tt.csr = testergame.NewCsr(ttConfig.CSR)
	}
	go tt.Close(ctx)
	return tt
}

func (tt *TesterTraffic) GetSecretKey(bundleId string) string {
	if key, ok := tt.teserTrafficConfig.ServerSecretKey[bundleId]; ok {
		return key
	}
	return ""
}

func (tt *TesterTraffic) AddInterceptor(interceptor ...testergame.InterceptorInterface) {
	if tt.interceptors == nil {
		tt.interceptors = make([]testergame.InterceptorInterface, 0)
	}
	tt.interceptors = append(tt.interceptors, interceptor...)
}

func (tt *TesterTraffic) AddMultiLink(ml *testergame.MultiLink) {
	if tt.multiLinks == nil {
		tt.multiLinks = make(map[string]*testergame.MultiLink)
	}
	tt.multiLinks[ml.GetBundleId()] = ml
}

// ExecInterceptors 执行拦截器
func (tt *TesterTraffic) ExecInterceptors(req *testergame.GameInitRequest) (testergame.ExperimentResult, error) {
	for _, interceptor := range tt.interceptors {
		if interceptor.ValidInterceptor(req) {
			if bucketConfig, err := interceptor.ExecRedirectBucket(req, tt.bucketConfigs); err == nil {
				if ret, err := tt.bucketExperimentsDistribute(bucketConfig.BucketId, *req); err == nil {
					go interceptor.InterceptorFinish(req)
					return ret, nil
				}
			}
		}
	}
	return testergame.ExperimentResult{}, errors.New("no interceptor exec")
}

func (tt *TesterTraffic) GameInitWithLog(requestData []byte, reqId int64) (ret testergame.ExperimentResult, serverLog string, err error) {
	defer func() {
		if r := recover(); r != nil {
			sugared.Error("GameInit panic err: %v", r)
		}
	}()

	gameRequest := testergame.GameInitRequest{}
	err = json.Unmarshal(requestData, &gameRequest)
	if err != nil {
		sugared.Errorf("unmarshal json error: %s", err)
		err = errors.New("request data json parse error")
		return
	}
	gameRequest.RequestId = reqId
	// 检查QA用户
	if tt.teserTrafficConfig.QA {
		aimBucketId := tt.GetQABucket(gameRequest.Uid)
		if aimBucketId > 0 {
			sugared.Infof("qa start aim bucketId: %d", aimBucketId)
			ret, err = tt.bucketExperimentsDistribute(aimBucketId, gameRequest)
			return
		}
		sugared.Infof("qa start")
		if qaConfig, err := tt.qaConfigs.GetQAConfig(gameRequest.BundleId, gameRequest.Uid); err == nil {
			sugared.Infof("qa config %+v", qaConfig)
			if qaGameConfig, ok := tt.bucketConfigs.GetBucketExperiment(gameRequest.BundleId, qaConfig.BucketId, qaConfig.ExperimentId); ok {
				return testergame.ExperimentResult{
					GameWayNum:       qaConfig.ExperimentId,
					BucketId:         qaConfig.BucketId,
					ExperimentConfig: qaGameConfig,
				}, serverLog, nil
			} else {
				sugared.Errorf("get qa config bundle:%s,Uid: %s,QaConfig:%+v not exist", gameRequest.BundleId, gameRequest.Uid, qaConfig)
			}
		}
		if qaConfig, err := tt.qaConfigs.GetQAUserMapping(os.Getenv("GAME_BUNDLE_ID"), gameRequest.Uid); err == nil {
			return testergame.ExperimentResult{
				ExperimentConfig: qaConfig.Experiment,
				GameWayNum:       qaConfig.GameWayNum,
				ExperimentType:   qaConfig.ExperimentType,
			}, serverLog, nil
		}
	}

	sugared.Infof("GameInit uid: %s", gameRequest.Uid)
	if tt.teserTrafficConfig.Dispatch.Enabled {
		hash := murmur3.Sum32([]byte(gameRequest.Uid))
		mod := int(hash % testergame.DispatchMod)
		sugared.Infof("dispatch uid: %s, mod: %d", gameRequest.Uid, mod)
		if mod < tt.teserTrafficConfig.Dispatch.Ratio || whiteuid.Check(gameRequest.Uid) {
			resultData, ok := testergame.ExecHttpRequest(tt.teserTrafficConfig.Dispatch.Api, http.MethodPost, requestData)
			if ok == nil {
				ret, serverLog, err = parseDispatchResult(resultData)
				if err == nil {
					return ret, serverLog, nil
				} else {
					sugared.Errorf("parse dispatch result error: %s", err)
				}
			} else {
				sugared.Errorf("request Dispath Url: %s, error: %s", tt.teserTrafficConfig.Dispatch.Api, err)
			}
			return
		}
	}

	ret, err = tt.GameInit(gameRequest)
	if err == nil {
		ret.RequestId = gameRequest.RequestId
		ret.Uid = gameRequest.Uid
		ret.BundleId = gameRequest.BundleId
		if tt.multiLinks != nil && ret.GameWayNum != "" {
			if multiLink, ok := tt.multiLinks[gameRequest.BundleId]; ok {
				if linkData, err := multiLink.GetLink(ret.GameWayNum, gameRequest); err == nil {
					ret.LinkData = linkData
				}
			}
		}
	}

	metrics.InitCount.WithLabelValues(gameRequest.BundleId, strconv.Itoa(ret.BucketId)).Inc()
	return
}

func (tt *TesterTraffic) MakeServerSecret(reqId int64, bundleId, uid, exp string, bucketId int) (string, error) {
	key := os.Getenv("SECRET_KEY")
	if key == "" {
		sugared.Errorf("env SECRET_KEY not exist")
		return "", errors.New("env SECRET_KEY not exist")
	}
	return testergame.MakeServerSecret(key, bundleId, uid, exp, reqId, bucketId)
}

// GameInit 游戏初始化
func (tt *TesterTraffic) GameInit(gameRequest testergame.GameInitRequest) (testergame.ExperimentResult, error) {
	defer func() {
		if r := recover(); r != nil {
			sugared.Error("GameInit panic err: %v", r)
		}
	}()

	nowMilli := time.Now().UnixMilli()

	// 获取用户实验缓存
	if userExperiment, ok := tt.userExperiment.GetUser(gameRequest.BundleId, gameRequest.Uid); ok {
		gameRequest.UserExperiment = &userExperiment
		if iResult, err := tt.ExecInterceptors(&gameRequest); err == nil {
			return iResult, nil
		}
		// 获取用户当前分配的桶配置
		if bucketConfig, ok := tt.bucketConfigs.GetBucketConfig(gameRequest.BundleId, userExperiment.BucketId); ok {
			if tt.teserTrafficConfig.ABTestEnabled && bucketConfig.BucketType == testergame.BucketTypeABTest {
				return tt.execABTest(gameRequest, bucketConfig)
			}
			// 获取用户实验，是否在当前桶的实验列表里
			if exp, ok := bucketConfig.ExperimentList[userExperiment.ExperimentId]; ok {
				// 如果当前桶是新增实验类型，并且当前时间减去分配时间大于实验天数时间，则释放分流
				if bucketConfig.ExperimentType == testergame.ExperimentTypeNew {
					// 是否为升级用户，升级用户重新分配
					if gameRequest.IsUpgrade {
						go tt.bucketsFlow.Minus(gameRequest.BundleId, userExperiment.BucketId)
						return tt.bucketsDistribute(testergame.ExperimentTypeNew, gameRequest)
					}
					// 判断新增转活跃
					if (nowMilli - userExperiment.DistributeTime) > tt.bucketConfigs.GetBucketExperimentDaysTime(bucketConfig) {
						// 释放分流
						if expResult, err := tt.releaseDistribute(gameRequest, userExperiment.ExperimentId, bucketConfig); err == nil {
							return expResult, nil
						}
						// 在满足新增转活跃的前提下，如果没有配置有效的释放规则，则进行所有活动桶的释放
						return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
					}
					resetExp := tt.resetAIExp(bucketConfig, userExperiment.ExperimentId, exp, gameRequest.Uid)
					// 不满足新增转活跃，则返回空，表示当前配置维持不变
					return testergame.ExperimentResult{
						GameWayNum:       userExperiment.ExperimentId,
						BucketId:         userExperiment.BucketId,
						ExperimentType:   testergame.ExperimentTypeNew,
						ExperimentConfig: resetExp,
					}, nil
				}
				// 用户在活跃桶，则进行释放检查
				if expResult, err := tt.releaseDistribute(gameRequest, userExperiment.ExperimentId, bucketConfig); err == nil {
					return expResult, nil
				}
				resetExp := tt.resetAIExp(bucketConfig, userExperiment.ExperimentId, exp, gameRequest.Uid)
				// 没有对应的释放配置则保持当前实验不变
				return testergame.ExperimentResult{
					ExperimentConfig: resetExp,
					BucketId:         userExperiment.BucketId,
					ExperimentType:   userExperiment.ExperimentType,
					GameWayNum:       userExperiment.ExperimentId,
				}, nil
			} else {
				// 用户当前实验没有在当前桶的实验配置，则进行释放检查
				if expResult, err := tt.releaseDistribute(gameRequest, userExperiment.ExperimentId, bucketConfig); err == nil {
					return expResult, nil
				}

				// 增加用户14天判断
				if (nowMilli - gameRequest.InstallTime) > testergame.OldNewExperimentDadys {
					if exresult, err := tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest); err == nil {
						return exresult, nil
					}
					// 桶在打开关闭重新分配的时候，会在当前桶内重新分配。这种情况一般在桶流量关闭的情况下产生
					if bucketConfig.ExperimentType == testergame.ExperimentTypeActive && bucketConfig.CloseReset == testergame.CloseResetOpen {
						if expResult, err := tt.bucketExperimentsDistribute(userExperiment.BucketId, gameRequest); err == nil {
							return expResult, nil
						}
					}
				}
				return testergame.ExperimentResult{
					ExperimentConfig: nil,
					BucketId:         userExperiment.BucketId,
					ExperimentType:   userExperiment.ExperimentType,
					GameWayNum:       userExperiment.ExperimentId,
				}, nil
			}
		} else {
			// 增加用户14天判断
			if (nowMilli - gameRequest.InstallTime) > testergame.OldNewExperimentDadys {
				return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
			}
			return testergame.ExperimentResult{
				ExperimentConfig: nil,
				BucketId:         userExperiment.BucketId,
				ExperimentType:   userExperiment.ExperimentType,
				GameWayNum:       userExperiment.ExperimentId,
			}, nil
		}
	}

	if gameRequest.GameWayNum == "" {
		return tt.bucketsDistribute(testergame.ExperimentTypeNew, gameRequest)
	}

	if iResult, err := tt.ExecInterceptors(&gameRequest); err == nil {
		return iResult, nil
	}

	if gameRequest.IsClientPanel {
		sugared.Infof("IsClientPanel: %v", gameRequest)
		if bucketConfig, ok := tt.bucketConfigs.GetClientPanelBucket(gameRequest.BundleId, gameRequest.GameWayNum); ok {
			sugared.Infof("bucketConfig BucketId: %d, ExperimentType: %d, ExperimentList: %v, ReleaseExperiments: %v", bucketConfig.BucketId, bucketConfig.ExperimentType, maps.Keys(bucketConfig.ExperimentList), bucketConfig.ReleaseExperiments)
			if exp, ok := bucketConfig.ExperimentList[gameRequest.GameWayNum]; ok {
				sugared.Infof("nowMilli:%d, installTime: %d, ExperimentDaysTimes: %d", nowMilli, gameRequest.InstallTime, tt.bucketConfigs.GetBucketExperimentDaysTime(bucketConfig))
				if (nowMilli - gameRequest.InstallTime) > tt.bucketConfigs.GetBucketExperimentDaysTime(bucketConfig) {
					return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
				} else {
					return testergame.ExperimentResult{
						BucketId:         bucketConfig.BucketId,
						GameWayNum:       gameRequest.GameWayNum,
						ExperimentType:   bucketConfig.ExperimentType,
						ExperimentConfig: exp,
					}, nil
				}
			}
			// if releaseConfig, ok := bucketConfig.ReleaseExperiments[gameRequest.GameWayNum]; ok {
			// 	if releaseConfig.BucketId == testergame.ReleaseHoldBucket {
			// 		return ExperimentResult{}, nil
			// 	} else {
			// 		return tt.bucketExperimentsDistribute(releaseConfig.BucketId, gameRequest)
			// 	}
			// }
			// 释放分流
			if expResult, err := tt.releaseDistribute(gameRequest, gameRequest.GameWayNum, bucketConfig); err == nil {
				return expResult, nil
			} else {
				return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
			}
		} else {
			return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
		}
	}

	// 老用户桶分配
	if expResult, err := tt.oldBucket(gameRequest); err == nil {
		return expResult, nil
	}

	if (nowMilli - gameRequest.InstallTime) > testergame.OldNewExperimentDadys {
		return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
	} else {
		return testergame.ExperimentResult{}, nil
	}
}

// saveUserExperiment 保存用户分配实验缓存
func (tt *TesterTraffic) saveUserExperiment(bundleId string, distinctId string, bucketId int, experimentId string, experimentType int, distributeTime int64, csrIndentity string) {
	userExp := testergame.UserExperiment{
		BucketId:       bucketId,
		ExperimentId:   experimentId,
		ExperimentType: experimentType,
		DistributeTime: distributeTime,
	}
	testergame.AddUserDistributeLog(bundleId, distinctId, csrIndentity, userExp)
	tt.userExperiment.SaveUser(bundleId, distinctId, userExp)
}

// bucketsDistribute bucket第一层分流
func (tt *TesterTraffic) bucketsDistribute(experimentType int, gameRequest testergame.GameInitRequest) (testergame.ExperimentResult, error) {
	sugared.Infof("bucketsDistribute, uid: %s, experimentType: %d", gameRequest.Uid, experimentType)
	if buckets := tt.bucketConfigs.GetBucketConfigs(gameRequest.BundleId, experimentType, gameRequest.GameVersion, gameRequest.SdkVersion, gameRequest.Country, gameRequest.OneWayReleaseTag); buckets != nil {

		schemes := tt.bucketSchemes(buckets)
		if len(schemes) > 0 {
			bucketAlgoKey := fmt.Sprintf("hs:testergame:bucket:%s:%s:%s:%d", gameRequest.BundleId, gameRequest.GameVersion, gameRequest.SdkVersion, experimentType)
			algo := tt.algoProvider.GetDistributeAlgo(testergame.AlgoTypeBucket, bucketAlgoKey, schemes)
			if algo == nil {
				return testergame.ExperimentResult{}, errors.New("get bucket algo err")
			}
			bucketScheme := algo.Next()
			bucketId, _ := strconv.Atoi(bucketScheme.Name)
			return tt.bucketExperimentsDistribute(bucketId, gameRequest)
		}
	}
	return testergame.ExperimentResult{}, errors.New("no valid buckets")
}

// bucketSchemes 获取bucket schems
func (tt *TesterTraffic) bucketSchemes(buckets []testergame.BucketConfig) []distributes.Scheme {
	schemes := make([]distributes.Scheme, 0)
	//weight0Count := 0
	for _, bucket := range buckets {
		bucketFlowCount := tt.bucketsFlow.Get(bucket.BundleId, bucket.BucketId)
		// 超过最大流量不再进行bucket桶分配
		if bucketFlowCount > bucket.MaxFlowNum {
			continue
		}
		// 小时限流检查，设置小时限流大于0时，检查小时流量检查，超过小时限流，本小时内桶内不再进量
		bucketHourFlowCount, _ := tt.bucketsFlow.GetWithFlowType(testergame.BucketFlowTypeHour, bucket.BundleId, bucket.BucketId)
		if bucket.HourMaxFlowNum > 0 && bucketHourFlowCount > bucket.HourMaxFlowNum {
			continue
		}
		// BucketState 为online状态时进行桶分配
		if bucket.BucketState != testergame.BucketStateOnline {
			continue
		}
		if len(bucket.ExperimentList) == 0 && bucket.BucketType != testergame.BucketTypeABTest {
			continue
		}
		// 权重为0不进入分配
		if bucket.FlowWeightPercent == 0 {
			//weight0Count++
			continue
		}
		schemes = append(schemes, distributes.Scheme{Name: strconv.Itoa(bucket.BucketId), Weight: bucket.FlowWeightPercent})
	}
	// 如果全部全总都为0，则所有加入分配，并把权重都设置为1
	//if weight0Count == len(buckets) && len(schemes) == 0 {
	//	for _, bucket := range buckets {
	//		schemes = append(schemes, distributes.Scheme{Name: strconv.Itoa(bucket.BucketId), Weight: 1})
	//	}
	//}
	return schemes
}

// bucketDistribute bucket实验分配
func (tt *TesterTraffic) bucketExperimentsDistribute(bucketId int, gameRequest testergame.GameInitRequest) (testergame.ExperimentResult, error) {
	// 获取分流桶的配置
	sugared.Infof("bucketExperimentsDistribute, uid: %s, bucketId: %d", gameRequest.Uid, bucketId)
	if bucketConfig, ok := tt.bucketConfigs.GetBucketConfig(gameRequest.BundleId, bucketId); ok {
		if tt.teserTrafficConfig.ABTestEnabled && bucketConfig.BucketType == testergame.BucketTypeABTest {
			return tt.execABTest(gameRequest, bucketConfig)
		}
		bucketAlgoKey := fmt.Sprintf("hs:gametester:bucket:experiments:%s:%d", gameRequest.BundleId, bucketConfig.BucketId)
		var csrOriginType testergame.CSRParamOriginType
		var csrIndentity string
		var err error
		if tt.teserTrafficConfig.UseCSR {
			//switch bucketConfig.ExperimentType {
			//case testergame.ExperimentTypeActive, testergame.ExperimentTypeNew:
			//	csrType := testergame.CSRApplyTypeActive
			//	if bucketConfig.ExperimentType == testergame.ExperimentTypeNew {
			//		csrType = testergame.CSRApplyTypeNew
			//	}
			//	csrIndentity, csrOriginType, err = tt.csr.GetCSRIndentity(gameRequest.BundleId, csrType, gameRequest.Uid, gameRequest.CsrParams)
			//	if err == nil {
			//		bucketAlgoKey += ":" + csrIndentity
			//	}
			//}
			if bucketConfig.ExperimentType == testergame.ExperimentTypeActive {
				csrType := testergame.CSRApplyTypeActive
				if bucketConfig.ExperimentType == testergame.ExperimentTypeNew {
					csrType = testergame.CSRApplyTypeNew
				}
				csrIndentity, csrOriginType, err = tt.csr.GetCSRIndentity(gameRequest.BundleId, csrType, gameRequest.Uid, gameRequest.CsrParams)
				if err == nil {
					bucketAlgoKey += ":" + csrIndentity
				}
			}
		}
		algo := tt.algoProvider.GetDistributeAlgo(testergame.AlgoTypeGameScheme, bucketAlgoKey, tt.bucketExperimentSchemes(bucketConfig))
		if algo == nil {
			return testergame.ExperimentResult{}, errors.New("get bucket experiment algo err")
		}
		scheme := algo.Next()
		// 修正installTime，如果installTime比服务器当前时间大，则以服务器时间为准
		var distributeTime int64
		nowTime := time.Now().UnixMilli()
		if gameRequest.InstallTime > nowTime {
			distributeTime = nowTime
		} else {
			distributeTime = gameRequest.InstallTime
		}
		if exp, ok := bucketConfig.ExperimentList[scheme.Name]; ok {
			// if exp, err := tt.bucketConfigs.GetExperiment(gameRequest.BundleId, bucketId, scheme.Name); err == nil {
			go tt.saveUserExperiment(gameRequest.BundleId, gameRequest.Uid, bucketId, scheme.Name, bucketConfig.ExperimentType, distributeTime, csrIndentity)
			go tt.bucketsFlow.Increase(gameRequest.BundleId, bucketId)
			// 检查重置AI特征
			if bucketConfig.BucketType == testergame.BucketTypeAI {
				exp = tt.resetAIExp(bucketConfig, scheme.Name, exp, gameRequest.Uid)
			}
			sugared.Infof("bucketExperimentsDistribute uid: %s, Bucket: %d, Experiment: %s,ExperimentType: %d", gameRequest.Uid, bucketId, scheme.Name, bucketConfig.ExperimentType)
			experimentResult := testergame.ExperimentResult{
				ExperimentConfig: exp,
				GameWayNum:       scheme.Name,
				BucketId:         bucketId,
				ExperimentType:   bucketConfig.ExperimentType,
			}
			if csrIndentity != "" {
				experimentResult.CSRIndentity = csrIndentity
				experimentResult.CSRParamOrigin = csrOriginType
			}
			return experimentResult, nil
		}
	}
	return testergame.ExperimentResult{}, fmt.Errorf("bundle: %s,no bucketId: %d", gameRequest.BundleId, bucketId)
}

// bucketExperimentSchemes 获取实验分流schemes
func (tt *TesterTraffic) bucketExperimentSchemes(bucketConfig testergame.BucketConfig) []distributes.Scheme {
	if expSchemes := tt.bucketConfigs.GetBucketDisExperiments(bucketConfig.BundleId, bucketConfig.BucketId); expSchemes != nil {
		return slices.Clone(expSchemes)
	}
	return nil
}

// execOldReleaseDistribute 原分流策略
func (tt *TesterTraffic) execOldReleaseDistribute(gameRequest testergame.GameInitRequest, experimentId string, bucketConfig testergame.BucketConfig) (testergame.ExperimentResult, error) {
	// 检查实验是否在释放配置中
	sugared.Infof("releaseDistribute, uid: %s, experimentId: %s", gameRequest.Uid, experimentId)
	if releaseConfig, ok := bucketConfig.ReleaseExperiments[experimentId]; ok {
		return tt.execReleaseConfig(releaseConfig, gameRequest)
	}
	// 没有释放配置，则返回空，并返回错误，无有效的释放分流配置
	return testergame.ExperimentResult{}, errors.New("no valid release distribute")
}

// execReleaseSettingsDistribute 新分流策略，控制里更强
func (tt *TesterTraffic) execReleaseSettingsDistribute(gameRequest testergame.GameInitRequest, experimentId string, bucketConfig testergame.BucketConfig) (testergame.ExperimentResult, error) {
	if releaseSetting := testergame.GetReleaseSetting(experimentId, bucketConfig.ReleaseSettings); releaseSetting != nil {
		var releaseConfig testergame.ReleaseConfig
		var err error
		switch releaseSetting.ReleaseType {
		case testergame.ReleaseTypeTraffic:
			releaseConfig, err = releaseSetting.ExecTrafficAllocation(gameRequest.Uid)
		case testergame.ReleaseTypeWeight:
			releaseConfig, err = releaseSetting.ExecWeightAllocation(gameRequest.BundleId, bucketConfig.BucketId, tt.algoProvider)
		default:
			err = errors.New("unknown releaseType:" + releaseSetting.ReleaseType)
		}
		if err != nil {
			return testergame.ExperimentResult{}, err
		}
		return tt.execReleaseConfig(releaseConfig, gameRequest)
	}
	return testergame.ExperimentResult{}, errors.New("no valid release distribute")
}

// execReleaseConfig 执行释放配置
func (tt *TesterTraffic) execReleaseConfig(releaseConfig testergame.ReleaseConfig, gameRequest testergame.GameInitRequest) (testergame.ExperimentResult, error) {
	// 检查释放配置的桶ID是否大于0，大于0可以分配，否则，不进行分配保持现有配置，返回空
	if releaseConfig.BucketId > 0 {
		releaseBucket, ok := tt.bucketConfigs.GetBucketConfig(gameRequest.BundleId, releaseConfig.BucketId)
		if !ok {
			// 指定的释放桶无效，则返回空，不进行分配 这里的逻辑需要和研发确认一下
			// 获取释放配置对应的释放桶配置，找不到释放的桶，则配置失效，则表示没有释放，返回空
			sugared.Errorf("releaseBucket: %d not found", releaseConfig.BucketId)
			return testergame.ExperimentResult{}, fmt.Errorf("get release bucket: %d experiment err", releaseConfig.BucketId)
		}
		// 检查用户是否有桶进入条件
		if !releaseBucket.Conditions.EvaluateFilters(gameRequest.GetBucketEntryConditions()) {
			sugared.Infof("条件不符，不能进入桶:%d，保持原方案不变", releaseConfig.BucketId)
			return testergame.ExperimentResult{}, nil
		}
		// 检查释放配置中，实验配置否为空，不为空，则分流到指定桶的指定实验，否则，在桶内进行轮询分配
		if releaseConfig.ExperimentId != "" {
			// 检查释放桶中的实验列表是否存在实验配置
			if exp, ok := releaseBucket.ExperimentList[releaseConfig.ExperimentId]; ok {
				sugared.Infof("releaseDistribute uid: %s,Bucket: %d, Experiment:%s", gameRequest.Uid, releaseBucket.BucketId, releaseConfig.ExperimentId)
				go tt.saveUserExperiment(gameRequest.BundleId, gameRequest.Uid, releaseConfig.BucketId, releaseConfig.ExperimentId, releaseBucket.ExperimentType, gameRequest.InstallTime, "")
				return testergame.ExperimentResult{
					GameWayNum:       releaseConfig.ExperimentId,
					BucketId:         releaseConfig.BucketId,
					ExperimentType:   releaseBucket.ExperimentType,
					ExperimentConfig: exp,
				}, nil
				// sugared.Errorf("tt.bucketConfigs.GetExperiment(BucketId:%d, ExperimentId: %s), Error: %v", releaseBucket.BucketId, releaseConfig.ExperimentId, err)// 获取实验配置失败，则不进行分配，返回空
				// return ExperimentResult{}, nil
			}
			return tt.bucketExperimentsDistribute(releaseBucket.BucketId, gameRequest)
			//return ExperimentResult{}, nil
		}
		// 释放实验为空，则在桶内释放分配
		return tt.bucketExperimentsDistribute(releaseConfig.BucketId, gameRequest)
	} else if releaseConfig.BucketId == testergame.ReleaseGlobal {
		return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
	}
	// 释放桶ID不大于零，则保持现有配置，不释放，返回空
	return testergame.ExperimentResult{}, nil
}

// releaseDistribute 执行桶的释放分流
func (tt *TesterTraffic) releaseDistribute(gameRequest testergame.GameInitRequest, experimentId string, bucketConfig testergame.BucketConfig) (testergame.ExperimentResult, error) {
	// 旧的释放规则配置，则优先走旧的释放逻辑
	if len(bucketConfig.ReleaseExperiments) > 0 {
		return tt.execOldReleaseDistribute(gameRequest, experimentId, bucketConfig)
	}
	// 新的释放逻辑
	if len(bucketConfig.ReleaseSettings) > 0 {
		return tt.execReleaseSettingsDistribute(gameRequest, experimentId, bucketConfig)
	}
	return testergame.ExperimentResult{}, errors.New("no valid release distribute")
}

// oldBucket 老用户桶释放 根据本地老用户所在桶，由客户端对应
func (tt *TesterTraffic) oldBucket(gameRequest testergame.GameInitRequest) (testergame.ExperimentResult, error) {
	sugared.Infof("oldBucket, uid: %s", gameRequest.Uid)
	nowMilli := time.Now().UnixMilli()
	// 老用户桶有效
	if gameRequest.OldBucket > 0 {
		// 获取老用户桶
		if bucketConfig, ok := tt.bucketConfigs.GetBucketConfig(gameRequest.BundleId, gameRequest.OldBucket); ok {
			// 新增转活跃
			if bucketConfig.ExperimentType == testergame.ExperimentTypeNew {
				if (nowMilli - gameRequest.InstallTime) > tt.bucketConfigs.GetBucketExperimentDaysTime(bucketConfig) {
					// 释放分流
					if expResult, err := tt.releaseDistribute(gameRequest, gameRequest.GameWayNum, bucketConfig); err == nil {
						return expResult, nil
					}
					return tt.bucketsDistribute(testergame.ExperimentTypeActive, gameRequest)
				}
			}

			// 检查ExperimentList是否有指定实验，如果指定实现不为空，则返回指定配置，为空则返回空
			if exp, ok := bucketConfig.ExperimentList[gameRequest.GameWayNum]; ok {
				sugared.Infof("oldBucket uid: %s, Bucket: %d,Experiment: %s", gameRequest.Uid, gameRequest.OldBucket, gameRequest.GameWayNum)
				return testergame.ExperimentResult{
					GameWayNum:       gameRequest.GameWayNum,
					BucketId:         gameRequest.OldBucket,
					ExperimentType:   bucketConfig.ExperimentType,
					ExperimentConfig: exp,
				}, nil
			}
			// 实验列表中没有指定实现，则走释放流程
			return tt.releaseDistribute(gameRequest, gameRequest.GameWayNum, bucketConfig)
		}
	}
	return testergame.ExperimentResult{}, fmt.Errorf("no old bucket: %d", gameRequest.OldBucket)
}

func (tt *TesterTraffic) resetAIExp(bucketConfig testergame.BucketConfig, expId string, exp map[string]any, uid string) map[string]any {
	var ok bool
	var aiFeatures map[string]map[string]any
	var aiFeatureIndex map[int]int
	var userFeaures map[string]int
	if bucketConfig.BucketType != testergame.BucketTypeAI {
		sugared.Infof("BucketType: %s not ai bucket", bucketConfig.BucketType)
		return exp
	}
	// 获取配置AI特征
	if aiFeatures, ok = bucketConfig.AIFeatures[expId]; !ok {
		sugared.Infof("ExpId: %s no ai features", expId)
		return exp
	}
	// 获取应用ai特征桶的方案的特征索引
	if aiFeatureIndex = tt.bucketConfigs.GetAIFeatureIndexes(bucketConfig.BundleId, expId); aiFeatureIndex == nil {
		sugared.Infof("Bundle: %s,Bucket: %d Not Setting AI Feature", bucketConfig.BundleId, bucketConfig.BucketId)
		return exp
	}
	// 获取用户的ai特征设置
	if aiFeatureConfig, ok := tt.teserTrafficConfig.AIFeautures[bucketConfig.BundleId]; ok {
		var err error
		if userFeaures, err = testergame.GetUserAIFeatures(uid, aiFeatureConfig.CacheKeyPrefix); err != nil {
			sugared.Infof("User: %s, Not Setting AI Feature, err: %v", uid, err)
			return exp
		}
		sugared.Infof("User: %s, AI Features: %+v", uid, userFeaures)
	} else {
		sugared.Infof("Bundle: %s Not Setting AI Feature", bucketConfig.BundleId)
	}

	newFeatures := make([]any, 0)
	resetFeatures := make(map[string]int)
	// 重组特征
	if expFeaturesAny, ok := exp["features"]; ok {
		if expFeatures, ok := expFeaturesAny.([]any); ok {
			sugared.Infof("minus features")
			for _, featureInfoAny := range expFeatures {
				featureInfo := featureInfoAny.(map[string]any)
				idAny, ok := featureInfo["id"]
				if !ok {
					continue
				}
				idFloat := idAny.(float64)
				idInt := int(idFloat)
				idStr := strconv.Itoa(idInt)
				if v, ok := userFeaures[idStr]; ok {
					delete(userFeaures, idStr)
					resetFeatures[idStr] = v
					if v == testergame.AIFeatureMinus {
						continue
					}
				}
				newFeatures = append(newFeatures, featureInfo)
			}
			sugared.Infof("append features")
			for featureId, v := range userFeaures {
				if v == testergame.AIFeatureAdd {
					if feature, ok := aiFeatures[featureId]; ok {
						newFeatures = append(newFeatures, feature)
						resetFeatures[featureId] = testergame.AIFeatureAdd
					}
				}
			}
		}
	}
	sugared.Infof("User: %s,ResetFeatures: %+v", uid, resetFeatures)
	if len(newFeatures) > 0 {
		if tt.teserTrafficConfig.QA {
			sugared.Infof("Uid: %s,OriginFeature: %+v,newFeatures: %+v", uid, exp["features"], newFeatures)
		}
		originExp := maps.Clone(exp)
		originExp["features"] = newFeatures
		return originExp
	}

	return exp
}

func (tt *TesterTraffic) SetABTestFeatures(f *testergame.ABTestFeatures) {
	tt.abtest = f
}

func (tt *TesterTraffic) TransABTestTypeTo2(ABTestType int) testergame.CSRApplyType {
	switch ABTestType {
	case testergame.UTypeActive:
		return testergame.CSRApplyTypeNew
	case testergame.UTypeNew:
		return testergame.CSRApplyTypeActive
	default:
		return testergame.CSRApplyTypeActive
	}
}

func (tt *TesterTraffic) execABTest(gameRequest testergame.GameInitRequest, bucketConfig testergame.BucketConfig) (testergame.ExperimentResult, error) {
	var scheme string
	bFlowInc := true
	defer func() {
		if bFlowInc {
			if gameRequest.UserExperiment == nil || gameRequest.UserExperiment.BucketId != bucketConfig.BucketId {
				go tt.bucketsFlow.Increase(gameRequest.BundleId, bucketConfig.BucketId)
			}
		}
		if kafkaRemote != nil {
			data, err := json.Marshal(map[string]any{
				"bundle_id":    gameRequest.BundleId,
				"distinct_id":  gameRequest.Uid,
				"bucket_id":    bucketConfig.BucketId,
				"user_way_num": scheme,
				"ts":           time.Now().Unix(),
			})
			if err == nil {
				kafkaRemote.Push("topic_ab_result", gameRequest.Uid, data)
			} else {
				sugared.Errorf("json.Marshal err %v", err)
			}
		}
		go tt.userExperiment.SaveUser(gameRequest.BundleId, gameRequest.Uid, testergame.UserExperiment{
			BucketId:       bucketConfig.BucketId,
			ExperimentId:   scheme,
			ExperimentType: bucketConfig.ExperimentType,
			DistributeTime: gameRequest.InstallTime,
		})
	}()
	if tt.abtest == nil {
		bFlowInc = false
		sugared.Errorf("abtest not setting")
		return testergame.ExperimentResult{}, fmt.Errorf("no ABTest")
	}
	abTestRequest := testergame.ABTestRequest{
		Uid:        gameRequest.Uid,
		BundleId:   gameRequest.BundleId,
		Attributes: map[string]any{},
		LayerIds:   gameRequest.LayerIds,
	}
	nowMilli := time.Now().UnixMilli()
	utype := 0
	if bucketConfig.ExperimentType == testergame.ExperimentTypeNew {
		// 新增用户热更新处理
		// if gameRequest.UserExperiment != nil && gameRequest.IsUpgrade {
		// 	bFlowInc = false
		// 	sugared.Infof("is upgrade, not exec abtest")
		// 	_ = tt.abtest.RequestLeaveApi(abTestRequest)
		// }
		if (nowMilli - gameRequest.InstallTime) > tt.bucketConfigs.GetBucketExperimentDaysTime(bucketConfig) {
			bFlowInc = false
			newbucketConfig, ok := tt.bucketConfigs.GetABActiveBucket(bucketConfig)
			if ok {
				bucketConfig = newbucketConfig
			} else {
				sugared.Errorf("no active bucket bid %d", bucketConfig.BucketId)
			}
			abTestRequest.Attributes["active_type"] = testergame.UTypeActive
			utype = testergame.UTypeActive
		} else {
			abTestRequest.Attributes["active_type"] = testergame.UTypeNew
			utype = testergame.UTypeNew
		}
	} else {
		abTestRequest.Attributes["active_type"] = testergame.UTypeActive
		utype = testergame.UTypeActive
	}
	if bucketConfig.ABTestProjectKey == "" {
		bFlowInc = false
		sugared.Errorf("abtest project key is empty")
		return testergame.ExperimentResult{}, fmt.Errorf("no ABTest")
	}
	//sugared.Infof("dddddddd ExpermentDays:%d, %d, %d", bucketConfig.ExpermentDays, nowMilli-gameRequest.InstallTime, tt.bucketConfigs.GetBucketExperimentDaysTime(bucketConfig))

	abTestRequest.Attributes["game_version"] = gameRequest.GameVersion
	abTestRequest.Attributes["sdk_version"] = gameRequest.SdkVersion
	abTestRequest.Attributes["gameway_num"] = gameRequest.GameWayNum
	abTestRequest.Attributes["is_client_panel"] = gameRequest.IsClientPanel
	abTestRequest.Attributes["old_exp"] = gameRequest.OldBucket
	abTestRequest.Attributes["country"] = gameRequest.Country
	abTestRequest.Attributes["is_upgrade"] = gameRequest.IsUpgrade
	abTestRequest.ProjectKey = bucketConfig.ABTestProjectKey

	// add csr
	if tt.teserTrafficConfig.UseCSR {
		for k, v := range gameRequest.CsrParams {
			if _, exist := abTestRequest.Attributes[k]; !exist {
				abTestRequest.Attributes[k] = v
			} else {
				sugared.Errorf("abTestRequest Attributes: %s already exist", k)
			}
		}
	}

	//
	abResponse, err := tt.abtest.Request(abTestRequest)
	if err == nil {
		var csrIndentity string
		scheme, csrIndentity = testergame.ParseGameWayNum3(utype, abResponse)
		sugared.Infof("abTestRequest: %+v, abResponse: %+v", abTestRequest, abResponse)
		pr := abResponse.Data.Params
		if scheme == "" || pr == nil || len(pr) == 0 {
			sugared.Warnf("abtest return nothing scheme: %s, pr: %v", scheme, pr)
			return testergame.ExperimentResult{}, nil
		}
		return testergame.ExperimentResult{
			GameWayNum:       scheme,
			ExperimentConfig: abResponse.Data.Params,
			BucketId:         bucketConfig.BucketId,
			ExperimentType:   bucketConfig.ExperimentType,
			CSRIndentity:     csrIndentity,
			ABTestResult:     abResponse.Data.CompressHit(),
		}, nil
	}
	sugared.Errorf("abTestRequest: %+v, abResponse: %+v exec err:%v", abTestRequest, abResponse, err.Error())
	// sugared.Errorf("abtest exec err: %v", err)
	return testergame.ExperimentResult{}, nil
}

// Close 关闭TesterTraffic
func (tt *TesterTraffic) Close(cxt context.Context) {
	tt.algoProvider.Close(cxt)
}

//func (tt *TesterTraffic) SaveGameTesterConfigs(bundleId string, data []byte) error {
//	return tt.bucketConfigs.Save(bundleId, data)
//}

func (tt *TesterTraffic) GetGameTesterConfigs(bundleId string) ([]testergame.BucketConfig, error) {
	return tt.bucketConfigs.GetAllWithBundleId(bundleId)
}

func (tt *TesterTraffic) GetGameTesterAllConfigs(bundleId string) ([]testergame.BucketConfig, error) {
	return tt.bucketConfigs.GetAllConfigsWithBundleId(bundleId)
}

func (tt *TesterTraffic) GetBundleBucketFlow(bundleId string, bucketId int, flowType string) int {
	count, err := tt.bucketsFlow.GetWithFlowType(flowType, bundleId, bucketId)
	if err != nil {
		sugared.Errorf("Get bucket flow err: %v", err)
		return 0
	}
	return count
	//return tt.bucketsFlow.Get(bundleId, bucketId)
}
func (tt *TesterTraffic) GetBundleFlow(bundleId string) map[int]int {
	return tt.bucketsFlow.GetBundle(bundleId)
}
func (tt *TesterTraffic) GetGameTesterConfigsNew() []testergame.BucketConfig {
	return tt.bucketConfigs.GetAllNew()
}

func (tt *TesterTraffic) AddQA(data []byte) error {
	sugared.Infof("QA.Add: %s", data)
	qaConfig := testergame.DBTestergameQaConfig{}
	err := json.Unmarshal(data, &qaConfig)
	if err != nil {
		userMappingConfig := testergame.UserMappingQaConfig{}
		err = json.Unmarshal(data, &userMappingConfig)
		if err != nil {
			sugared.Errorf("addUserMappingQa json.Unmarshal Err: %v", err)
			return err
		}
		if userMappingConfig.Uid == "" {
			sugared.Error("addUserMappingQa Uid is empty")
			return errors.New("uid is empty")
		}
		sugared.Infof("AddUserMappingQa config: %v", userMappingConfig)
		_ = tt.qaConfigs.AddQAUserMappingConfig(os.Getenv("GAME_BUNDLE_ID"), userMappingConfig)
		return nil
		//return err
	}
	if qaConfig.Bundleid != "" && qaConfig.Bucket > 0 {
		if _, ok := tt.bucketConfigs.GetBucketExperiment(qaConfig.Bundleid, qaConfig.Bucket, qaConfig.Experiment); !ok {
			return fmt.Errorf("bundle:%s no bucket:%d experiment:%s found", qaConfig.Bundleid, qaConfig.Bucket, qaConfig.Experiment)
		}
		return tt.qaConfigs.AddQAConfig(os.Getenv("GAME_BUNDLE_ID"), qaConfig)
	}
	return fmt.Errorf("no bucket:%d experiment:%s found", qaConfig.Bucket, qaConfig.Experiment)
}

func (tt *TesterTraffic) GetQABucket(uid string) int {
	cacheKey := fmt.Sprintf("hs:testergame:qa:bucket:%s", uid)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	bucketid, err := redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		sugared.Errorf("GetQABucket Err: %v", err)
		return 0
	}
	bucketId, _ := strconv.Atoi(bucketid)
	sugared.Infof("GetQABucket, cacheKey: %s, bucketId: %d", cacheKey, bucketId)
	return bucketId
}

func (tt *TesterTraffic) AimBucketQA(data []byte) error {
	list := make(map[string][]string, 0)
	err := json.Unmarshal(data, &list)
	if err != nil {
		sugared.Errorf("AimBucketQA json.Unmarshal Err: %v", err)
		return err
	}
	for bucketid, uids := range list {
		for _, uid := range uids {
			cacheKey := fmt.Sprintf("hs:testergame:qa:bucket:%s", uid)
			ret, err := redisClient.Set(context.Background(), cacheKey, bucketid, 24*time.Hour*7).Result()
			sugared.Infof("AimBucketQA Set ret:%s, cacheKey:%s, bucketid:%s, err:%v", ret, cacheKey, bucketid, err)
		}
	}
	return nil
}

//func (tt *TesterTraffic) SaveQA(data []byte) error {
//	return tt.qaConfigs.SaveQAConfigs(data)
//}
//
//func (tt *TesterTraffic) GetQAConfigs() map[string]map[string]testergame.QAConfig {
//	if tt.teserTrafficConfig.QA {
//		return tt.qaConfigs.GetQAConfigs()
//	}
//	return nil
//}
//
//func (tt *TesterTraffic) GetQAMappingConfigs() map[string]testergame.UserMappingQaConfig {
//	if tt.teserTrafficConfig.QA {
//		return tt.qaConfigs.GetQAMapping()
//	}
//	return nil
//}

func (tt *TesterTraffic) CheckBucketFlowLimit(bundleId string, url string, at []string, keyContent string) {
	//buckets, err := tt.bucketConfigs.GetAllWithBundleId(bundleId)
	//if err != nil {
	//	return
	//}
	sugared.Infof("MonitorBucketMaxFlow: %s Stop", bundleId)
	//for _, bucket := range buckets {
	//	sugared.Infof("BucketID: %d", bucket.BucketId)
	//	bucketFlow := tt.bucketsFlow.Get(bucket.BundleId, bucket.BucketId)
	//	if bucketFlow > bucket.MaxFlowNum {
	//		unixTime, err := tt.bucketsFlow.SetMonitorMax(bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow)
	//		if err != nil {
	//			sugared.Infof("Bunlde:%s, Bucket: %d, MaxFlowNum: %d,Count: %d, SetMonitorMax Err: %v", bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow, err)
	//			continue
	//		}
	//		if unixTime > 0 {
	//			//bucketResult := map[string]any{
	//			//	"bucketId":     bucket.BucketId,
	//			//	"maxFlowNum":   bucket.MaxFlowNum,
	//			//	"count":        bucketFlow,
	//			//	"limitMaxTime": time.UnixMilli(unixTime).Format(time.RFC3339),
	//			//}
	//			//location, err := time.LoadLocation("Asia/Shanghai")
	//			//if err != nil {
	//			//	sugared.Errorf("LoadLocation Err: %v", err)
	//			//	continue
	//			//}
	//			localTime := time.UnixMilli(unixTime).Format("2006-01-02 15:04:05")
	//			content := &bytes.Buffer{}
	//			content.WriteString("包：" + bucket.BundleId + "\n")
	//			content.WriteString("桶ID:")
	//			content.WriteString(strconv.Itoa(bucket.BucketId))
	//			content.WriteString("\n")
	//			content.WriteString("最大人数:")
	//			content.WriteString(strconv.Itoa(bucket.MaxFlowNum))
	//			content.WriteString("\n")
	//			content.WriteString("当前人数:")
	//			content.WriteString(strconv.Itoa(bucketFlow))
	//			content.WriteString("\n")
	//			content.WriteString("已满量:")
	//			content.WriteString(localTime)
	//			sugared.Infof("MaxFlowNum: %d,Count: %d,Time: %s", bucket.MaxFlowNum, bucketFlow, localTime)
	//			// 发送满量消息
	//			err = notify.DDNotifyWithUrl(url, content.Bytes(), at, keyContent)
	//			if err != nil {
	//				sugared.Errorf("notify.DDNotifyWithUrl, Url: %s err: %v", url, err)
	//			}
	//			sugared.Infof("DD Send Url: %s, At: %v", url, at)
	//			//result = append(result, bucketResult)
	//			sugared.Infof("Bundle: %s, Bucket: %d, MaxFlowNum: %d, Count: %d, SetMonitorMax Success,UpdateTime: %s", bucket.BundleId, bucket.BucketId, bucket.MaxFlowNum, bucketFlow, time.UnixMilli(unixTime).Format(time.RFC3339))
	//		}
	//	}
	//}
}

func (tt *TesterTraffic) GetMonitorBucketFlow(bundleId string) map[int]testergame.MonitorBucketMax {
	buckets, err := tt.bucketConfigs.GetAllWithBundleId(bundleId)
	if err != nil {
		sugared.Errorf("GetMonitorBucketFlow err: %v", err)
		return nil
	}
	return tt.bucketsFlow.GetMonitorMax(buckets)
}

func parseDispatchResult(data []byte) (er testergame.ExperimentResult, sl string, err error) {
	result := struct {
		ExperimentDataMap struct {
			DefaultLayer testergame.ExperimentResult `json:"defaultLayer"`
		} `json:"experimentDataMap"`
		ServerLog string `json:"serverLog"`
	}{}
	err = json.Unmarshal(data, &result)
	if err != nil {
		return er, sl, err
	}
	return result.ExperimentDataMap.DefaultLayer, result.ServerLog, nil
}

func VerifyBucket(bucketConfig testergame.BucketConfig, gameRequest testergame.GameInitRequest) {

}
