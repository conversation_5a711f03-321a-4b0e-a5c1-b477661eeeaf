package models

// AdConfig ad_info 配置，包含条件，返回时删除
//type AdConfig map[string]any
//
//const AdConfigIsFirstStatusKey = "is_first_status"
//
//// resetIsFirst 重置isfirst变量，是当天首次请求 依据UTC时间
//func (ac AdConfig) resetIsFirst(bundleId, thinkUid string) {
//	ac["isfirst"] = NoFirstDay
//	if open, ok := ac[AdConfigIsFirstStatusKey].(string); ok && open == "open" {
//		if CheckDayFirst(bundleId, thinkUid) {
//			sugared.Infof("thinkuid : %s is first", thinkUid)
//			ac["isfirst"] = IsFirstDay
//		}
//		delete(ac, "is_first_status")
//	}
//}
//
//// checkCountryCodeByList 检查国家列表，通过list参数
//func (ac AdConfig) checkCountryCode(listParamName string, countryCode string) bool {
//	if countryList, ok := ac[listParamName].([]string); ok {
//		if slices.Contains(countryList, countryCode) {
//			return true
//		}
//		delete(ac, listParamName)
//	}
//	return false
//}
//
//// checkCombination 联合条件检查
//func (ac AdConfig) checkCombination(combination []string, params map[string]any) bool {
//	cCount := len(combination)
//	switch cCount {
//	case 1:
//		cParamName := combination[0]
//		return ac.checkCombinationParam(params, cParamName)
//	case 3:
//		operate := combination[2]
//		result := make([]bool, 2)
//		for i, cParamName := range combination[:1] {
//			cResult := ac.checkCombinationParam(params, cParamName)
//			if operate == "&&" && !cResult {
//				return false
//			} else if operate == "||" && cResult {
//				return true
//			}
//			result[i] = cResult
//		}
//		if operate == "&&" {
//			return result[0] && result[1]
//		} else if operate == "||" {
//			return result[0] || result[1]
//		}
//	}
//	delete(ac, "combination")
//	return false
//}
//
//// checkCombinationParam 联合条件参数检查
//func (ac AdConfig) checkCombinationParam(params map[string]any, cParamName string) bool {
//	result := false
//	cParamNameLen := len(cParamName)
//	if cParamName[cParamNameLen-8:] == "_contain" {
//		cParamName = cParamName[:cParamNameLen-8]
//		if pValue, ok := params[cParamName].(string); ok && pValue != "" {
//			if values, ok := ac[cParamName].([]string); ok {
//				for _, value := range values {
//					if strings.Contains(value, pValue) {
//						result = true
//					}
//				}
//			}
//		}
//	} else {
//		if pValue, ok := params[cParamName].(string); ok && pValue != "" {
//			if values, ok := ac[cParamName].([]string); ok {
//				if slices.Contains(values, pValue) {
//					result = true
//				}
//			}
//		}
//	}
//	delete(ac, cParamName)
//	return result
//}
//
//const (
//	AdsKeyPreinstall   = "preinstall"
//	AdsKeyRoas         = "roas"
//	AdsKeyRoasLimit    = "roas_limit"
//	AdsKeyRoasLimit2   = "roas_limit2"
//	AdsKeyCountryCode  = "country_code"
//	AdsKeyCountryCode2 = "country_code2"
//	AdsKeyCountryCode3 = "country_code3"
//	AdsKeyCountryCode4 = "country_code4"
//	AdsKeyDefaul       = "default"
//)
//
//var adsFilterKeys = []string{
//	AdsKeyPreinstall,
//	AdsKeyRoas,
//	AdsKeyRoasLimit,
//	AdsKeyRoasLimit2,
//	AdsKeyCountryCode,
//	AdsKeyCountryCode2,
//	AdsKeyCountryCode3,
//	AdsKeyCountryCode4,
//	AdsKeyDefaul,
//}
//
//type AdsConfigSelectorFn func(adConfigs AdConfigs, params map[string]any) (AdConfig, bool)
//
//var adsFilterMaps = map[string]AdsConfigSelectorFn{
//	AdsKeyPreinstall:   preinstallSelector,
//	AdsKeyRoas:         roasSelector,
//	AdsKeyRoasLimit:    roasLimitSelector,
//	AdsKeyRoasLimit2:   roasLimit2Selector,
//	AdsKeyCountryCode:  countryCodeSelector,
//	AdsKeyCountryCode2: countryCode2Selector,
//	AdsKeyCountryCode3: countryCode3Selector,
//	AdsKeyCountryCode4: countryCode4Selector,
//	AdsKeyDefaul:       defaultSelector,
//}
//
//var adsFilters = []AdsConfigSelectorFn{
//	preinstallSelector,
//	roasSelector,
//	roasLimitSelector,
//	roasLimit2Selector,
//	countryCodeSelector,
//	countryCode2Selector,
//	countryCode3Selector,
//	countryCode4Selector,
//	defaultSelector,
//}
//
//// AdConfigs 所有的ad_info配置
//type AdConfigs map[string]any
//
//type AdOtherConfigs map[string]any
//
//// selectAdConfig 选择符合条件的AdConfig
//func (ads AdConfigs) selectAdConfig(params map[string]any) AdConfig {
//	for _, adsFilterKey := range adsFilterKeys {
//		if _, ok := ads[adsFilterKey]; ok {
//			if fn, ok := adsFilterMaps[adsFilterKey]; ok {
//				if adConfig, ok := fn(ads, params); ok {
//					return adConfig
//				}
//			}
//		}
//	}
//	return nil
//}
//
//const (
//	ABTesterKeyName = "huoshan" // AdConfigs 中判断是否要参与火山ABTester的键
//	SwitchKeyName   = "switch"  // AdConfigs 中switch配置键名
//	DidiKeyName     = "didi"    // AdConfigs 中滴滴分流标识
//)
//
//// checkABTester 检查是否要执行火山ABTester
//func (ads AdConfigs) checkABTester() bool {
//	if _, ok := ads[ABTesterKeyName]; ok {
//		delete(ads, ABTesterKeyName)
//		return true
//	}
//	return false
//}
//
//// getDidiSchemes 获取滴滴分案列表
//func (ads AdConfigs) getDidiSchemes() []string {
//	if schems, ok := ads[DidiKeyName].([]string); ok {
//		delete(ads, DidiKeyName)
//		return schems
//	}
//	return nil
//}
//
//// checkDidi 检查是否为滴滴分流
//func (ads AdConfigs) checkDidi() bool {
//	if _, ok := ads[DidiKeyName]; ok {
//		return true
//	}
//	return false
//}
//
//// getSwitchConfigs 获取开关配置
//func (ads AdConfigs) getSwitchConfig() SwitchConfig {
//	if switchConfig, ok := ads[SwitchKeyName]; ok {
//		delete(ads, SwitchKeyName)
//		return switchConfig.(map[string]any)
//	}
//	return nil
//}
//
//// resetAdInfoKey 更改ad_info为default
//func (ads AdConfigs) resetAdInfoKey() {
//	if v, ok := ads["ad_info"]; ok {
//		ads["defalut"] = v
//		delete(ads, "ad_info")
//	}
//}
//
//// preinstallSelector 选择preinstall 根据mediaSource
//func preinstallSelector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if mediaSource, ok := params["media_source"].(string); ok && mediaSource != "" {
//		if adConfigAny, ok := adConfigs[AdsKeyPreinstall]; ok {
//			adConfig := adConfigAny.(map[string]any)
//			if list, ok := adConfig["list"].([]string); ok {
//				if slices.Contains(list, mediaSource) {
//					return adConfig, true
//				}
//			}
//		}
//	}
//	return AdConfig{}, false
//}
//
//// roasSelector 选择roas 根据campain
//func roasSelector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if campaign, ok := params["campaign"].(string); ok && campaign != "" {
//		if adConfigAny, ok := adConfigs[AdsKeyRoas]; ok {
//			adConfig := AdConfig(adConfigAny.(map[string]any))
//			if strings.Contains(campaign, "roas") {
//				return adConfig, true
//			}
//		}
//	}
//	return AdConfig{}, false
//}
//
//// roasLimitSelector 选择roas_limit
//func roasLimitSelector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfigAny, ok := adConfigs[AdsKeyRoasLimit]; ok {
//		adConfig := AdConfig(adConfigAny.(map[string]any))
//		if combination, ok := adConfig["combination"].([]string); ok {
//			countryCode := params[ParamNameServerCountryCode].(string)
//			if adConfig.checkCombination(combination, params) && adConfig.checkCountryCode("country_list", countryCode) {
//				return adConfig, true
//			}
//		}
//	}
//	return AdConfig{}, false
//}
//
//// roasLimit2Selector 选择roas_limit2
//func roasLimit2Selector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfigAny, ok := adConfigs[AdsKeyRoasLimit2]; ok {
//		adConfig := AdConfig(adConfigAny.(map[string]any))
//		if combination, ok := adConfig["combination"].([]string); ok {
//			countryCode := params[ParamNameServerCountryCode].(string)
//			if adConfig.checkCombination(combination, params) && adConfig.checkCountryCode("country_list", countryCode) {
//				return adConfig, true
//			}
//		}
//	}
//	return AdConfig{}, false
//}
//
//// countryCodeSelector countryCode选择
//func countryCodeSelector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfigAny, ok := adConfigs[AdsKeyCountryCode]; ok {
//		adConfig := AdConfig(adConfigAny.(map[string]any))
//		countryCode := params[ParamNameServerCountryCode].(string)
//		if adConfig.checkCountryCode("list", countryCode) {
//			return adConfig, true
//		}
//	}
//	return AdConfig{}, false
//}
//
//// countryCode2Selector countryCode选择
//func countryCode2Selector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfigAny, ok := adConfigs[AdsKeyCountryCode2]; ok {
//		adConfig := AdConfig(adConfigAny.(map[string]any))
//		countryCode := params[ParamNameServerCountryCode].(string)
//		if adConfig.checkCountryCode("list", countryCode) {
//			return adConfig, true
//		}
//	}
//	return AdConfig{}, false
//}
//
//// countryCode3Selector countryCode选择
//func countryCode3Selector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfigAny, ok := adConfigs[AdsKeyCountryCode3]; ok {
//		adConfig := AdConfig(adConfigAny.(map[string]any))
//		countryCode := params[ParamNameServerCountryCode].(string)
//		if adConfig.checkCountryCode("list", countryCode) {
//			return adConfig, true
//		}
//	}
//	return AdConfig{}, false
//}
//
//// countryCode4Selector countryCode选择
//func countryCode4Selector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfigAny, ok := adConfigs[AdsKeyCountryCode4]; ok {
//		adConfig := AdConfig(adConfigAny.(map[string]any))
//		countryCode := params[ParamNameServerCountryCode].(string)
//		if adConfig.checkCountryCode("list", countryCode) {
//			return adConfig, true
//		}
//	}
//	return AdConfig{}, false
//}
//
//func defaultSelector(adConfigs AdConfigs, params map[string]any) (AdConfig, bool) {
//	if adConfig, ok := adConfigs[AdsKeyDefaul]; ok {
//		return adConfig.(map[string]any), true
//	}
//	return AdConfig{}, false
//}
