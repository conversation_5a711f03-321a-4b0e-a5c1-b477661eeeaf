package adconfigs

import (
	"encoding/json"
	"go.uber.org/zap"
	"reflect"
)

// AdConfig 格式化liuliang配置中config中的单个节点
type AdConfig struct {
	Keys    []string       `json:"keys"`
	Filters []Filter       `json:"filters"`
	AdInfo  map[string]any `json:"ad_info"`
}

// evaluateFilters 计算Filters是否符合条件
func (ads AdConfig) evaluateFilters(attributes map[string]any) bool {
	if ads.Filters == nil || len(ads.Filters) == 0 {
		return true
	}
	if reflect.ValueOf(attributes).Kind() != reflect.Map {
		return false
	}
	result := ads.Filters[0].EvaluateFilter(attributes)
	for i := 0; i < len(ads.Filters)-1; i++ {
		logicFn := LogicFunc(ads.Filters[i].LogicOperator)
		if logicFn == nil {
			return false
		}
		result = logicFn(result, ads.Filters[i+1].EvaluateFilter(attributes))
	}
	return result
}

// GetAdInfo 通过条件判断获取AdInfo
func (ads AdConfig) GetAdInfo(params map[string]any) map[string]any {
	if len(ads.Keys) == 0 {
		return ads.AdInfo
	}
	sugared.Infof("keys: %v", ads.Keys)
	attributes := make(map[string]any)
	for _, key := range ads.Keys {
		attributes[key] = params[key]
	}
	sugared.Infof("attributes: %v", attributes)
	if ads.evaluateFilters(attributes) {
		return ads.AdInfo
	}
	return nil
}

// AdConfigs 格式化liuiang配置中的config字段的json数据
type AdConfigs struct {
	ABTester        string
	Switch          map[string]any
	FilterAdConfigs map[string]AdConfig
}

func NewAdConfigsWithAdInfo(adInfo map[string]any) AdConfigs {
	adConfig := AdConfig{
		Keys:    make([]string, 0),
		Filters: make([]Filter, 0),
		AdInfo:  adInfo,
	}
	return AdConfigs{
		ABTester:        "",
		Switch:          make(map[string]any),
		FilterAdConfigs: map[string]AdConfig{"default": adConfig},
	}
}

// GetAdInfo 根据条件和selectorOrder，判断符合条件的AdInfo
// selectorOrder 取自BundleConfig中的AdsConfigFilters，这里指定过滤参数的顺序
func (adConfigs AdConfigs) GetAdInfo(params map[string]any, selectOrder []string) map[string]any {
	for _, selectKey := range selectOrder {
		if adConfig, ok := adConfigs.FilterAdConfigs[selectKey]; ok {
			if adInfo := adConfig.GetAdInfo(params); adInfo != nil {
				return CloneMap(adInfo)
			}
		}
	}
	return nil
}

// CloneMap 克隆一个新的map
func CloneMap(original map[string]any) map[string]any {
	// 创建一个新的 map
	clone := make(map[string]any)

	// 迭代原始 map 并复制每一个键值对
	for key, value := range original {
		clone[key] = value
	}

	return clone
}

func (adConfigs AdConfigs) CheckAbTester() bool {
	if adConfigs.ABTester == "1" {
		return true
	}
	return false
}

var sugared *zap.SugaredLogger

func SetSugaredLogger(logger *zap.SugaredLogger) {
	sugared = logger
}

const (
	ABTesterKeyName = "huoshan" // AdConfigs 中判断是否要参与火山ABTester的键
	SwitchKeyName   = "switch"  // AdConfigs 中switch配置键名
	DidiKeyName     = "didi"    // AdConfigs 中滴滴分流标识
)

func parseAdConfig(config map[string]any) AdConfig {
	adConfig := AdConfig{
		Keys:    make([]string, 0),
		Filters: make([]Filter, 0),
		AdInfo:  make(map[string]any),
	}
	//sugared.Infof("config: %+v", config)
	if combination, ok := config["combination"].([]any); ok {
		//sugared.Infof("combination: %v", combination)
		comCondtions := make([]Condition, 0)
		if len(combination) == 3 {
			//comConditions := make([]adconfigs.Condition, 2)
			logicOperator := LogicOperatorType(combination[2].(string))
			//sugared.Infof("Logic Operator: %v", logicOperator)
			for i := 0; i < 2; i++ {
				key := combination[i].(string)
				//sugared.Infof("Index %d, key: %v", i, key)
				if value, ok := config[key]; ok {
					if valueSlice, ok := value.([]any); ok {
						condition := Condition{
							Key:           key,
							Value:         valueSlice,
							Type:          STRING,
							Op:            "in",
							LogicOperator: logicOperator,
						}
						adConfig.Keys = append(adConfig.Keys, key)
						comCondtions = append(comCondtions, condition)
						delete(config, key)
					}
				}
			}
		} else if len(combination) == 1 {
			key := combination[0].(string)
			if value, ok := config[key]; ok {
				if valueSlice, ok := value.([]any); ok {
					condition := Condition{
						Key:           key,
						Value:         valueSlice,
						Type:          STRING,
						Op:            "in",
						LogicOperator: AND,
					}
					adConfig.Keys = append(adConfig.Keys, key)
					comCondtions = append(comCondtions, condition)
					delete(config, key)
				}
			}
		}
		filter := Filter{
			Conditions:    comCondtions,
			LogicOperator: AND,
		}
		adConfig.Filters = append(adConfig.Filters, filter)
		delete(config, "combination")

		if countryList, ok := config["country_list"]; ok && countryList != nil {
			key := "server_country_code"
			adConfig.Keys = append(adConfig.Keys, key)
			filter := Filter{
				Conditions: []Condition{
					{
						Key:           key,
						Value:         countryList,
						Type:          STRING,
						Op:            "in",
						LogicOperator: AND,
					},
				},
			}
			adConfig.Filters = append(adConfig.Filters, filter)
			delete(config, "country_list")
		}
		for k, v := range config {
			adConfig.AdInfo[k] = v
		}
		//sugared.Infof("AdConfig: %+v", adConfig)
		return adConfig
	}

	if countryList, ok := config["list"]; ok && countryList != nil {
		key := "server_country_code"
		adConfig.Keys = append(adConfig.Keys, key)
		filter := Filter{
			Conditions: []Condition{
				{
					Key:           key,
					Value:         countryList,
					Type:          STRING,
					Op:            "in",
					LogicOperator: AND,
				},
			},
		}
		adConfig.Filters = append(adConfig.Filters, filter)
		delete(config, "list")
		for k, v := range config {
			adConfig.AdInfo[k] = v
		}
		return adConfig
	}
	if adInfo, ok := config["ad_info"].(map[string]any); ok && adInfo != nil {
		for key, value := range adInfo {
			adConfig.AdInfo[key] = value
		}
		delete(config, "ad_info")
		return adConfig
	}
	for k, v := range config {
		adConfig.AdInfo[k] = v
	}
	return adConfig
}

func ParseAdConfigs(configJson string) (AdConfigs, error) {
	adConfigs := AdConfigs{
		Switch:          make(map[string]any),
		FilterAdConfigs: make(map[string]AdConfig),
	}
	configs := make(map[string]any)
	err := json.Unmarshal([]byte(configJson), &configs)
	if err != nil {
		sugared.Errorf("json unmarshal(%s) fail: %s", configJson, err.Error())
		return adConfigs, err
	}

	if huoshan, ok := configs[ABTesterKeyName].(string); ok {
		adConfigs.ABTester = huoshan
		delete(configs, ABTesterKeyName)
	}
	if switchConfig, ok := configs[SwitchKeyName].(map[string]any); ok {
		adConfigs.Switch = switchConfig
		delete(configs, SwitchKeyName)
	}
	for k, v := range configs {
		if vv, ok := v.(map[string]any); ok {
			adConfigs.FilterAdConfigs[k] = parseAdConfig(vv)
		}
	}
	return adConfigs, nil
}
