package adconfigs

import (
	"encoding/json"
	"testing"
)

var releaseJson = `
 {
	"filters": [
            {
                "conditions": [
                    {
                        "key": "media_source",
                        "value": [
                            "digitalturbine_int"
                        ],
                        "type": "string",
                        "op": "in",
                        "logic_operator": "&&"
                    },
                    {
                        "key": "campaign",
                        "value": "ROAS",
                        "type": "string",
                        "op": "contain",
                        "logic_operator": "&&"
                    }
                ],
                "logic_operator": "&&"
            },
            {
                "conditions": [
                    {
                        "key": "country",
                        "value": [
                            "DE",
                            "FR"
                        ],
                        "type": "string",
                        "op": "in",
                        "logic_operator": "&&"
                    }
                ],
                "logic_operator": "&&"
            }
        ],
        "ad_info": {
            "adwaynum": "3993",
            "adplatform": "1",
            "abtest": "",
            "banner": {
                "adunit": {
                    "has_fa": "eac83cba1cd3cf70",
                    "no_fa": "eac83cba1cd3cf70"
                },
                "banner_interval": [
                    0,
                    0.1,
                    0.2,
                    0.3,
                    0.4,
                    0.5,
                    0.6,
                    0.7,
                    0.8,
                    0.9,
                    1.0,
                    1.1,
                    1.2,
                    1.3,
                    1.4,
                    1.5,
                    1.6,
                    1.7,
                    1.8,
                    1.9,
                    2.0,
                    2.5,
                    3,
                    3.5,
                    4,
                    4.5,
                    5
                ],
                "banner_events": [
                    "s_ad_banner_revenue_3520"
                ]
            },
            "insert": {
                "adunit": {
                    "has_fa": "29bdc0edba6547ce",
                    "no_fa": "29bdc0edba6547ce"
                },
                "adunit2": {
                    "has_fa": "8515539cd9d5831d",
                    "no_fa": "8515539cd9d5831d"
                },
                "preload": "close"
            },
            "reward": {
                "adunit": {
                    "has_fa": "5b9d019e6f3e50fe",
                    "no_fa": "5b9d019e6f3e50fe"
                },
                "adunit2": {
                    "has_fa": "94dd774852a11d86",
                    "no_fa": "94dd774852a11d86"
                },
                "preload": "close"
            },
            "is_first": "0",
            "ab_live": "1"
        }
}
`

func TestRelease(t *testing.T) {
	adConfigs := new(AdConfig)
	err := json.Unmarshal([]byte(releaseJson), adConfigs)
	if err != nil {
		t.Error(err)
	}
	t.Logf("Realse: %v", adConfigs)
	params := make(map[string]any)
	params["media_source"] = "digitalturbine_int"
	params["campaign"] = "Juggle-WW-BC-ROAS0"
	params["country"] = "DE"
	if adInfo := adConfigs.GetAdInfo(params); adInfo != nil {
		t.Logf("AdInfo: %v", adInfo)
	} else {
		t.Log("Failure")
	}
}

func BenchmarkRelease(b *testing.B) {
	adConfigs := new(AdConfig)
	err := json.Unmarshal([]byte(releaseJson), adConfigs)
	if err != nil {
		b.Error(err)
	}
	//b.Logf("Realse: %v", release)
	params := make(map[string]any)
	params["media_source"] = "digitalturbine_int"
	params["campaign"] = "Juggle-WW-BC-ROAS0"
	params["country"] = "DE"
	for i := 0; i < b.N; i++ {
		adConfigs.evaluateFilters(params)
	}
}
