package models

import (
	"github.com/Masterminds/semver/v3"
	"testing"
)

func TestVersion(t *testing.T) {
	appVersion, err := semver.NewVersion("5.2.5")
	if err != nil {
		t.<PERSON><PERSON>rf("appVersion:5.2.5, err: %v", err)
	}
	v1, err := semver.NewVersion("5.2.5")
	if err != nil {
		t.<PERSON><PERSON>rf("v1: 5.2.5, err: %v", err)
	}
	v2, err := semver.NewVersion("5.2.0")
	if err != nil {
		t.Errorf("v2: 5.2.0, err:: %v", err)
	}
	if appVersion.GreaterThan(v1) || appVersion.Equal(v1) {
		t.Log("appVersion(5.2.5)>=v1(5.2.5)")
	}
	if appVersion.GreaterThan(v2) || appVersion.Equal(v2) {
		t.Log("appVersion(5.2.5)>=v2(5.2.0)")
	}
}
