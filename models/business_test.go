package models

import (
	"context"
	_ "github.com/go-sql-driver/mysql"
	"hungrystudio.com/core/log"
	"hungrystudio.com/core/uuid"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/models/business"
	"hungrystudio.com/datatester/models/distributes"
	"os"
	"testing"
)

var bsns *Business

func init() {
	appConfig := common.NewDataTesterServerConfig("../configs/app.yaml")
	_ = os.Setenv("DB_PASSWORD", "BUC0TG0aWCVaLGaM")

	logger := log.NewLogger(appConfig.Logger)
	sugared := logger.Sugar()

	xormEngine, err := common.NewXormEngine(appConfig.DBConfigs)
	if err != nil {
		sugared.Fatal(err)
	}

	business.SetXormEngine(xormEngine)
	business.SetSugaredLogger(sugared)

	distributes.SetSugaredLogger(sugared)

	bsns = NewBusiness(context.Background())
}

func BenchmarkBusiness_Init(b *testing.B) {
	req := business.Request{
		BundleId:    "com.block.juggle",
		ApiVersion:  "v58",
		CountryCode: "CN",
		DistinctId:  uuid.NewUUID(),
	}
	//reqJson, _ := json.Marshal(req)
	for i := 0; i < b.N; i++ {
		_, err := bsns.Init(req)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func TestBusiness_Init(t *testing.T) {
	req := business.Request{
		BundleId:    "com.block.juggle",
		ApiVersion:  "v58",
		CountryCode: "CN",
		DistinctId:  uuid.NewUUID(),
	}
	for i := 0; i < 100; i++ {
		_, err := bsns.Init(req)
		if err != nil {
			t.Fatal(err)
		}
	}
	//t.Log(result)
}
