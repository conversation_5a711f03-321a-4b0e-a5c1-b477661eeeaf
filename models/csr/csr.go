package csr

import (
	"hungrystudio.com/datatester/models/distributes"
	"strings"
)

var AdfConf = map[string]string{
	"max":        "1",
	"admob":      "2",
	"ironsource": "3",
	"hs":         "4",
}

const (
	KindList  string = "list"
	KindRange string = "range"
	KindGroup string = "group"
)

const (
	IndentityKeyEcpm    string = "ecpm"
	IndentityKeyGameNum string = "gamenum"
	IndentityKeyAdNum   string = "adnum"
	IndentityKeyADay    string = "aday" // 活跃天数
)

// IsExistence 判断是否存在
func IsExistence(plan string, schemes []distributes.Scheme) bool {
	// 判断 plan 是否在 schemes 中
	found := false
	for _, scheme := range schemes {
		if scheme.Name == plan {
			found = true
			break
		}
	}
	return found
}

// FindString 判断指定字符串是否在数组里面
func FindString(target string, arr []interface{}) int {
	for index, elem := range arr {
		// 使用类型断言判断元素是否为字符串类型
		if str, ok := elem.(string); ok {
			str = strings.ToLower(str)
			if str == target {
				return index + 1
			}
		}
	}
	return 1
}

// FindFloat 判断指定数值是否在数组里面
func FindFloat(target float64, arr []interface{}) int {
	for index, elem := range arr {
		// 使用类型断言判断元素是否为字符串类型
		if str, ok := elem.(float64); ok {
			if str == target {
				return index + 1
			}
		}
	}
	return 1
}

// LastLessThan 函数用于查找切片中最后一个小于某个数值的位置
func LastLessThan(target float64, arr []interface{}) int {

	var lastIndex = 0
	if target == 0 {
		return lastIndex + 1
	}

	for i, elem := range arr {
		// 使用类型断言判断元素是否为 float64 或 int 类型
		switch val := elem.(type) {
		case float64:
			if val <= target {
				lastIndex = i
			}
		case int:
			if float64(val) <= target {
				lastIndex = i
			}
		}
	}

	return lastIndex + 2
}

// FindGroup 判断指定字符串是否在某个数组分组里面
func FindGroup(target string, arr []interface{}) int {

	for index, elemArr := range arr {
		if subArr, ok := elemArr.([]interface{}); ok {
			for _, elem := range subArr {
				// 使用类型断言判断元素是否为字符串类型
				if str, ok := elem.(string); ok {
					str = strings.ToLower(str)
					if str == target {
						return index + 1
					}
				}
			}
		}
	}
	return 1
}

// FindFloatGroup 判断指定数值是否在某个数组分组里面
func FindFloatGroup(target float64, arr []interface{}) int {

	for index, elemArr := range arr {
		if subArr, ok := elemArr.([]interface{}); ok {
			for _, elem := range subArr {
				// 使用类型断言判断元素是否为字符串类型
				if str, ok := elem.(float64); ok {
					if str == target {
						return index + 1
					}
				}
			}
		}
	}
	return 1
}

func ParseIndentity(kind string, value any, values []any) int {
	switch kind {
	case KindList:
		if targetValue, ok := value.(string); ok { //字符串分组
			targetValue = strings.ToLower(targetValue)
			return FindString(targetValue, values)
		} else if targetFloat, ok := value.(float64); ok { //数值分组
			return FindFloat(targetFloat, values)
		}
	case KindRange:
		if targetNum, ok := value.(float64); ok {
			return LastLessThan(targetNum, values)
		}
	case KindGroup:
		if groupValue, ok := value.(string); ok { //字符串分组
			groupValue = strings.ToLower(groupValue)
			return FindGroup(groupValue, values)
		} else if groupFloat, ok := value.(float64); ok { //数值分组
			return FindFloatGroup(groupFloat, values)
		}
	}
	return 1
}
