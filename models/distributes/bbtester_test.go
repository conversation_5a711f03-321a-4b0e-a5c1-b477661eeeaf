package distributes

//import (
//	"math/rand"
//	"testing"
//)
//
//func allocate(schemes []BBScheme, index int) {
//	schemes[index].Count++
//}
//
//func TestValidateDistribution(t *testing.T) {
//	schemes := []BBScheme{
//		{"A", 5, 0},
//		{"B", 3, 0},
//		{"C", 2, 0},
//	}
//	totalAllocations := 0
//	for {
//		index := rand.Intn(len(schemes))
//		schemes[index].Count++
//
//		totalAllocations++
//		if validateDistribution(schemes, totalAllocations) {
//			t.Log("分配符合预期，分配次数:", totalAllocations)
//			break
//		} else {
//			schemes[index].Count--
//			totalAllocations--
//		}
//		if totalAllocations > 1000 {
//			break
//		}
//	}
//	t.Log("最终分配结果：")
//	for _, scheme := range schemes {
//		t.Logf("%s: 分配次数：%d, 目标权重:%d\n", scheme.Name, scheme.Count, scheme.Weight)
//	}
//}
