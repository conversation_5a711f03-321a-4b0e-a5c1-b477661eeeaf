package distributes

import (
	"math/rand"
	"sort"
	"time"
)

type SudokuRoundRobin struct {
	*RoundRobin
	sequence       bool
	changeSequence bool
}

func NewSudokuRoundRobin(id string, schemes []Scheme) *SudokuRoundRobin {
	if len(schemes) == 0 {
		sugared.Errorf("schemes list cannot be empty")
		return nil
	}
	//取消随机，默认取第一个
	currentIndex := 0
	//currentIndex := rand.Intn(len(schemes))
	sequence := true
	changeSequence := false
	id = "sdk_rr_" + id
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
		sequence = intToBool(status["sequence"])
		changeSequence = intToBool(status["changeSequence"])
	}
	rr := NewRoundRobin(id, schemes)
	rr.currentIndex = currentIndex
	return &SudokuRoundRobin{
		RoundRobin:     rr,
		sequence:       sequence,
		changeSequence: changeSequence,
	}
}

func (sdk *SudokuRoundRobin) ResetSchemes(schemes []Scheme) {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()

	if sdk.CheckReset(schemes, false) {
		schemes = RandomOrder(schemes)
		sdk.schemes = schemes
		sdk.currentIndex = 0
		sdk.lastDistributeTime = time.Now()
		sugared.Infof("SyhSchemes 数量方法变化: %v", schemes)
		sdk.checkSchemes()
		return
	}

	//reset := false
	//if len(sdk.schemes) != len(schemes) {
	//	reset = true
	//} else {
	//	sourceSchemes := slices.Clone(sdk.schemes)
	//	// sugared.Infof("no sort source schemes: %v,reset schemes %v", sourceSchemes, schemes)
	//	sort.Slice(sourceSchemes, func(i, j int) bool {
	//		return sourceSchemes[i].Name < sourceSchemes[j].Name
	//	})
	//
	//	sort.Slice(schemes, func(i, j int) bool {
	//		return schemes[i].Name < schemes[j].Name
	//	})
	//	// sugared.Infof("source schemes: %v,reset schemes %v", sourceSchemes, schemes)
	//	for i := range sourceSchemes {
	//		if sourceSchemes[i].Name != schemes[i].Name {
	//			reset = true
	//			break
	//		}
	//	}
	//}
	////数量不一致
	//if reset {
	//	schemes = RandomOrder(schemes)
	//	sdk.schemes = schemes
	//	sdk.currentIndex = 0
	//	sdk.lastDistributeTime = time.Now()
	//	sugared.Infof("SyhSchemes 数量方法变化: %v", schemes)
	//	return
	//}

	// 不在对长度一致的方案号进行计算
	//提取方案号
	//sdkNames := ExtractNames(sdk.schemes)
	//rsNames := ExtractNames(schemes)
	//
	////方案发生改变
	//if !slices.Equal(rsNames, sdkNames) {
	//	schemes = RandomOrder(schemes)
	//	sdk.schemes = schemes
	//	sdk.currentIndex = 0
	//	sdk.lastDistributeTime = time.Now()
	//	sugared.Infof("SyhSchemes 方案元素发生变化: %v", schemes)
	//}

	// return
}

func (sdk *SudokuRoundRobin) checkSchemes() {
	testMap := make(map[string]bool)
	for _, scheme := range sdk.schemes {
		if testMap[scheme.Name] {
			sugared.Errorf("checkSchemes err csrid:%s, curIdx:%d, scheme:%v, schemes:%v", sdk.id, sdk.currentIndex, scheme, sdk.schemes)
			break
		}
		testMap[scheme.Name] = true
	}
}

func (sdk *SudokuRoundRobin) Next() Scheme {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()
	if len(sdk.schemes) == 1 {
		return sdk.schemes[0]
	}

	if sdk.currentIndex == 0 {
		sdk.schemes = RandomOrder(sdk.schemes)
		sugared.Infof("初始分配: key:%s, Schemes: %v", sdk.id, sdk.schemes)
		sdk.checkSchemes()
	}
	scheme := sdk.schemes[sdk.currentIndex]
	sugared.Infof("csrnext csrid:%s, curIdx:%d, scheme:%s", sdk.id, sdk.currentIndex, scheme.Name)

	//一轮分配完毕，打乱顺序，重新分配
	if sdk.currentIndex == len(sdk.schemes)-1 {
		sdk.currentIndex = 0
	} else {
		sdk.currentIndex = (sdk.currentIndex + 1) % (len(sdk.schemes))
	}

	sdk.lastDistributeTime = time.Now()
	return scheme
	//scheme := sdk.schemes[sdk.currentIndex]
	//if sdk.changeSequence {
	//	sdk.changeSequence = false
	//	return scheme
	//}
	//if sdk.sequence {
	//	sdk.currentIndex = (sdk.currentIndex + 1) % (len(sdk.schemes))
	//	if sdk.currentIndex == len(sdk.schemes)-1 {
	//		sdk.sequence = false
	//		sdk.changeSequence = true
	//	}
	//} else {
	//	sdk.currentIndex = (sdk.currentIndex - 1) % (len(sdk.schemes))
	//	if sdk.currentIndex == 0 {
	//		sdk.sequence = true
	//		sdk.changeSequence = true
	//	}
	//}
	//sdk.lastDistributeTime = time.Now()
	//return scheme
}

func (sdk *SudokuRoundRobin) Close() {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()
	status := map[string]int{
		"currentIndex":   sdk.currentIndex,
		"sequence":       boolToInt(sdk.sequence),
		"changeSequence": boolToInt(sdk.changeSequence),
	}
	saveAlgoStatus(sdk.id, status)
}

// boolToInt 布尔值 true = 1， false = 0
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// intToBool 1 = true 否则为false
func intToBool(i int) bool {
	// if i == 1 {
	// 	return true
	// }
	// return false
	return i == 1
}

// RandomOrder 随机打乱顺序
func RandomOrder(schemes []Scheme) []Scheme {

	// 使用时间作为随机种子
	//rand.Seed(time.Now().UnixNano())
	//
	//// Fisher-Yates 洗牌算法打乱顺序
	//for i := len(schemes) - 1; i > 0; i-- {
	//	j := rand.Intn(i + 1)
	//	schemes[i], schemes[j] = schemes[j], schemes[i]
	//}
	//
	rand.Shuffle(len(schemes), func(i, j int) {
		schemes[j], schemes[i] = schemes[i], schemes[j]
	})
	return schemes
}

// ExtractNames extracts the Name fields from a slice of Scheme
func ExtractNames(schemes []Scheme) []string {
	names := make([]string, len(schemes))
	for i, scheme := range schemes {
		names[i] = scheme.Name
	}
	sort.Strings(names)
	return names
}
