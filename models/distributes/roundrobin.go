package distributes

import (
	"sync"
	"time"
)

// RoundRobin 轮询算法
type RoundRobin struct {
	*Algo
	currentIndex int
	mux          sync.Mutex
}

// NewRoundRobin 新建轮询算法
func NewRoundRobin(id string, schemes []Scheme) *RoundRobin {
	if len(schemes) == 0 {
		sugared.Errorf("schemes list cannot be empty")
		return nil
	}
	id = "rr_" + id
	currentIndex := 0
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
	}
	return &RoundRobin{
		Algo:         NewAlgo(id, schemes),
		currentIndex: currentIndex,
	}
}

func (rr *RoundRobin) GetCreateTime() time.Time {
	return rr.createTime
}

// Next 获取下一个scheme
func (rr *RoundRobin) Next() Scheme {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	if len(rr.schemes) == 1 {
		return rr.schemes[0]
	}
	scheme := rr.schemes[rr.currentIndex]
	rr.currentIndex = (rr.currentIndex + 1) % (len(rr.schemes))
	rr.lastDistributeTime = time.Now()
	return scheme
}

func (rr *RoundRobin) Close() {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	status := map[string]int{"currentIndex": rr.currentIndex}
	saveAlgoStatus(rr.id, status)
}

func (rr *RoundRobin) ResetSchemes(schemes []Scheme) {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	if len(rr.schemes) != len(schemes) {
		rr.schemes = schemes
		if rr.currentIndex >= len(schemes) {
			rr.currentIndex = 0
		}
	}
}
