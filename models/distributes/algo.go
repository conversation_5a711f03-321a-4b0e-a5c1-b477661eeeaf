package distributes

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"
)

const AlgoSaveDir = "data/algo"

type DistributeAlgo interface {
	GetCreateTime() time.Time
	GetLastDistributeTime() time.Time
	ResetSchemes(schemes []Scheme)
	Next() Scheme
	Close()
}

type Algo struct {
	id                 string
	createTime         time.Time
	lastDistributeTime time.Time
	schemes            []Scheme
	mux                sync.Mutex
}

func NewAlgo(id string, schemes []Scheme) *Algo {
	return &Algo{
		id:                 id,
		createTime:         time.Now(),
		lastDistributeTime: time.Now(),
		schemes:            schemes,
	}
}

func (a *Algo) GetCreateTime() time.Time {
	//TODO implement me
	return a.createTime
}

func (a *Algo) GetLastDistributeTime() time.Time {
	//TODO implement me
	return a.lastDistributeTime
}

//	func (a *Algo) ResetScheme(schemes []Scheme) {
//		//TODO implement overwrite me
//		return
//	}
//

// CheckReset 检查是否要重置schemes列表
func (a *Algo) CheckReset(schemes []Scheme, checkWeight bool) bool {
	reset := false
	if len(schemes) != len(a.schemes) {
		reset = true
	} else {
		hashMap := make(map[string]int)
		for _, scheme := range schemes {
			hashMap[scheme.Name] = scheme.Weight
		}
		for _, scheme := range a.schemes {
			if v, ok := hashMap[scheme.Name]; ok {
				if checkWeight && v != scheme.Weight {
					reset = true
					break
				}
			} else {
				reset = true
				break
			}
		}
	}
	return reset
}

//
//func (a *Algo) Next() Scheme {
//	//TODO implement me
//
//	return Scheme{}
//}

//func (a *Algo) Close() {
//	//TODO implement me
//	panic("implement me")
//}

// saveAlgoStatus 保存算法状态
func saveAlgoStatus(id string, algoStatus map[string]int) {
	if _, err := os.Stat(AlgoSaveDir); err != nil {
		err = os.MkdirAll(AlgoSaveDir, 0755)
		if err != nil {
			sugared.Errorf("error creating algo save dir %s: %v", AlgoSaveDir, err)
			return
		}
	}
	filename := fmt.Sprintf("%s/%s.json", AlgoSaveDir, id)
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0755)
	if err != nil {
		sugared.Errorf("error opening algo save file %s: %v", filename, err)
		return
	}
	defer file.Close()
	encoder := json.NewEncoder(file)
	err = encoder.Encode(algoStatus)
	if err != nil {
		sugared.Errorf("error encoding algo save file %s: %v", filename, err)
	}
}

// loadAlgoStatus 从文件载入算法状态
func loadAlgoStatus(id string) (algoStatus map[string]int) {
	algoStatus = make(map[string]int)
	filename := fmt.Sprintf("%s/%s.json", AlgoSaveDir, id)
	file, err := os.Open(filename)
	if err != nil {
		//sugared.Errorf("error opening algo save file %s: %v", filename, err)
		return nil
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&algoStatus)
	if err != nil {
		sugared.Errorf("error decoding algo save file %s: %v", filename, err)
		return nil
	}
	_ = os.Remove(filename)
	return algoStatus
}
