package distributes

import (
	"context"
	"fmt"
	"sync"
	"time"
)

type Scheme struct {
	Name   string
	Weight int
}

const (
	DayDistributeProviderInstance       = "DayDistributeProvidate"
	SudoDistributeProviderRobinInstance = "SudoDistributeProvideRobin"
)

type DistributeFactory struct {
	mux       sync.Mutex
	providers map[string]Distribute
}

func NewDistributeFactory() *DistributeFactory {
	return &DistributeFactory{
		providers: make(map[string]Distribute),
	}
}

func (df *DistributeFactory) Register(name string, provider Distribute) {
	df.providers[name] = provider
}

func (df *DistributeFactory) GetDistributeProvider(name string) Distribute {
	if provider, ok := df.providers[name]; ok {
		return provider
	}
	return nil
}

type Distribute interface {
	Distribute(group string, disposeId string, schemes []Scheme) Scheme
	GenerateDistributeKey(group string, disposeId string) string
	NewAlgo(key string, schemes []Scheme) DistributeAlgo
	Clean(ctx context.Context)
	Close(ctx context.Context)
}

type DistributeProvider struct {
	algos         sync.Map
	cleanDuration time.Duration
}

func NewDistributeProvider(ctx context.Context, cleanDuration time.Duration) *DistributeProvider {
	dp := &DistributeProvider{
		algos:         sync.Map{},
		cleanDuration: cleanDuration * time.Hour,
	}
	go dp.Clean(ctx)
	return dp
}

//func (dp *DistributeProvider) Distribute(group string, disposeId string, schemes []Scheme) Scheme {
//	//TODO implement me
//	key := dp.GenerateDistributeKey(group, disposeId)
//	var algo DistributeAlgo
//	if a, ok := dp.algos.Load(key); ok {
//		algo = a.(DistributeAlgo)
//	} else {
//		algo = dp.NewAlog(key, schemes)
//		dp.algos.Store(key, algo)
//	}
//	// 重置schemes，每个不同算法需要重写此方法，以适配
//	algo.ResetSchemes(schemes)
//	return algo.Next()
//}

//func (dp *DistributeProvider) NewAlog(key string, schemes []Scheme) DistributeAlgo {
//	//TODO overwrite me
//	return NewAlgo(key, schemes)
//}

//func (dp *DistributeProvider) GenerateDistributeKey(group string, disposeId string) string {
//	//TODO implement me
//	return fmt.Sprintf("%s-%s", group, disposeId)
//}

func (dp *DistributeProvider) Clean(ctx context.Context) {
	//TODO implement me
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			dp.algos.Range(func(k, v interface{}) bool {
				algo := v.(DistributeAlgo)
				if time.Now().Sub(algo.GetLastDistributeTime()) > dp.cleanDuration {
					algo.Close()
					dp.algos.Delete(k)
				}
				return true
			})
		}

	}
}

func (dp *DistributeProvider) Close(ctx context.Context) {
	<-ctx.Done()
	dp.algos.Range(func(k, v interface{}) bool {
		algo := v.(DistributeAlgo)
		algo.Close()
		return true
	})
}

type DayDistributeProvider struct {
	*DistributeProvider
}

func NewDayDistributeProvider(ctx context.Context, cleanDuration time.Duration) *DayDistributeProvider {
	dp := &DayDistributeProvider{
		DistributeProvider: NewDistributeProvider(ctx, cleanDuration),
	}
	return dp
}

func (ddp *DayDistributeProvider) GenerateDistributeKey(group string, disposeId string) string {
	day := time.Now().Format("20060102")
	return fmt.Sprintf("day_distribute_%s_%s_%s", day, group, disposeId)
}

func (ddp *DayDistributeProvider) NewAlgo(key string, schemes []Scheme) DistributeAlgo {
	return NewWeightRoundRobin(key, schemes)
}

func (ddp *DayDistributeProvider) Distribute(group string, disposeId string, schemes []Scheme) Scheme {
	//TODO implement me
	key := ddp.GenerateDistributeKey(group, disposeId)
	var algo DistributeAlgo
	if a, ok := ddp.algos.Load(key); ok {
		algo = a.(DistributeAlgo)
	} else {
		algo = ddp.NewAlgo(key, schemes)
		ddp.algos.Store(key, algo)
	}
	// 重置schemes，每个不同算法需要重写此方法，以适配
	algo.ResetSchemes(schemes)
	return algo.Next()
}

type SudoRoBinDistributeProvider struct {
	*DistributeProvider
}

func NewSudoRoBinDistributeProvider(ctx context.Context, cleanDuration time.Duration) *SudoRoBinDistributeProvider {
	dp := &SudoRoBinDistributeProvider{
		DistributeProvider: NewDistributeProvider(ctx, cleanDuration),
	}
	return dp
}

func (sudoDp *SudoRoBinDistributeProvider) GenerateDistributeKey(group string, disposeId string) string {
	return fmt.Sprintf("csr_distribute_%s_%s", group, disposeId)
}

func (sudoDp *SudoRoBinDistributeProvider) NewAlgo(key string, schemes []Scheme) DistributeAlgo {
	return NewSudokuRoundRobin(key, schemes)
}

func (sudoDp *SudoRoBinDistributeProvider) Distribute(group string, disposeId string, schemes []Scheme) Scheme {
	//TODO implement me
	key := sudoDp.GenerateDistributeKey(group, disposeId)
	var algo DistributeAlgo
	if a, ok := sudoDp.algos.Load(key); ok {
		algo = a.(DistributeAlgo)
	} else {
		algo = sudoDp.NewAlgo(key, schemes)
		sudoDp.algos.Store(key, algo)
	}
	// 重置schemes，每个不同算法需要重写此方法，以适配
	algo.ResetSchemes(schemes)
	return algo.Next()
}
