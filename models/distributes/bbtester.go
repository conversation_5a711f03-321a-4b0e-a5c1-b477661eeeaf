package distributes

type BBScheme struct {
	Name     string
	Weight   int
	Count    int
	MaxCount int
	Ratio    float64
}

type BlockBlastTester struct {
	schemes    map[string]BBScheme
	totalCount int64
}

const DefaultTotalCount = 10000000000

func NewBlockBlastTester(schemes []BBScheme, totalCount int64) *BlockBlastTester {
	ss := make(map[string]BBScheme)
	totalWeight := 0
	for _, scheme := range schemes {
		if _, ok := ss[scheme.Name]; !ok {
			totalWeight += scheme.Weight
			//tc += scheme.Count
			ss[scheme.Name] = scheme
		}
	}
	for _, scheme := range ss {
		scheme.Ratio = float64(scheme.Weight) / float64(totalWeight)
		ss[scheme.Name] = scheme
	}
	if totalCount == 0 {
		totalCount = DefaultTotalCount
	}
	return &BlockBlastTester{
		schemes:    ss,
		totalCount: totalCount,
	}
}

func (bbt *BlockBlastTester) ValidateDistribute(schemeName string) bool {
	if scheme, ok := bbt.schemes[schemeName]; ok {
		scheme.Count++
		if float64(scheme.Count)/float64(bbt.totalCount) < scheme.Ratio {
			if scheme.MaxCount != 0 {
				if scheme.Count > scheme.MaxCount {
					scheme.Count--
					bbt.schemes[schemeName] = scheme
					return false
				}
			}
			bbt.schemes[schemeName] = scheme
			return true
		} else {
			scheme.Count--
			bbt.schemes[schemeName] = scheme
			return false
		}
	}
	return false
}

func validateDistribution(schemes []BBScheme, totalAllocations int) bool {
	for _, scheme := range schemes {
		actualRatio := float64(scheme.Count) / float64(totalAllocations)
		expectedRatio := float64(scheme.Weight) / float64(getTotalWeight(schemes))
		if actualRatio > expectedRatio*1.05 || actualRatio < expectedRatio*0.95 {
			return false
		}
	}
	return true
}

func getTotalWeight(schemes []BBScheme) int {
	totalWeight := 0
	for _, scheme := range schemes {
		totalWeight += scheme.Weight
	}
	return totalWeight
}
