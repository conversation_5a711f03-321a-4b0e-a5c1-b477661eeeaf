package distributes

import (
	"fmt"
	"os"
	"slices"
	"testing"

	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
	"hungrystudio.com/core/log"
)

var loggerConfigContent = `
- level: -1
  lj_logger:
    filename: "logs/datatester.log"
    maxsize: 500
    maxage: 7
    maxbackups: 10
    localtime: true
    compress: true
- level: 2
  lj_logger:
    filename: "logs/datatester-errors.log"
    maxsize: 500
    maxage: 14
    maxbackups: 20
    localtime: true
    compress: true
`

func init() {
	loggerConfig := make([]log.ZapLoggerConfig, 0)

	err := yaml.Unmarshal([]byte(loggerConfigContent), &loggerConfig)
	if err != nil {
		fmt.Println(err)
	}
	logger := log.NewLogger(loggerConfig)
	sugared = logger.Sugar()
	dir, _ := os.Getwd()
	sugared.Infof("Dir: %s", dir)
	SetSugaredLogger(sugared)
}

func TestNewSudokuRoundRobin(t *testing.T) {
	schemes := []Scheme{
		{Name: "A", Weight: 1},
		{Name: "B", Weight: 1},
		{Name: "C", Weight: 1},
	}

	srr := NewSudokuRoundRobin("test", schemes)
	assert.NotNil(t, srr)
	assert.Equal(t, 3, len(srr.schemes))
	assert.Equal(t, 0, srr.currentIndex)
	assert.True(t, srr.sequence)
	assert.False(t, srr.changeSequence)
}

func TestSudokuRoundRobin_Next(t *testing.T) {
	schemes := []Scheme{
		{Name: "A", Weight: 1},
		{Name: "B", Weight: 1},
		{Name: "C", Weight: 1},
	}

	srr := NewSudokuRoundRobin("test", schemes)

	// Test first round
	assert.Equal(t, "A", srr.Next().Name)
	assert.Equal(t, "B", srr.Next().Name)
	assert.Equal(t, "C", srr.Next().Name)

	// Test second round (should be randomized)
	secondRound := []string{
		srr.Next().Name,
		srr.Next().Name,
		srr.Next().Name,
	}

	assert.NotEqual(t, []string{"A", "B", "C"}, secondRound)
	assert.ElementsMatch(t, []string{"A", "B", "C"}, secondRound)
}

func TestSudokuRoundRobin_ResetSchemes(t *testing.T) {
	originalSchemes := []Scheme{
		{Name: "A", Weight: 1},
		{Name: "B", Weight: 1},
		{Name: "C", Weight: 1},
	}

	srr := NewSudokuRoundRobin("test", originalSchemes)

	newSchemes := []Scheme{
		{Name: "B", Weight: 1},
		{Name: "C", Weight: 1},
		{Name: "A", Weight: 1},
	}

	srr.ResetSchemes(newSchemes)

	assert.Equal(t, 3, len(srr.schemes))
	assert.Equal(t, 0, srr.currentIndex)

	// Verify that the new schemes are being used
	usedSchemes := make(map[string]bool)
	for i := 0; i < 3; i++ {
		scheme := srr.Next()
		usedSchemes[scheme.Name] = true
	}

	assert.True(t, usedSchemes["A"])
	assert.True(t, usedSchemes["B"])
	assert.True(t, usedSchemes["C"])
}

func BenchmarkSudokuRoundRobin_ResetSchemes(b *testing.B) {
	b.Run("ResetSchemes", func(b *testing.B) {
		originalSchemes := []Scheme{
			{Name: "1001", Weight: 1},
			{Name: "1002", Weight: 1},
			{Name: "1003", Weight: 1},
			{Name: "1004", Weight: 1},
			{Name: "1005", Weight: 1},
			{Name: "1006", Weight: 1},
			{Name: "1007", Weight: 1},
			{Name: "1008", Weight: 1},
			{Name: "1009", Weight: 1},
			{Name: "1010", Weight: 1},
			{Name: "1011", Weight: 1},
			{Name: "1012", Weight: 1},
			{Name: "1013", Weight: 1},
			{Name: "1014", Weight: 1},
			{Name: "1015", Weight: 1},
			{Name: "1016", Weight: 1},
			{Name: "1017", Weight: 1},
			{Name: "1018", Weight: 1},
			{Name: "1019", Weight: 1},
			{Name: "1020", Weight: 1},
			{Name: "1021", Weight: 1},
			{Name: "1022", Weight: 1},
			{Name: "1023", Weight: 1},
			{Name: "1024", Weight: 1},
			{Name: "1025", Weight: 1},
			{Name: "1026", Weight: 1},
			{Name: "1027", Weight: 1},
			{Name: "1028", Weight: 1},
			{Name: "1029", Weight: 1},
			{Name: "1030", Weight: 1},
			{Name: "1031", Weight: 1},
			{Name: "1032", Weight: 1},
			{Name: "1033", Weight: 1},
			{Name: "1034", Weight: 1},
			{Name: "1035", Weight: 1},
			{Name: "1036", Weight: 1},
			{Name: "1037", Weight: 1},
			{Name: "1038", Weight: 1},
			{Name: "1039", Weight: 1},
			{Name: "1040", Weight: 1},
			{Name: "1041", Weight: 1},
			{Name: "1042", Weight: 1},
			{Name: "1043", Weight: 1},
			{Name: "1044", Weight: 1},
			{Name: "1045", Weight: 1},
			{Name: "1046", Weight: 1},
			{Name: "1047", Weight: 1},
			{Name: "1048", Weight: 1},
			{Name: "1049", Weight: 1},
			{Name: "1050", Weight: 1},
			{Name: "1051", Weight: 1},
			{Name: "1052", Weight: 1},
			{Name: "1053", Weight: 1},
			{Name: "1054", Weight: 1},
			{Name: "1055", Weight: 1},
			{Name: "1056", Weight: 1},
			{Name: "1057", Weight: 1},
			{Name: "1058", Weight: 1},
			{Name: "1059", Weight: 1},
			{Name: "1060", Weight: 1},
			{Name: "1061", Weight: 1},
			{Name: "1062", Weight: 1},
			{Name: "1063", Weight: 1},
			{Name: "1064", Weight: 1},
			{Name: "1065", Weight: 1},
			{Name: "1066", Weight: 1},
			{Name: "1067", Weight: 1},
			{Name: "1068", Weight: 1},
			{Name: "1069", Weight: 1},
			{Name: "1070", Weight: 1},
			{Name: "1071", Weight: 1},
			{Name: "1072", Weight: 1},
			{Name: "1073", Weight: 1},
			{Name: "1074", Weight: 1},
			{Name: "1075", Weight: 1},
			{Name: "1076", Weight: 1},
			{Name: "1077", Weight: 1},
			{Name: "1078", Weight: 1},
			{Name: "1079", Weight: 1},
			{Name: "1080", Weight: 1},
			{Name: "1081", Weight: 1},
			{Name: "1082", Weight: 1},
			{Name: "1083", Weight: 1},
			{Name: "1084", Weight: 1},
			{Name: "1085", Weight: 1},
			{Name: "1086", Weight: 1},
			{Name: "1087", Weight: 1},
			{Name: "1088", Weight: 1},
			{Name: "1089", Weight: 1},
			{Name: "1090", Weight: 1},
			{Name: "1091", Weight: 1},
			{Name: "1092", Weight: 1},
			{Name: "1093", Weight: 1},
			{Name: "1094", Weight: 1},
			{Name: "1095", Weight: 1},
			{Name: "1096", Weight: 1},
			{Name: "1097", Weight: 1},
			{Name: "1098", Weight: 1},
			{Name: "1099", Weight: 1},
			{Name: "1100", Weight: 1},
		}
		newSchemes := slices.Clone(originalSchemes)
		RandomOrder(newSchemes)
		sugared.Infof("originalSchemes: %v, newSchemes: %v", originalSchemes, newSchemes)
		srr := NewSudokuRoundRobin("test", originalSchemes)

		// newSchemes := []Scheme{
		// 	{Name: "B", Weight: 1},
		// 	{Name: "C", Weight: 1},
		// 	{Name: "A", Weight: 1},
		// }

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			srr.ResetSchemes(newSchemes)
		}
	})
}
