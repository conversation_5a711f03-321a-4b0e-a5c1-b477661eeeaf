package distributes

import "testing"

func BenchmarkWeightedRoundRobin_Next(b *testing.B) {
	id := "test_weighted_round_robin"
	schemes := []Scheme{
		{Name: "scheme1", Weight: 6300},
		{Name: "scheme2", Weight: 2500},
		{Name: "scheme3", Weight: 1200},
	}
	weightedRoundroBin := NewWeightRoundRobin(id, schemes)
	totalCount := 0
	schemeCount := map[string]int{
		"scheme1": 0,
		"scheme2": 0,
		"scheme3": 0,
	}
	for i := 0; i < b.N; i++ {
		scheme := weightedRoundroBin.Next()
		totalCount++
		schemeCount[scheme.Name]++
	}
	b.Logf("Total:%d", totalCount)
	for name, count := range schemeCount {
		b.Logf("Scheme: %s, count: %d,rate: %f", name, count, float64(count)/float64(totalCount))
	}
}
