package models

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/exp/slices"
	"math/rand"
	"os"
	"sync"
	"time"
)

const AlgoSaveDir = "data/algo"

// DayDistributeProvider 日分发程序
type DayDistributeProvider struct {
	disposors sync.Map
}

// NewDayDistributeProvider 创建分发程序
func NewDayDistributeProvider() *DayDistributeProvider {
	dp := &DayDistributeProvider{
		disposors: sync.Map{},
	}
	go dp.clean()
	return dp
}

// DistrubteApiVersionAndCountry 根据apiVersion和countryCode 按照utc以天为单位分发
func (dp *DayDistributeProvider) DistrubteApiVersionAndCountry(bundleId, apiVersion, countryCode string, schemes []Scheme) Scheme {
	var wrr *WeightedRoundRobin
	dKey := dp.dayDistributeKey(bundleId, apiVersion, countryCode)
	if dis, ok := dp.disposors.Load(dKey); ok {
		wrr = dis.(*WeightedRoundRobin)
		wrr.resetSchemes(schemes)
	} else {
		wrr = NewWeightRoundRobin(dKey, schemes)
		dp.disposors.Store(dKey, wrr)
	}
	return wrr.Next()
}

// dayDistributeKey day分发key
func (dp *DayDistributeProvider) dayDistributeKey(bundleId, apiVersion, countryCode string) string {
	dateStr := time.Now().UTC().Format("20060102")
	return fmt.Sprintf("day_distribute_%s_%s_%s_%s", dateStr, bundleId, apiVersion, countryCode)
}

// clean 清理过期的分发
func (dp *DayDistributeProvider) clean() {
	sugared.Infof("cleaning distrubute per 1 hour")
	ticker := time.NewTicker(time.Minute * 10)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			dp.disposors.Range(func(k, v interface{}) bool {
				dl := v.(DistributeAlgo)
				if time.Now().Sub(dl.GetCreateTime()) > (24*time.Hour + 10*time.Minute) {
					sugared.Infof("cleaning distrubute %v", k)
					dp.disposors.Delete(k)
				}
				return true
			})
		}
	}
}

func (dp *DayDistributeProvider) Close(ctx context.Context) error {
	<-ctx.Done()
	dp.disposors.Range(func(k, v interface{}) bool {
		v.(DistributeAlgo).Close()
		return true
	})
	return nil
}

type DistributeAlgo interface {
	GetCreateTime() time.Time
	Next() Scheme
	Close()
}

// Scheme 分发方案 Name: 方案号，Weight：权重
type Scheme struct {
	Name   string
	Weight int
}

// WeightedRoundRobin 加权轮训
type WeightedRoundRobin struct {
	id            string
	mux           sync.Mutex
	createTime    time.Time
	schemes       []Scheme
	currentIndex  int
	currentWeight int
	gcdWeight     int
	maxWeight     int
	n             int
}

// NewWeightRoundRobin 新建加权轮训
func NewWeightRoundRobin(id string, schemes []Scheme) *WeightedRoundRobin {
	if len(schemes) == 0 {
		sugared.Errorf("schems list cannot be empty")
		return nil
	}
	id = "wrr_" + id
	currentIndex := -1
	currentWeight := 0
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
		currentWeight = status["currentWeight"]
	}
	wrr := &WeightedRoundRobin{
		id:            id,
		createTime:    time.Now(),
		schemes:       schemes,
		currentIndex:  currentIndex,
		currentWeight: currentWeight,
		n:             len(schemes),
	}
	maxWeight := wrr.getMaxWeight(schemes)
	if maxWeight == 0 {
		sugared.Errorf("all schemes have weight of 0")
		return nil
	}
	wrr.maxWeight = maxWeight
	wrr.gcdWeight = wrr.getGCDWeight(schemes)
	return wrr
}

// resetSchemes 重置方案列表 当前方案列表与提供方案列表不同的情况下重置
func (wrr *WeightedRoundRobin) resetSchemes(schemes []Scheme) {
	if !slices.Equal(wrr.schemes, schemes) && len(schemes) > 0 {
		maxWeight := wrr.getMaxWeight(schemes)
		if maxWeight > 0 {
			wrr.schemes = schemes
			wrr.maxWeight = maxWeight
			wrr.gcdWeight = wrr.getGCDWeight(schemes)
			wrr.n = len(schemes)
		}
	}
}

func (wrr *WeightedRoundRobin) GetCreateTime() time.Time {
	return wrr.createTime
}

// Next 获取下一个要分发的方案
func (wrr *WeightedRoundRobin) Next() Scheme {
	wrr.mux.Lock()
	defer wrr.mux.Unlock()
	if len(wrr.schemes) == 1 {
		return wrr.schemes[0]
	}
	for {
		wrr.currentIndex = (wrr.currentIndex + 1) % wrr.n
		if wrr.currentIndex == 0 {
			wrr.currentWeight = wrr.currentWeight - wrr.gcdWeight
			if wrr.currentWeight <= 0 {
				wrr.currentWeight = wrr.maxWeight
				if wrr.currentWeight == 0 {
					return Scheme{}
				}
			}
		}
		if wrr.schemes[wrr.currentIndex].Weight >= wrr.currentWeight {
			return wrr.schemes[wrr.currentIndex]
		}
	}
}

// getMaxWeight 获取最大权重
func (wrr *WeightedRoundRobin) getMaxWeight(schemes []Scheme) int {
	maxWeight := 0
	for _, scheme := range schemes {
		if scheme.Weight > maxWeight {
			maxWeight = scheme.Weight
		}
	}
	return maxWeight
}

// getGCD 获取最大公约数
func (wrr *WeightedRoundRobin) getGCD(a, b int) int {
	for b == 0 {
		return a
	}
	return wrr.getGCD(b, a%b)
}

// getGCDWeight 获取权重最大公约数
func (wrr *WeightedRoundRobin) getGCDWeight(schemes []Scheme) int {
	if len(schemes) == 0 {
		return 1
	}
	gcdWeight := schemes[0].Weight
	for _, scheme := range schemes {
		gcdWeight = wrr.getGCD(gcdWeight, scheme.Weight)
	}
	return gcdWeight
}

func (wrr *WeightedRoundRobin) Close() {
	wrr.mux.Lock()
	defer wrr.mux.Unlock()
	sugared.Infof("Close Weighted RoundRobin")
	status := map[string]int{
		"currentIndex":  wrr.currentIndex,
		"currentWeight": wrr.currentWeight,
	}
	saveAlgoStatus(wrr.id, status)
}

// saveAlgoStatus 保存算法状态
func saveAlgoStatus(id string, algoStatus map[string]int) {
	if _, err := os.Stat(AlgoSaveDir); err != nil {
		err = os.MkdirAll(AlgoSaveDir, 0755)
		if err != nil {
			sugared.Errorf("error creating algo save dir %s: %v", AlgoSaveDir, err)
			return
		}
	}
	filename := fmt.Sprintf("%s/%s.json", AlgoSaveDir, id)
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0755)
	if err != nil {
		sugared.Errorf("error opening algo save file %s: %v", filename, err)
		return
	}
	defer file.Close()
	encoder := json.NewEncoder(file)
	err = encoder.Encode(algoStatus)
	if err != nil {
		sugared.Errorf("error encoding algo save file %s: %v", filename, err)
	}
}

// loadAlgoStatus 从文件载入算法状态
func loadAlgoStatus(id string) (algoStatus map[string]int) {
	algoStatus = make(map[string]int)
	filename := fmt.Sprintf("%s/%s.json", AlgoSaveDir, id)
	file, err := os.Open(filename)
	if err != nil {
		//sugared.Errorf("error opening algo save file %s: %v", filename, err)
		return nil
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&algoStatus)
	if err != nil {
		sugared.Errorf("error decoding algo save file %s: %v", filename, err)
		return nil
	}
	_ = os.Remove(filename)
	return algoStatus
}

// RoundRobin 轮询算法
type RoundRobin struct {
	id           string
	createTime   time.Time
	schemes      []Scheme
	currentIndex int
	mux          sync.Mutex
}

// NewRoundRobin 新建轮询算法
func NewRoundRobin(id string, schemes []Scheme) *RoundRobin {
	if len(schemes) == 0 {
		sugared.Errorf("schemes list cannot be empty")
		return nil
	}
	id = "rr_" + id
	currentIndex := 0
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
	}
	return &RoundRobin{
		id:           id,
		currentIndex: currentIndex,
		schemes:      schemes,
		createTime:   time.Now(),
	}
}

func (rr *RoundRobin) GetCreateTime() time.Time {
	return rr.createTime
}

// Next 获取下一个scheme
func (rr *RoundRobin) Next() Scheme {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	if len(rr.schemes) == 1 {
		return rr.schemes[0]
	}
	scheme := rr.schemes[rr.currentIndex]
	rr.currentIndex = (rr.currentIndex + 1) % (len(rr.schemes))
	return scheme
}

func (rr *RoundRobin) Close() {
	rr.mux.Lock()
	defer rr.mux.Unlock()
	status := map[string]int{"currentIndex": rr.currentIndex}
	saveAlgoStatus(rr.id, status)
}

type SudokuRoundRobin struct {
	*RoundRobin
	sequence       bool
	changeSequence bool
}

func NewSudokuRoundRobin(id string, schemes []Scheme) *SudokuRoundRobin {
	if len(schemes) == 0 {
		sugared.Errorf("schemes list cannot be empty")
		return nil
	}
	//取消随机，默认取第一个
	currentIndex := 0
	//currentIndex := rand.Intn(len(schemes))
	sequence := true
	changeSequence := false
	id = "sdk_rr_" + id
	if status := loadAlgoStatus(id); status != nil {
		currentIndex = status["currentIndex"]
		sequence = intToBool(status["sequence"])
		changeSequence = intToBool(status["changeSequence"])
	}
	return &SudokuRoundRobin{
		RoundRobin: &RoundRobin{
			id:           id,
			createTime:   time.Now(),
			schemes:      schemes,
			currentIndex: currentIndex,
		},
		sequence:       sequence,
		changeSequence: changeSequence,
	}
}

// Next 打乱策略号的顺序，依次分配。一轮分配完之后，将数组重新打乱再分配，以此类推
func (sdk *SudokuRoundRobin) Next() Scheme {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()
	if len(sdk.schemes) == 1 {
		return sdk.schemes[0]
	}
	if sdk.currentIndex == 0 {
		fmt.Println(sdk.schemes)
	}

	scheme := sdk.schemes[sdk.currentIndex]

	//一轮分配完毕，打乱顺序，重新分配
	if sdk.currentIndex == len(sdk.schemes)-1 {
		sdk.schemes = RandomOrder(sdk.schemes)
		sdk.currentIndex = 0
	} else {
		sdk.currentIndex = (sdk.currentIndex + 1) % (len(sdk.schemes))
	}

	return scheme

	//scheme := sdk.schemes[sdk.currentIndex]
	//if sdk.changeSequence {
	//	sdk.changeSequence = false
	//	return scheme
	//}
	//if sdk.sequence {
	//	sdk.currentIndex = (sdk.currentIndex + 1) % (len(sdk.schemes))
	//	if sdk.currentIndex == len(sdk.schemes)-1 {
	//		sdk.sequence = false
	//		sdk.changeSequence = true
	//	}
	//} else {
	//	sdk.currentIndex = (sdk.currentIndex - 1) % (len(sdk.schemes))
	//	if sdk.currentIndex == 0 {
	//		sdk.sequence = true
	//		sdk.changeSequence = true
	//	}
	//}
	//return scheme
}

func (sdk *SudokuRoundRobin) Close() {
	sdk.mux.Lock()
	defer sdk.mux.Unlock()
	status := map[string]int{
		"currentIndex":   sdk.currentIndex,
		"sequence":       boolToInt(sdk.sequence),
		"changeSequence": boolToInt(sdk.changeSequence),
	}
	saveAlgoStatus(sdk.id, status)
}

// boolToInt 布尔值 true = 1， false = 0
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// intToBool 1 = true 否则为false
func intToBool(i int) bool {
	if i == 1 {
		return true
	}
	return false
}

// RandomOrder 随机打乱顺序
func RandomOrder(schemes []Scheme) []Scheme {

	// 使用时间作为随机种子
	rand.Seed(time.Now().UnixNano())

	// Fisher-Yates 洗牌算法打乱顺序
	for i := len(schemes) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		schemes[i], schemes[j] = schemes[j], schemes[i]
	}

	return schemes
}
