package models

import (
	"encoding/json"
	"testing"
	"time"
)

func BenchmarkWeightRoundRobin(b *testing.B) {
	b.Logf("Start benchmark weight round-robin")
	var schemes = []Scheme{
		{Name: "2982", Weight: 100},
		{Name: "2983", Weight: 100},
		{Name: "2926", Weight: 29},
		{Name: "2927", Weight: 29},
		{Name: "2928", Weight: 29},
		{Name: "9936", Weight: 4013},
		{Name: "4861", Weight: 5700},
	}
	statics := map[string]*struct {
		Count      int64
		TimeLength int64
	}{
		"2982": {
			Count:      0,
			TimeLength: 0,
		},
		"2983": {
			Count:      0,
			TimeLength: 0,
		},
		"2926": {
			Count:      0,
			TimeLength: 0,
		},
		"2927": {
			Count:      0,
			TimeLength: 0,
		},
		"2928": {
			Count:      0,
			TimeLength: 0,
		},
		"9936": {
			Count:      0,
			TimeLength: 0,
		},
		"4861": {
			Count:      0,
			TimeLength: 0,
		},
	}
	var totalCount int64 = 0
	var totalLength int64 = 0
	wrr := NewWeightRoundRobin("test", schemes)
	for i := 0; i < b.N; i++ {
		startTime := time.Now()
		scheme := wrr.Next()
		sub := time.Now().Sub(startTime)
		static := statics[scheme.Name]
		static.Count++
		static.TimeLength += int64(sub)
		totalCount++
		totalLength += int64(sub)
	}
	b.Logf("statics Lenght: %d", len(statics))

	for scheme, static := range statics {
		count := static.Count
		timeLength := static.TimeLength
		var perLength int64
		if count > 0 {
			perLength = timeLength / count
		} else {
			perLength = 0
		}
		var rate float64
		if totalCount > 0 {
			rate = float64(count) / float64(totalCount)
		} else {
			rate = 0
		}
		b.Logf("SchemeWeight: %s, Count: %d,TimeLength: %d, PerTimeLength: %d,Rate: %f", scheme, count, timeLength, perLength, rate)
	}
	b.Logf("TotalCount: %d,TotalLength: %d,PerLength: %d", totalCount, totalLength, totalLength/totalCount)
	b.Logf("Over benchmark weight round-robin")
}

// CSR分流测试
func TestSudokuRoundRobin(t *testing.T) {

	var schemes = []Scheme{
		{Name: "100", Weight: 0},
		{Name: "101", Weight: 0},
		{Name: "102", Weight: 0},
		{Name: "103", Weight: 0},
		{Name: "104", Weight: 0},
		{Name: "105", Weight: 0},
		{Name: "106", Weight: 0},
		{Name: "107", Weight: 0},
		{Name: "108", Weight: 0},
		{Name: "109", Weight: 0},
	}

	// 随机打乱顺序
	schemes = RandomOrder(schemes)

	sudokuRR := NewSudokuRoundRobin("test", schemes)
	for i := 0; i < 33; i++ {
		scheme := sudokuRR.Next()
		t.Logf("scheme: %s", scheme.Name)
	}
}

// CSR分流测试
func TestRobain(t *testing.T) {

	var schemes = "{\n\t\"country\": {\n\t\t\"kind\": \"list\",\n\t\t\"value\": [ \"in\", \"\",\"us\", \"mx\", \"br\", \"co\", \"ph\", \"ar\", \"tr\", \"cl\", \"pe\", \"id\", \"vn\", \"ve\", \"ua\", \"th\", \"es\", \"my\", \"ru\", \"gb\", \"za\", \"eg\", \"pl\", \"ca\", \"ec\", \"fr\", \"au\", \"ma\", \"de\", \"bo\", \"kz\", \"ro\", \"jp\", \"dz\", \"kr\", \"pk\", \"tw\", \"cr\", \"uy\", \"it\", \"gt\", \"il\", \"sa\", \"iq\", \"uz\", \"jo\", \"tn\", \"az\", \"pa\", \"rs\", \"by\", \"nl\", \"pt\", \"ae\", \"hr\", \"gr\", \"ke\", \"hu\", \"cz\", \"sv\", \"pr\"]\n\t},\n\t\"start_time\": {\n\t\t\"kind\": \"range\",\n\t\t\"value\": [0, 2749, 3764, 4900, 5977, 7153, 8532, 10544, 13792, 20953]\n\t},\n\t\"country_group\": {\n\t\t\"kind\": \"group\",\n\t\t\"value\": [[\"\"], [\"US\", \"CA\", \"AU\"], [\"JP\", \"KR\", \"TW\"], [\"ES\", \"NL\", \"DE\", \"FR\", \"SE\", \"GB\", \"IT\", \"CH\"]]\n\t}\n}"
	archiveConf := make(map[string]any)
	err := json.Unmarshal([]byte(schemes), &archiveConf)
	if err != nil {
		t.Logf("json.Unmarshal: %s", err)
	}
	// 定义一个顺序的 key 切片
	keys := []string{"country", "start_time", "country_group"}

	for i := 0; i < len(keys); i++ {
		key := keys[i]
		t.Logf("Index: %d, Key: %s", i, key)

		// 获取对应 key 的值
		value, ok := archiveConf[key]
		if !ok {
			t.Logf("Key not found: %s", key)
			continue
		}

		// 处理对应 key 的值
		t.Logf("Value: %v", value)
	}
}
