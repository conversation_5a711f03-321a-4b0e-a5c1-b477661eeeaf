package models

import (
	"context"
	"time"

	"go.uber.org/zap"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/datatester/kafka"
	"hungrystudio.com/datatester/models/distributes"
)

var sugared *zap.SugaredLogger

func SetSugaredLogger(s *zap.SugaredLogger) {
	sugared = s
}

var kafkaRemote *kafka.KafkaRemote

func SetKafkaRemote(kr *kafka.KafkaRemote) {
	kafkaRemote = kr
}

var redisClient cache.RedisClient

func SetRedisClient(rc cache.RedisClient) {
	redisClient = rc
}

var csrParamRedis cache.RedisClient

func SetCsrParamRedis(c cache.RedisClient) {
	csrParamRedis = c
}

func RedisTimeoutContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), 10*time.Millisecond)
}

//var dayDistributeProvider *DayDistributeProvider
//
//func SetDistributeProvider(p *DayDistributeProvider) {
//	dayDistributeProvider = p
//}

var distributeFactory *distributes.DistributeFactory

func SetDistributeFactory(f *distributes.DistributeFactory) {
	distributeFactory = f
}

var didiDistributeProvider *DidiDistributeProvider

func SetDidiDistributeProvider(p *DidiDistributeProvider) {
	didiDistributeProvider = p
}

const (
	ParamNameServerCountryCode = "server_country_code"
	ParamNameServerIP          = "server_ip"
	ParamNameLayerType         = "layer_type"
	ParamNameLayerTypeValue    = "p5"
)

const (
	PlatformIOS     = "ios"
	PlatformAndroid = "gp"
)

const (
	BundleIdBlockAndroid   = "com.block.juggle"
	BundleIdBlockIos       = "com.blockpuzzle.us.ios"
	BundleIdSudokuAndroid  = "com.mathbrain.sudoku"
	BundleIdSudokuIos      = "com.mathbrain.sudoku.ios"
	BundleIdMatch3dAndroid = "com.hungrystudio.matchout.match3d"
	BundleIdMatch3dIos     = "com.hungrystudio.matchout"
)

const CountryCodeDefault string = "QT"

var CountryC16 = map[string]struct{}{
	"US":               {},
	"BR":               {},
	"KR":               {},
	"DE":               {},
	"IN":               {},
	"JP":               {},
	"MX":               {},
	"GB":               {},
	"ID":               {},
	"FR":               {},
	"AU":               {},
	"CA":               {},
	"TH":               {},
	"IT":               {},
	"ES":               {},
	CountryCodeDefault: {}}
