SELECT SUM(`count`) FROM ( 
	SELECT COUNT(*) as `count` From gt_user_experiments_0 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_1 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_2 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_3  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_4  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_5  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_6  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_7  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_8  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_9  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_10 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_11 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_12 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_13  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_14  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_15  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_16  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_17  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_18  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_19  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_20 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_21 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_22 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_23  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_24  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_25  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_26  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_27  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_28  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_29  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_30 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_31 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_32 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_33  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_34  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_35  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_36  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_37  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_38  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_39  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_40 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_41 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_42 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_43  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_44  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_45  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_46  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_47  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_48  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_49  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_50 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_51 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_52 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_53  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_54  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_55  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_56  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_57  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_58  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_59  WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_60 WHERE bundle_id = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_61 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_62 WHERE bundle_id  = 'com.block.juggle'
	UNION ALL 
	SELECT COUNT(*) as `count` From gt_user_experiments_63  WHERE bundle_id  = 'com.block.juggle'
) as combined_counts
