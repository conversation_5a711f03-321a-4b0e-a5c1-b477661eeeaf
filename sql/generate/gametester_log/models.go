package models

type UserDistributes struct {
	Id             int    `xorm:"'id' not null pk autoincr INT"`
	BundleId       string `xorm:"'bundle_id' comment('包名') index(bundle_buckdet_exp) VARCHAR(64)"`
	Uid            string `xorm:"'uid' not null VARCHAR(64)"`
	BucketId       int    `xorm:"'bucket_id' not null comment('桶ID') index(bundle_buckdet_exp) INT"`
	Exp            string `xorm:"'exp' not null comment('实验方案') index(bundle_buckdet_exp) VARCHAR(32)"`
	ExpType        int    `xorm:"'exp_type' comment('实验类型') TINYINT"`
	CsrIndentity   string `xorm:"'csr_indentity' comment('csr分流标识，活跃实验有') VARCHAR(32)"`
	InstallTime    int64  `xorm:"'install_time' not null comment('安装时间') index BIGINT"`
	DistributeTime int64  `xorm:"'distribute_time' comment('方案下发时间') BIGINT"`
	DistributeDate int    `xorm:"'distribute_date' not null comment('分配日期') index INT"`
	Hostname       string `xorm:"'hostname' comment('主机名称') index(hostname_sindex) VARCHAR(32)"`
	SIndex         int    `xorm:"'s_index' comment('主机服务索引') index(hostname_sindex) INT"`
}

func (m *UserDistributes) TableName() string {
	return "user_distributes"
}
