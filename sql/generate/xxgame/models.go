package models

import (
	"time"
)

type AbtestCsrConfig struct {
	Id            uint      `xorm:"'id' not null pk autoincr comment('自增ID') UNSIGNED INT"`
	BundleId      string    `xorm:"'bundle_id' not null default '' comment('包体') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	ClientVersion string    `xorm:"'client_version' not null default '' comment('客户端版本') index(unqidx_bundle_cv_group) VARCHAR(64)"`
	GroupId       string    `xorm:"'group_id' not null default '' comment('分组标识') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	Ratio         int       `xorm:"'ratio' not null default 0 comment('流量生效百分比') INT"`
	Config        string    `xorm:"'config' not null comment('分组配置') MEDIUMTEXT(16777215)"`
	StartTs       int       `xorm:"'start_ts' default 0 comment('生效时间(单位：秒)') INT"`
	EndTs         int       `xorm:"'end_ts' default 0 comment('失效时间(单位：秒)') INT"`
	IsOpen        int       `xorm:"'is_open' default 0 comment('是否开启') TINYINT(1)"`
	CreatedAt     time.Time `xorm:"'created_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
	UpdatedAt     time.Time `xorm:"'updated_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
}

func (m *AbtestCsrConfig) TableName() string {
	return "abtest_csr_config"
}

type AbtestCsrConfigTransition struct {
	Id            uint      `xorm:"'id' not null pk autoincr comment('自增ID') UNSIGNED INT"`
	BundleId      string    `xorm:"'bundle_id' not null default '' comment('包体') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	ClientVersion string    `xorm:"'client_version' not null default '' comment('客户端版本') index(unqidx_bundle_cv_group) VARCHAR(64)"`
	MaxVersion    string    `xorm:"'max_version' not null comment('最大版本') VARCHAR(64)"`
	Type          string    `xorm:"'type' not null comment('类型 new 新用户， active 活跃用户') VARCHAR(16)"`
	GroupId       string    `xorm:"'group_id' not null default '' comment('分组标识') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	Ratio         int       `xorm:"'ratio' not null default 0 comment('流量生效百分比') INT"`
	MarkRatio     string    `xorm:"'mark_ratio' not null comment('同标识生效比例') VARCHAR(16)"`
	Config        string    `xorm:"'config' not null comment('分组配置') MEDIUMTEXT(16777215)"`
	StartTs       int       `xorm:"'start_ts' default 0 comment('生效时间(单位：秒)') INT"`
	EndTs         int       `xorm:"'end_ts' default 0 comment('失效时间(单位：秒)') INT"`
	IsOpen        int       `xorm:"'is_open' default 0 comment('是否开启') TINYINT(1)"`
	CreatedAt     time.Time `xorm:"'created_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
	UpdatedAt     time.Time `xorm:"'updated_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
}

func (m *AbtestCsrConfigTransition) TableName() string {
	return "abtest_csr_config_transition"
}

type BusinessConfig struct {
	Id          uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Bundleid    string `xorm:"'bundleid' not null comment('包名') unique(bundleid_version_adwaytype_adwaynum) VARCHAR(64)"`
	Version     string `xorm:"'version' not null comment('api版本') unique(bundleid_version_adwaytype_adwaynum) index VARCHAR(64)"`
	Adwaytype   string `xorm:"'adwaytype' not null comment('方案类型') unique(bundleid_version_adwaytype_adwaynum) VARCHAR(32)"`
	Adwaynum    string `xorm:"'adwaynum' not null comment('方案id') unique(bundleid_version_adwaytype_adwaynum) index VARCHAR(32)"`
	Config      string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate        string `xorm:"'rate' not null comment('占比') DECIMAL(10,2)"`
	Type        int    `xorm:"'type' not null default 0 comment('新增活跃 0活跃 1新增') INT"`
	MiniVersion string `xorm:"'mini_version' not null comment('最小appVersion') VARCHAR(16)"`
	WhiteList   string `xorm:"'white_list' comment('白名单') TEXT(65535)"`
	Status      int    `xorm:"'status' not null default 0 comment('0 生效 1 不生效') index INT"`
	Desc        string `xorm:"'desc' not null comment('描述') VARCHAR(512)"`
	Deleted     int    `xorm:"'deleted' not null comment('1 删除 0 未删除') TINYINT(1)"`
	CreateAt    int    `xorm:"'create_at' not null comment('创建时间') INT"`
	ModifyAt    int    `xorm:"'modify_at' not null comment('更新时间') INT"`
}

func (m *BusinessConfig) TableName() string {
	return "business_config"
}

type CsrConfig struct {
	Id            uint      `xorm:"'id' not null pk autoincr comment('自增ID') UNSIGNED INT"`
	BundleId      string    `xorm:"'bundle_id' not null default '' comment('包体') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	ClientVersion string    `xorm:"'client_version' not null default '' comment('客户端版本') index(unqidx_bundle_cv_group) VARCHAR(64)"`
	GroupId       string    `xorm:"'group_id' not null default '' comment('分组标识') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	Ratio         int       `xorm:"'ratio' not null default 0 comment('流量生效百分比') INT"`
	Config        string    `xorm:"'config' not null comment('分组配置') TEXT(65535)"`
	StartTs       int       `xorm:"'start_ts' default 0 comment('生效时间(单位：秒)') INT"`
	EndTs         int       `xorm:"'end_ts' default 0 comment('失效时间(单位：秒)') INT"`
	IsOpen        int       `xorm:"'is_open' default 0 comment('是否开启') TINYINT(1)"`
	CreatedAt     time.Time `xorm:"'created_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
	UpdatedAt     time.Time `xorm:"'updated_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
}

func (m *CsrConfig) TableName() string {
	return "csr_config"
}

type DefendConfig struct {
	Id       int    `xorm:"'id' not null pk autoincr INT"`
	BundleId string `xorm:"'bundle_id' not null comment('包名') unique(bundle_id_version) VARCHAR(64)"`
	Version  string `xorm:"'version' not null comment('版本') unique(bundle_id_version) VARCHAR(16)"`
	Config   string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	State    int    `xorm:"'state' not null comment('状态1 开启 0 关闭') INT"`
}

func (m *DefendConfig) TableName() string {
	return "defend_config"
}

type LiuliangJuggConfig struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Adwaynum int    `xorm:"'adwaynum' not null comment('方案id') index INT"`
	Version  string `xorm:"'version' not null comment('api版本') index VARCHAR(64)"`
	Config   string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate     string `xorm:"'rate' not null comment('占比') DECIMAL(10,2)"`
	Type     int    `xorm:"'type' not null default 0 comment('新增活跃 0活跃 1新增') INT"`
	Status   int    `xorm:"'status' not null comment('0 生效 1 不生效') index INT"`
}

func (m *LiuliangJuggConfig) TableName() string {
	return "liuliang_jugg_config"
}

type LiuliangJuggDjhConfig struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Adwaynum int    `xorm:"'adwaynum' not null comment('方案id') index INT"`
	Config   string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate     string `xorm:"'rate' not null comment('占比') DECIMAL(10,2)"`
	Mark     string `xorm:"'mark' not null default '' comment('多聚合标识') VARCHAR(32)"`
	Status   int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

func (m *LiuliangJuggDjhConfig) TableName() string {
	return "liuliang_jugg_djh_config"
}

type LiuliangJuggDoudiConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

func (m *LiuliangJuggDoudiConfig) TableName() string {
	return "liuliang_jugg_doudi_config"
}

type LiuliangJuggGuoshen struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Bundleid string `xorm:"'bundleid' not null default '' comment('包名') index(index_b_v) VARCHAR(200)"`
	Version  string `xorm:"'version' not null default '' comment('版本') index(index_b_v) VARCHAR(32)"`
	Type     int    `xorm:"'type' not null comment('类型') INT"`
	Status   int    `xorm:"'status' not null default 0 INT"`
}

func (m *LiuliangJuggGuoshen) TableName() string {
	return "liuliang_jugg_guoshen"
}

type LiuliangJuggNoNormalConfig struct {
	Id     uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Opt    string `xorm:"'opt' not null default '' comment('opt') index VARCHAR(32)"`
	Config string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

func (m *LiuliangJuggNoNormalConfig) TableName() string {
	return "liuliang_jugg_no_normal_config"
}

type LiuliangJuggQaAdwaynum struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Thinkuid string `xorm:"'thinkuid' not null default '' index VARCHAR(64)"`
	Adwaynum string `xorm:"'adwaynum' not null default '' VARCHAR(64)"`
}

func (m *LiuliangJuggQaAdwaynum) TableName() string {
	return "liuliang_jugg_qa_adwaynum"
}

type LiuliangJuggSpecialConfig struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Thinkuid string `xorm:"'thinkuid' not null default '' index VARCHAR(64)"`
	Config   string `xorm:"'config' not null TEXT(65535)"`
}

func (m *LiuliangJuggSpecialConfig) TableName() string {
	return "liuliang_jugg_special_config"
}

type LiuliangJuggTester struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Bundleid string `xorm:"'bundleid' not null default '' comment('包名') index(index_b_v) VARCHAR(200)"`
	Version  string `xorm:"'version' not null default '' comment('版本') index(index_b_v) VARCHAR(32)"`
	Type     int    `xorm:"'type' not null comment('类型') INT"`
	Status   int    `xorm:"'status' not null default 0 INT"`
}

func (m *LiuliangJuggTester) TableName() string {
	return "liuliang_jugg_tester"
}

type LiuliangJuggTianchongConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Tcnum      string `xorm:"'TCNum' not null default '' comment('TCNum') VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate       string `xorm:"'rate' not null comment('比例') DECIMAL(10,2)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

func (m *LiuliangJuggTianchongConfig) TableName() string {
	return "liuliang_jugg_tianchong_config"
}

type TestergameQaConfig struct {
	Id         int    `xorm:"'id' not null pk autoincr INT"`
	Bundleid   string `xorm:"'bundleid' not null unique(bundleid_uid) VARCHAR(64)"`
	Uid        string `xorm:"'uid' not null unique(bundleid_uid) VARCHAR(64)"`
	Bucket     int    `xorm:"'bucket' not null INT"`
	Experiment string `xorm:"'experiment' not null VARCHAR(32)"`
	State      int    `xorm:"'state' not null index TINYINT"`
}

func (m *TestergameQaConfig) TableName() string {
	return "testergame_qa_config"
}
