package models

type GtUserExperiments struct {
	BundleId       string `xorm:"'bundle_id' not null pk comment('包名') VARCHAR(128)"`
	Uid            string `xorm:"'uid' not null pk comment('用户访客ID') VARCHAR(64)"`
	BucketId       int    `xorm:"'bucket_id' not null comment('桶ID') INT"`
	ExperimentId   string `xorm:"'experiment_id' not null comment('实验ID') VARCHAR(32)"`
	ExperimentType int    `xorm:"'experiment_type' not null comment('实验类型') INT"`
	DistributeTime int64  `xorm:"'distribute_time' not null comment('分配时间') BIGINT"`
}
