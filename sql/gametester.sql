SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

CREATE TABLE IF NOT EXISTS `gt_user_experiments` (
   `bundle_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '包名',
   `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户访客ID',
   `bucket_id` int NOT NULL COMMENT '桶ID',
   `experiment_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实验ID',
   `experiment_type` int NOT NULL COMMENT '实验类型',
   `distribute_time` bigint NOT NULL COMMENT '分配时间',
   PRIMARY KEY (`bundle_id`,`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表';

SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';
CREATE TABLE IF NOT EXISTS `gt_user_experiments_0` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_1` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_2` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_3` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_4` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_5` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_6` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_7` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_8` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_9` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_10` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_11` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_12` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_13` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_14` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_15` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_16` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_17` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_18` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_19` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_20` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_21` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_22` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_23` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_24` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_25` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_26` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_27` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_28` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_29` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_30` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_31` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_32` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_33` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_34` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_35` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_36` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_37` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_38` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_39` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_40` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_41` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_42` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_43` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_44` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_45` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_46` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_47` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_48` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_49` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_50` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_51` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_52` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_53` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_54` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_55` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_56` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_57` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_58` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_59` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_60` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_61` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_62` LIKE `gt_user_experiments`;
CREATE TABLE IF NOT EXISTS `gt_user_experiments_63` LIKE `gt_user_experiments`;