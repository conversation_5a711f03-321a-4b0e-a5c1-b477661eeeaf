package console

import (
	"flag"
	"fmt"
	"hungrystudio.com/datatester/models/testergame"
	"os"
)

func PushOverSocre() {
	var bundle string
	var filename string
	var maxCount int
	var env string
	cmd := flag.NewFlagSet(ToolNanmePushOverScore, flag.ExitOnError)
	cmd.StringVar(&env, "env", testergame.EnvTesting, "上传环境")
	cmd.StringVar(&bundle, "bundle", "", "上传包名")
	cmd.StringVar(&filename, "file", "", "上传用户文件")
	cmd.IntVar(&maxCount, "max", 0, "最大上传用户数量")
	cmd.Usage = func() {
		_, _ = fmt.Fprintf(cmd.Output(), "Usage of %s:\n", os.Args[2])
		cmd.PrintDefaults()
	}
	err := cmd.Parse(os.Args[3:])
	if err != nil {
		panic(err)
	}
	fmt.Printf("包:%s,filename: %s, maxCount: %d\n", bundle, filename, maxCount)

	switch env {
	case testergame.EnvTesting, testergame.EnvGray:
		if localRedis == nil {
			panic("localReis is nil")
		}
		testergame.PushOverScoreUser(bundle, filename, maxCount, localRedis)
	case testergame.EnvProd:
		if prodRedis == nil {
			panic("prodReis is nil")
		}
		testergame.PushOverScoreUser(bundle, filename, maxCount, prodRedis)
	default:
		panic(fmt.Sprintf("env: %s overScoreInterceptor not support", env))
	}

}
