package console

import (
	"fmt"
	"hungrystudio.com/core/cache"
	"os"
	"xorm.io/xorm"
)

const (
	ToolNanmePushOverScore = "push-over-score"
	ToolNameTestDistribute = "test-distribute"
)

var localRedis, prodRedis cache.RedisClient

func SetLocalRedis(r cache.RedisClient) {
	localRedis = r
}

func SetProdRedis(r cache.RedisClient) {
	prodRedis = r
}

var localMysqlClient xorm.EngineInterface

func SetLocalMysqlClient(client xorm.EngineInterface) {
	localMysqlClient = client
}

var prodMysqlClient xorm.EngineInterface

func SetProdMysqlClient(client xorm.EngineInterface) {
	prodMysqlClient = client
}

func ParseConsole() {
	fmt.Println("命令行模式")
	if len(os.Args) > 2 {
		toolName := os.Args[2]
		switch toolName {
		case ToolNanmePushOverScore:
			PushOverSocre()
		case ToolNameTestDistribute:
			TestDistribute()
		}
	}
}
