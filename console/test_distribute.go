package console

import (
	"bufio"
	"bytes"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"hungrystudio.com/core/uuid"
	"hungrystudio.com/datatester/models/testergame"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"time"
)

const (
	DefaultGameVersion = "9.9.9"
	DefaultSdkVersion
	UserStoreDir = "data/user-stores"
)

type UserStore struct {
	Uid            string `json:"uid"`
	Bundle         string `json:"bundle"`
	Bucket         int    `json:"bucket"`
	GameVersion    string `json:"game_version"`
	SdkVersion     string `json:"sdk_version"`
	Experiment     string `json:"experiment"`
	ExperimentType int    `json:"experimentType"`
	InstallTime    int64  `json:"installTime"`
}

type TestDistributeConfig struct {
	TestId       string   `yaml:"testId" json:"testId"`
	Bundle       string   `yaml:"bundle" json:"bundle"`             // 测试包名
	Count        int      `yaml:"count" json:"count"`               // 测试用户数
	GameVersions []string `yaml:"gameVersions" json:"gameVersions"` // 用户可选资源版本号列表
	SdkVersions  []string `yaml:"sdkVersions" json:"sdkVersions"`   // 用户可选sdk版本列表
	MinTime      string   `yaml:"minTime" json:"minTime"`           // 生成用户installTime最小时间
	MaxTime      string   `yaml:"maxTime" json:"maxTime"`           // 生成用户installTime最大时间
	RequestApi   string   `yaml:"requestApi" json:"requestApi"`     // 测试请求地址
	RequestCount int      `yaml:"requestCount" json:"requestCount"` // 请求测试
	Clean        bool     `yaml:"clean" json:"clean"`               // 是否清理

	userStoreDir  string
	usersFilename string
}

func TestDistribute() {
	config, err := parseConfig()
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("ParseConfig: %+v\n", *config)
	config.userStoreDir = fmt.Sprintf("%s/%s", UserStoreDir, config.TestId)
	_, err = os.Stat(config.userStoreDir)
	if err != nil {
		err = os.MkdirAll(UserStoreDir, 0777)
		if err != nil {
			log.Fatal(err)
		}
	}
	config.usersFilename = fmt.Sprintf("data/%s-%s.txt", config.TestId, config.Bundle)
	err = initTestUsers(config)
	if err != nil {
		log.Fatal(err)
	}
	for i := 0; i < config.RequestCount; i++ {
		scannerUsers(config, execRequestGameInit)
	}
}

func initTestUsers(config *TestDistributeConfig) error {
	_, err := os.Stat(config.usersFilename)
	if err != nil {
		writeFile, err := os.OpenFile(config.usersFilename, os.O_RDWR|os.O_CREATE, 0666)
		if err == nil {
			// 生成用户
			for i := 0; i < config.Count; i++ {
				_, _ = writeFile.Write([]byte(uuid.NewUUID() + "\n"))
			}
			writeFile.Close()
		} else {
			return err
		}
	}
	return nil
}

func scannerUsers(config *TestDistributeConfig, execFunc func(config *TestDistributeConfig, user *UserStore) error) {
	usersFile, err := os.Open(config.usersFilename)
	if err != nil {
		log.Fatalf("os.Open(%s) err: %v\n", config.usersFilename, err)
		return
	}
	defer usersFile.Close()
	scanner := bufio.NewScanner(usersFile)
	for scanner.Scan() {
		uid := scanner.Text()
		installTime, err := makeRandomTime(config.MinTime, config.MaxTime)
		if err != nil {
			log.Fatalf("makeRandomTime(%s) err: %v\n", uid, err)
			return
		}
		userStore, err := getUser(uid, config)
		if err != nil {
			userStore = &UserStore{
				Uid:         uid,
				Bundle:      config.Bundle,
				InstallTime: installTime,
				GameVersion: config.GameVersions[rand.Intn(len(config.GameVersions))],
				SdkVersion:  config.SdkVersions[rand.Intn(len(config.SdkVersions))],
			}
		}
		err = execFunc(config, userStore)
		if err != nil {
			log.Fatalf("execFunc err: %v\n", err)
		}
		err = saveUser(userStore)
		if err != nil {
			log.Printf("saveUser(%s) err: %v\n", uid, err)
		}
	}
}

//type GameInitResponse struct {
//	ExperimentDataMap map[string]struct {
//		DefaultLayer testergame.ExperimentResult `json:"defaultLayer"`
//	} `json:"experimentDataMap"`
//}

func execRequestGameInit(config *TestDistributeConfig, user *UserStore) error {
	requestData := testergame.GameInitRequest{
		Uid:         user.Uid,
		BundleId:    user.Bundle,
		InstallTime: user.InstallTime,
		GameVersion: user.GameVersion,
		SdkVersion:  user.SdkVersion,
	}
	if user.Experiment != "" {
		requestData.GameWayNum = user.Experiment
	}
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	body, err := json.Marshal(requestData)
	if err != nil {
		return err
	}
	request, err := http.NewRequest(http.MethodPost, config.RequestApi, bytes.NewReader(body))
	if err != nil {
		return err
	}
	request.Header.Set("Content-Type", "application/json")
	response, err := client.Do(request)
	if err != nil {
		return err
	}
	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("http status code: %d", response.StatusCode)
	}
	defer response.Body.Close()
	result := &struct {
		ExperimentDataMap *struct {
			DefaultLayer *testergame.ExperimentResult `json:"defaultLayer"`
		} `json:"experimentDataMap"`
	}{}
	bodyResult, err := io.ReadAll(response.Body)
	if err != nil {
		return err
	}
	err = json.Unmarshal(bodyResult, result)
	if err != nil {
		log.Fatalf("result:%s err: %v\n", string(bodyResult), err)
		return err
	}
	if result.ExperimentDataMap != nil {
		if result.ExperimentDataMap.DefaultLayer != nil {
			user.Bucket = result.ExperimentDataMap.DefaultLayer.BucketId
			user.Experiment = result.ExperimentDataMap.DefaultLayer.GameWayNum
			user.ExperimentType = result.ExperimentDataMap.DefaultLayer.ExperimentType
			log.Printf("userStore: %+v\n", *user)
		}

	}
	return nil
}

func parseConfig() (*TestDistributeConfig, error) {
	var configFilename string
	cmd := flag.NewFlagSet(ToolNameTestDistribute, flag.ExitOnError)
	cmd.StringVar(&configFilename, "config", "", "测试分流配置文件")
	cmd.Usage = func() {
		_, _ = fmt.Fprintf(cmd.Output(), "Usage of %s:\n", os.Args[2])
		cmd.PrintDefaults()
	}

	err := cmd.Parse(os.Args[3:])
	if err != nil {
		return nil, err
	}
	var config = &TestDistributeConfig{}
	if configFilename != "" {
		configFile, err := os.Open(configFilename)
		if err != nil {
			return nil, err
		}
		decoder := json.NewDecoder(configFile)

		err = decoder.Decode(config)
		if err != nil {
			return nil, err
		}
	}
	return config, nil
}

func saveUser(userStore *UserStore) error {
	data, err := json.Marshal(userStore)
	if err != nil {
		return err
	}
	userFilename := userStoreFilename(userStore.Bundle, userStore.Uid)
	userFile, err := os.OpenFile(userFilename, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		return err
	}
	defer userFile.Close()
	_, err = userFile.Write(data)
	if err != nil {
		return err
	}
	return nil
}

func userStoreFilename(bundle, uid string) string {
	userBundleDir := fmt.Sprintf("%s/%s", UserStoreDir, bundle)
	if _, err := os.Stat(userBundleDir); os.IsNotExist(err) {
		if err = os.Mkdir(userBundleDir, 0777); err != nil {
			log.Fatalf("os.Mkdir(%s) err: %v\n", userBundleDir, err)
		}
	}
	return fmt.Sprintf("%s/%s/%s.txt", UserStoreDir, bundle, uid)
}

func getUser(uid string, config *TestDistributeConfig) (*UserStore, error) {
	userFilename := userStoreFilename(config.Bundle, uid)
	userFile, err := os.Open(userFilename)
	if err != nil {
		installTime, err := makeRandomTime(config.MinTime, config.MaxTime)
		if err != nil {
			return nil, err
		}
		gameVersion := config.GameVersions[rand.Intn(len(config.GameVersions))]
		sdkVersion := config.SdkVersions[rand.Intn(len(config.SdkVersions))]
		return &UserStore{
			Uid:         uid,
			Bundle:      config.Bundle,
			InstallTime: installTime,
			GameVersion: gameVersion,
			SdkVersion:  sdkVersion,
		}, nil
	}
	defer userFile.Close()
	var userStore *UserStore
	err = json.NewDecoder(userFile).Decode(userStore)
	if err != nil {
		return nil, err
	}
	return userStore, nil
}

func makeRandomTime(min, max string) (int64, error) {
	minTime, err := time.Parse("2006-01-02 15:05", min)
	if err != nil {
		return 0, err
	}
	minUnixMilli := minTime.UnixMilli()
	maxTime, err := time.Parse("2006-01-02 15:05", max)
	if err != nil {
		return 0, err
	}
	maxUnixMilli := maxTime.UnixMilli()
	return minUnixMilli + rand.Int63n(maxUnixMilli-minUnixMilli+1), nil
}
