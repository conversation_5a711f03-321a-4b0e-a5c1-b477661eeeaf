name: fluent-bit
services:
  fluent-bit:
    image: cr.fluentbit.io/fluent/fluent-bit:3.2.4
    restart: always
    container_name: fluent-bit
    network_mode: host
    volumes:
      - "/data/golang/deploy/gametester/app32601/logs/datatester.log:/data/logs/blockjuggle/app32601/datatester.log"
      - "/data/golang/deploy/gametester/app32602/logs/datatester.log:/data/logs/blockjuggle/app32602/datatester.log"
      - "/data/golang/deploy/blockpuzzle/app32801/logs/datatester.log:/data/logs/blockpuzzle/app32801/datatester.log"
      - "/data/golang/deploy/blockpuzzle/app32802/logs/datatester.log:/data/logs/blockpuzzle/app32802/datatester.log"
      - "/data/golang/deploy/journey/app32701/logs/datatester.log:/data/logs/journey/app32701/datatester.log"
      - "/data/golang/deploy/journey/app32702/logs/datatester.log:/data/logs/journey/app32702/datatester.log"
      - "./fluent-bit.conf:/fluent-bit/etc/conf/fluent-bit.conf"
      - "./parser.conf:/fluent-bit/etc/conf/custom_parsers.conf"
      - "./ca.crt:/fluent-bit/secrets/ca.crt:or"
      - "./fluent-bit-db:/fluent-bit/logs"
    environment:
      - FLUENT_BIT_LOG_LEVEL=debug
      - HOSTNAME=${HOSTNAME}
    command:
      - /fluent-bit/bin/fluent-bit
      - --workdir=/fluent-bit/etc
      - --config=/fluent-bit/etc/conf/fluent-bit.conf