[SERVICE]
    Daemon Off
    Flush        1
    HTTP_Server On
    HTTP_Listen 0.0.0.0
    HTTP_Port 2020
    Log_Level    ${FLUENT_BIT_LOG_LEVEL}
    Parsers_File /fluent-bit/etc/parsers.conf
    Parsers_File /fluent-bit/etc/conf/custom_parsers.conf

[INPUT]
    Name         tail
    Path         /data/logs/blockjuggle/app*/datatester.log
    Tag          gametester.blockjuggle.*
    Refresh_Interval 10
    Rotate_Wait  30
    DB           /fluent-bit/logs/fluent-bit.db
    Mem_Buf_Limit 5MB
    Skip_Long_Lines On

[INPUT]
    Name         tail
    Path         /data/logs/blockpuzzle/app*/datatester.log
    Tag          gametester.blockpuzzle.*
    Refresh_Interval 10
    Rotate_Wait  30
    DB           /fluent-bit/logs/fluent-bit.db
    Mem_Buf_Limit 5MB
    Skip_Long_Lines On

[INPUT]
    Name         tail
    Path         /data/logs/journey/app*/datatester.log
    Tag          gametester.journey.*
    Refresh_Interval 10
    Rotate_Wait  30
    DB           /fluent-bit/logs/fluent-bit.db
    <PERSON><PERSON>_Buf_Limit 5MB
    Skip_Long_Lines On


[FILTER]
    Name parser
    Match gametester.*
    Key_Name log
    Parser json

[FILTER]
    Name modify
    Match gametester.*
    Add hostname ${HOSTNAME}

[FILTER]
    Name modify
    Match gametester.blockjuggle.*
    Add bundleid com.block.juggle    

[FILTER]
    Name modify
    Match gametester.blockpuzzle.*
    Add bundleid com.blockpuzzle.us.ios    

[FILTER]
    Name modify
    Match gametester.journey.*
    Add bundleid com.hungrystudio.block.puzzle.fun.brain.free

[FILTER]
    Name nest
    Match gametester.*
    Operation lift
    Nested_under log
    Add_prefix lifted_

[OUTPUT]
    Name es
    Match gametester.blockjuggle.*
    Host ***********
    Port 9200
    HTTP_User fluent_user
    HTTP_Passwd svZbsmq4g6uoy6Y5JfZPe
    Logstash_Format On
    Suppress_Type_Name On
    Retry_Limit False
    Generate_ID On
    Logstash_Prefix fluentbit-app-log-gametester-blockjuggle
    tls On
    tls.verify On
    tls.ca_file /fluent-bit/secrets/ca.crt

[OUTPUT]
    Name es
    Match gametester.blockpuzzle.*
    Host ***********
    Port 9200
    HTTP_User fluent_user
    HTTP_Passwd svZbsmq4g6uoy6Y5JfZPe
    Logstash_Format On
    Suppress_Type_Name On
    Retry_Limit False
    Generate_ID On
    Logstash_Prefix fluentbit-app-log-gametester-blockpuzzle
    tls On
    tls.verify On
    tls.ca_file /fluent-bit/secrets/ca.crt

[OUTPUT]
    Name es
    Match gametester.journey.*
    Host ***********
    Port 9200
    HTTP_User fluent_user
    HTTP_Passwd svZbsmq4g6uoy6Y5JfZPe
    Logstash_Format On
    Suppress_Type_Name On
    Retry_Limit False
    Generate_ID On
    Logstash_Prefix fluentbit-app-log-gametester-journey
    tls On
    tls.verify On
    tls.ca_file /fluent-bit/secrets/ca.crt