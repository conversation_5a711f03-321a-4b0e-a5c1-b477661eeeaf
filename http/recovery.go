package http

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/config"
	hsGin "hungrystudio.com/core/gin"
	"hungrystudio.com/datatester/metrics"
	"net/http"
	"time"
)

type RecoveryServer struct {
	*hsGin.HttpServer
	redisClient cache.RedisClient
	sugared     *zap.SugaredLogger
}

const CacheKeyPrefix = "BBGIOS:%s"
const CacheExpiration = time.Hour * 24 * 3

func NewRecoveryServer(appModel string, httpConfig config.HttpServerConfig) *RecoveryServer {
	server := hsGin.NewHttpServer(appModel, httpConfig, metrics.GinMiddlewaresMetrics)

	rs := &RecoveryServer{
		HttpServer: server,
	}

	rs.AddRoutes(gin.RouteInfo{
		Method:      http.MethodGet,
		Path:        "/recovery",
		Handler:     "recovery",
		HandlerFunc: rs.recovery,
	})

	return rs
}

func (rs *RecoveryServer) RegisterRedisClient(r cache.RedisClient) {
	rs.redisClient = r
}

func (rs *RecoveryServer) RegisterSugaredLogger(s *zap.SugaredLogger) {
	rs.sugared = s
}

func (rs *RecoveryServer) recovery(c *gin.Context) {
	distinctId := c.Query("distinct_id")
	key := fmt.Sprintf(CacheKeyPrefix, distinctId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	if score, err := rs.redisClient.Get(ctx, key).Result(); err == nil {
		go func() {
			rs.redisClient.Expire(ctx, key, CacheExpiration)
		}()
		c.String(http.StatusOK, score)
	} else {
		c.String(http.StatusNotFound, "")
	}
}
