package http

import (
	"github.com/gin-gonic/gin"
	"hungrystudio.com/datatester/models"
	"io"
	"net/http"
)

func (t *DataTesterServer) csrRoutes(appModel string) {
	t.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "csr_config",
			HandlerFunc: t.getCSRConfig,
			Handler:     "csr-config",
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "csr_config_new",
			Handler:     "csr_config_new",
			HandlerFunc: t.getCSRConfigNew,
		},
	)

	if appModel == gin.DebugMode {
		t.AddRoutes(
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "transition_configs",
				HandlerFunc: t.getTransitions,
				Handler:     "transition_configs",
			})
	}
}

func (t *DataTesterServer) getCSRConfig(c *gin.Context) {
	//ip := utils.RemoteIp(c.Request)
	//sugared.Infof("IP:%s", ip)
	//if len(limitIps) > 0 && !slices.Contains(limitIps, ip) {
	//	result := InitResult{Code: "0", Message: "success", Data: map[string]any{
	//		"csr_num": models.CSRNumNo,
	//		"key":     "key",
	//		"csr_json": map[string]any{
	//			"aa": "aaa",
	//			"bb": "bb",
	//		},
	//	}}
	//	c.JSON(http.StatusOK, result)
	//	return
	//}
	initResult := InitResult{Code: "1", Message: "success", Data: map[string]string{
		"csr_num": models.CSRNumParseErr,
	}}

	defer c.Request.Body.Close()
	paramsContent, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("ReadAll Error: %v", err)
		c.JSON(http.StatusOK, initResult)
	}

	requestJson := trimSuffixNullByte(paramsContent)

	csrResponse := csrTraffic.GetConfig(requestJson)
	initResult.Data = csrResponse
	c.JSON(http.StatusOK, initResult)
}

func (t *DataTesterServer) getCSRConfigNew(c *gin.Context) {
	//ip := utils.RemoteIp(c.Request)
	//sugared.Infof("IP:%s", ip)
	//if len(limitIps) > 0 && !slices.Contains(limitIps, ip) {
	//	result := InitResult{Code: "0", Message: "success", Data: map[string]any{
	//		"csr_num": models.CSRNumNo,
	//		"key":     "key",
	//		"csr_json": map[string]any{
	//			"aa": "aaa",
	//			"bb": "bb",
	//		},
	//	}}
	//	c.JSON(http.StatusOK, result)
	//	return
	//}
	initResult := InitResult{Code: "1", Message: "success", Data: map[string]string{
		"csr_num": models.CSRNumParseErr,
	}}

	defer c.Request.Body.Close()
	paramsContent, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("ReadAll Error: %v", err)
		c.JSON(http.StatusOK, initResult)
	}

	requestJson := trimSuffixNullByte(paramsContent)

	csrResponse := csrTraffic.GetTransitionConfig(requestJson)
	initResult.Data = csrResponse
	sugared.Infof("RequestBody: %s,Result: %+v", string(requestJson), csrResponse)
	c.JSON(http.StatusOK, initResult)
}

func (t *DataTesterServer) getTransitions(c *gin.Context) {
	c.JSON(http.StatusOK, csrTraffic.GetTransitions())
}
