package http

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

func (t *DataTesterServer) lookupRoutes() {
	t.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/lookup/gametester/get_configs",
			Handler:     "loopup/gametester/get_configs",
			HandlerFunc: t.getGameTesterConfigs,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/lookup/gametester/bucketflow",
			Handler:     "loopup/gametester/bucketflow",
			HandlerFunc: t.getBucketFlow,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/lookup/gametester/flow",
			Handler:     "loopup/gametester/flow",
			HandlerFunc: t.getBundleFlow,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/lookup/gametester/get_configs_all",
			Handler:     "loopup/gametester/get_configs_all",
			HandlerFunc: t.getGameTesterConfigsNew,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/lookup/defend/get_configs",
			Handler:     "loopup/defend/get_configs",
			HandlerFunc: t.getDefendConfigs,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/lookup/gametester/monitor_bucket_flow",
			Handler:     "lookup/gametester/monitor_bucket_flow",
			HandlerFunc: t.monitorBucketFlow,
		},
	)
}

func (t *DataTesterServer) monitorBucketFlow(c *gin.Context) {
	bundleId := c.Query("bundleId")
	c.JSON(http.StatusOK, testerTraiffic.GetMonitorBucketFlow(bundleId))
}
