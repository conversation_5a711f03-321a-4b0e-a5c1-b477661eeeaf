package http

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"hungrystudio.com/core/config"
	hsGin "hungrystudio.com/core/gin"
	"hungrystudio.com/core/utils"
	"hungrystudio.com/datatester/global"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models"
)

type DataTesterServer struct {
	*hsGin.HttpServer
}

type InitResult struct {
	Code    string `json:"status"`
	Message string `json:"msg"`
	Data    any    `json:"data"`
}

func NewDataTesterServer(appModel, appName string, httpConfig config.HttpServerConfig) *DataTesterServer {
	server := hsGin.NewHttpServer(appModel, httpConfig, metrics.GinMiddlewaresMetrics)
	server.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"POST", "OPTIONS"},
		AllowHeaders:     []string{"Content-type"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	tServer := &DataTesterServer{
		server,
	}
	pprof.Register(server.GetEngine(), "/debug/pprof")

	sugared.Infof("Start AppName: %s", appName)
	switch appName {
	case global.AppNameBusiness:
		tServer.businessRoutes(appModel)
	case global.AppNameGameTester:
		tServer.gametesterRoutes(appModel)
	case global.AppNameDefend:
		tServer.defendRoutes(appModel)
	case global.AppNameCsr:
		tServer.csrRoutes(appModel)
	case global.AppNameLookup:
		tServer.lookupRoutes()
	default:
		tServer.initRoutes(appModel)
	}
	//tServer.initRoutes(appModel)

	return tServer
}

func (t *DataTesterServer) initRoutes(appModel string) {
	t.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/configs",
			HandlerFunc: t.getAllConfigs,
			Handler:     "configs",
		},
		//gin.RouteInfo{
		//	Method:      http.MethodPost,
		//	Path:        "csr_config",
		//	HandlerFunc: t.getCSRConfigTest,
		//	Handler:     "csr-config",
		//},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/sudo/dynamic_config",
			HandlerFunc: t.getSudoDynamicConfig,
			Handler:     "sudo/dynamic_config",
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/game_init",
			HandlerFunc: t.gameInit,
			Handler:     "game_init",
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/defend/init",
			HandlerFunc: t.defendInit,
			Handler:     "defend_init",
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/defend/get_config",
			Handler:     "defend_get_config",
			HandlerFunc: t.getDefendConfigs,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/gametester_configs",
			Handler:     "gametester_configs",
			HandlerFunc: t.getGameTesterConfigsWan,
		},
	)

	if appModel == gin.DebugMode {
		t.AddRoutes(
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/configs-test",
				Handler:     "configs-test",
				HandlerFunc: t.getAllConfigsTest,
			},
			//gin.RouteInfo{
			//	Method:      http.MethodPost,
			//	Path:        "/csr_config-test",
			//	Handler:     "csr_config-test",
			//	HandlerFunc: t.getCSRConfigTest,
			//},
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/sudo/dynamic_config-test",
				Handler:     "sudo/dynamic_config-test",
				HandlerFunc: t.getSudoDynamicConfigTest,
			},
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/game_init_test",
				HandlerFunc: t.gameInitTest,
				Handler:     "game_init_test",
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/game_tester_configs",
				Handler:     "game_tester_configs",
				HandlerFunc: t.getGameTesterConfigs,
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/game_tester_configs_new",
				Handler:     "game_tester_configs_new",
				HandlerFunc: t.getGameTesterConfigsNew,
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/now",
				Handler:     "/now",
				HandlerFunc: t.getNowTime,
			},
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/business/experiments_test",
				Handler:     "business/experiments_test",
				HandlerFunc: t.businessInitTest,
			},
		)
	}
}

func (t *DataTesterServer) decodeRequestBody(bodyRead io.ReadCloser) ([]byte, error) {
	defer bodyRead.Close()
	body, err := io.ReadAll(bodyRead)
	if err != nil {
		sugared.Errorf("decodeRequestBody io.ReadAll request.Body Error: %v", err)
		return nil, err
	}
	src := t.encode(string(body))
	decodeLen := base64.StdEncoding.DecodedLen(len(src))
	dst := make([]byte, decodeLen)
	n, err := base64.StdEncoding.Decode(dst, src)
	if err != nil {
		sugared.Errorf("base64.StdEncoding.Decode Error:%v", err)
		return nil, err
	}
	if n < decodeLen {
		dst = dst[:n]
	}
	return dst, nil
}

func (t *DataTesterServer) getAllConfigs(c *gin.Context) {
	initResult := InitResult{Code: "0", Message: "failed", Data: ""}

	paramsContent := strings.TrimSpace(c.PostForm("params"))
	if paramsContent == "" {
		sugared.Errorf("Arguments: params Not Setting")
		initResult.Message = "Arguments: params Not Setting"
		c.String(http.StatusOK, t.encodeResult(initResult, []byte(paramsContent)))
		return
	}
	sugared.Infof("Params: %s", paramsContent)
	src := t.encode(paramsContent)
	dst := make([]byte, base64.URLEncoding.DecodedLen(len(src)))
	params := make(map[string]any)
	_, err := base64.URLEncoding.Decode(dst, src)
	if err != nil {
		sugared.Errorf("base64.StdEncoding.Decode Error: %v", err)
		initResult.Message = "param err"
		c.String(http.StatusOK, t.encodeResult(initResult, []byte(paramsContent)))
		return
	}
	// 去除结尾空字符
	requestJson := trimSuffixNullByte(dst)
	//sugared.Infof("Params: %s", string(dst[:i+1]))
	err = json.Unmarshal(requestJson, &params)
	if err != nil {
		sugared.Errorf("json.Unmarshal: %s, Error: %v", string(dst), err)
		initResult.Message = "Arguments: params Parse Error"
		c.String(http.StatusOK, t.encodeResult(initResult, requestJson))
		return
	}
	sugared.Infof("Params Map: %v", params)
	ip := utils.RemoteIp(c.Request)
	country, err := ipData.GetCountry(ip)
	if err != nil {
		country = "other"
	}
	params[models.ParamNameServerCountryCode] = country
	params[models.ParamNameServerIP] = ip
	bundleId := params["bundleId"].(string)
	//sugared.Infof("GetTraffice By Bundle Id: %s", bundleId)
	if traffic := traiffics.GetTraffic(bundleId); traffic != nil {
		//sugared.Infof("Get Traffic %v", traffic)
		initResult.Data = traffic.GetTrafficConfig(params)
		initResult.Code = "1"
		initResult.Message = "success"
	} else {
		initResult.Message = fmt.Sprintf("bundle id: %s not exist", bundleId)
	}
	c.String(http.StatusOK, t.encodeResult(initResult, requestJson))
}

var EncodeKey = []byte("C1eWgtN/ZOJ=qw2TXyhxjV+0SlUL35R6ri9G4uamPfQpK78AdHbBczFnYEskMDIvo")
var EncodeKeyMaxIndex = len(EncodeKey) - 1
var EncodeKeyIndexes = make(map[byte]int)

func init() {
	for i := 0; i <= EncodeKeyMaxIndex; i++ {
		EncodeKeyIndexes[EncodeKey[i]] = i
	}
}
func (t *DataTesterServer) encode(param string) []byte {
	strings.TrimSpace(param)
	param = strings.Replace(param, " ", "+", -1)
	paramLength := len(param)
	encodeBytes := make([]byte, 0, paramLength)
	for i := 0; i < paramLength; i++ {
		s := param[i]
		if index, ok := EncodeKeyIndexes[s]; ok {
			t := EncodeKey[EncodeKeyMaxIndex-index]
			encodeBytes = append(encodeBytes, t)
		}
	}
	return encodeBytes
}

func (t *DataTesterServer) encodeResult(result InitResult, request []byte) string {
	resultJson, err := json.Marshal(result)
	if err != nil {
		sugared.Errorf("json.Marshal Error: %v", err)
		return ""
	}
	sugared.Infof("RequestJson:%s,ResultJson:%s", string(request), resultJson)
	dst := make([]byte, base64.StdEncoding.EncodedLen(len(resultJson)))
	base64.StdEncoding.Encode(dst, resultJson)
	return string(t.encode(string(dst)))
}

// trimSuffixNull 去除结尾空字符
func trimSuffixNullByte(src []byte) []byte {
	i := len(src) - 1
	for i >= 0 {
		if src[i] == 0 {
			i--
		} else {
			break
		}
	}
	return src[:i+1]
}

func (t *DataTesterServer) getAllConfigsTest(c *gin.Context) {
	initResult := InitResult{Code: "0", Message: "failed", Data: ""}
	defer c.Request.Body.Close()
	jsonDecoder := json.NewDecoder(c.Request.Body)
	params := make(map[string]any)
	err := jsonDecoder.Decode(&params)
	if err != nil {
		sugared.Errorf("jsonDecoder.Decode Error: %v", err)
		initResult.Message = err.Error()
		c.JSON(http.StatusOK, initResult)
		return
	}
	ip := utils.RemoteIp(c.Request)
	country, err := ipData.GetCountry(ip)
	if err != nil {
		country = "other"
	}
	params[models.ParamNameServerCountryCode] = country
	params[models.ParamNameServerIP] = ip
	bundleId := params["bundleId"].(string)
	//sugared.Infof("GetTraffice By Bundle Id: %s", bundleId)
	if traffic := traiffics.GetTraffic(bundleId); traffic != nil {
		//sugared.Infof("Get Traffic %v", traffic)
		initResult.Data = traffic.GetTrafficConfig(params)
		initResult.Code = "1"
		initResult.Message = "success"
	} else {
		initResult.Message = fmt.Sprintf("bundle id: %s not exist", bundleId)
	}
	c.JSON(http.StatusOK, initResult)
}

//func (t *DataTesterServer) getCSRConfig(c *gin.Context) {
//	ip := utils.RemoteIp(c.Request)
//	if len(limitIps) > 0 && !slices.Contains(limitIps, ip) {
//		result := InitResult{Code: "0", Message: "success", Data: map[string]any{
//			"csr_num": models.CSRNumNo,
//			"key":     "key",
//			"csr_json": map[string]any{
//				"aa": "aaa",
//				"bb": "bb",
//			},
//		}}
//		c.String(http.StatusOK, t.encodeResult(result, []byte{}))
//		return
//	}
//
//	// 暂时注释只返回以上测试数据
//	initResult := InitResult{Code: "1", Message: "success", Data: map[string]string{
//		"csr_num": models.CSRNumParseErr,
//	}}
//	paramsContent := strings.TrimSpace(c.PostForm("params"))
//	if paramsContent == "" {
//		sugared.Errorf("Arguments: params Not Setting")
//		initResult.Message = "Arguments: params Not Setting"
//		c.String(http.StatusOK, t.encodeResult(initResult, []byte(paramsContent)))
//		return
//	}
//	sugared.Infof("Params: %s", paramsContent)
//	src := t.encode(paramsContent)
//	dst := make([]byte, base64.URLEncoding.DecodedLen(len(src)))
//	_, err := base64.URLEncoding.Decode(dst, src)
//	if err != nil {
//		sugared.Errorf("base64.StdEncoding.Decode Error: %v", err)
//		initResult.Message = "param err"
//		c.String(http.StatusOK, t.encodeResult(initResult, []byte(paramsContent)))
//		return
//	}
//	// 去除结尾空字符
//	requestJson := trimSuffixNullByte(dst)
//
//	csrResponse := csrTraffic.GetConfig(requestJson)
//	initResult.Data = csrResponse
//	c.String(http.StatusOK, t.encodeResult(initResult, requestJson))
//}

func (t *DataTesterServer) getSudoDynamicConfig(c *gin.Context) {

	initResult := InitResult{Code: "0", Message: "failed", Data: ""}

	paramsContent := strings.TrimSpace(c.PostForm("params"))
	if paramsContent == "" {
		sugared.Errorf("Arguments: params Not Setting")
		initResult.Message = "Arguments: params Not Setting"
		c.String(http.StatusOK, t.encodeResult(initResult, []byte(paramsContent)))
		return
	}
	sugared.Infof("Params: %s", paramsContent)
	src := t.encode(paramsContent)
	dst := make([]byte, base64.URLEncoding.DecodedLen(len(src)))
	params := make(map[string]any)
	_, err := base64.URLEncoding.Decode(dst, src)
	if err != nil {
		sugared.Errorf("base64.StdEncoding.Decode Error: %v", err)
		initResult.Message = "param err"
		c.String(http.StatusOK, t.encodeResult(initResult, []byte(paramsContent)))
		return
	}
	// 去除结尾空字符
	requestJson := trimSuffixNullByte(dst)
	err = json.Unmarshal(requestJson, &params)
	if err != nil {
		sugared.Errorf("json.Unmarshal: %s, Error: %v", string(dst), err)
		initResult.Message = "Arguments: params Parse Error"
		c.String(http.StatusOK, t.encodeResult(initResult, requestJson))
		return
	}
	sugared.Infof("Params Map: %v", params)
	ip := utils.RemoteIp(c.Request)
	country, err := ipData.GetCountry(ip)
	if err != nil {
		country = "other"
	}
	params[models.ParamNameServerCountryCode] = country
	params[models.ParamNameServerIP] = ip
	bundleId := params["bundleId"].(string)
	groupDynamic := fmt.Sprintf("%s.%s", "dynamic", bundleId)
	if traffic := traiffics.GetTraffic(groupDynamic); traffic != nil {
		initResult.Data = traffic.GetTrafficConfig(params)
		initResult.Code = "1"
		initResult.Message = "success"
	} else {
		initResult.Message = fmt.Sprintf("bundle id: %s not exist", bundleId)
	}
	c.String(http.StatusOK, t.encodeResult(initResult, requestJson))
}

func (t *DataTesterServer) getSudoDynamicConfigTest(c *gin.Context) {
	initResult := InitResult{Code: "0", Message: "failed", Data: ""}
	defer c.Request.Body.Close()
	jsonDecoder := json.NewDecoder(c.Request.Body)
	params := make(map[string]any)
	err := jsonDecoder.Decode(&params)
	if err != nil {
		sugared.Errorf("jsonDecoder.Decode Error: %v", err)
		initResult.Message = err.Error()
		c.JSON(http.StatusOK, initResult)
		return
	}
	ip := utils.RemoteIp(c.Request)
	country, err := ipData.GetCountry(ip)
	if err != nil {
		country = "other"
	}
	params[models.ParamNameServerCountryCode] = country
	params[models.ParamNameServerIP] = ip
	bundleId := params["bundleId"].(string)
	groupDynamic := fmt.Sprintf("%s.%s", "dynamic", bundleId)
	if traffic := traiffics.GetTraffic(groupDynamic); traffic != nil {
		initResult.Data = traffic.GetTrafficConfig(params)
		initResult.Code = "1"
		initResult.Message = "success"
	} else {
		initResult.Message = fmt.Sprintf("bundle id: %s not exist", bundleId)
	}

	c.JSON(http.StatusOK, initResult)
}

func (t *DataTesterServer) getNowTime(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"time": time.Now(),
	})
}
