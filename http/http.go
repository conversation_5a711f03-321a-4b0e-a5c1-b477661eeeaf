package http

import (
	"go.uber.org/zap"
	"hungrystudio.com/datatester/models"
)

var sugared *zap.SugaredLogger

func SetSugaredLogger(s *zap.SugaredLogger) {
	sugared = s
}

var ipData *models.IPData

func SetIPData(ip *models.IPData) {
	ipData = ip
}

var appName string

func SetAppName(name string) {
	appName = name
}

var traiffics *models.Traffics

func SetTraiffics(traf *models.Traffics) {
	traiffics = traf
}

var csrTraffic *models.CSRTraffic

func SetCSRTraffic(csr *models.CSRTraffic) {
	csrTraffic = csr
}

var limitIps []string

func SetLimitIps(ips []string) {
	limitIps = ips
}

var testerTraiffic *models.TesterTraffic

func SetTesterTraiffic(tester *models.TesterTraffic) {
	testerTraiffic = tester
}

var defend *models.DefendConfigs

func SetDefendConfigs(configs *models.DefendConfigs) {
	defend = configs
}

//var gameTesterDBEngine xorm.EngineInterface
//
//func SetGameTesterDBEngine(db xorm.EngineInterface) {
//	gameTesterDBEngine = db
//}

var bsns *models.Business

func SetBusiness(b *models.Business) {
	bsns = b
}
