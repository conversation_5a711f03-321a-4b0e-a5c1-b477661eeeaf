package http

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"hungrystudio.com/datatester/models/testergame"
	"net/http"
)

func (admin *GameTesterAdmin) initMultiLinkRoutes() {
	multiLinkRoutes := gin.RoutesInfo{
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/upload-link/:bundleId/:env",
			Handler:     "admin/multilink/upload-link",
			HandlerFunc: admin.multiLinkUploadLink,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/upload-links/:bundleId/:env",
			Handler:     "admin/multilink/upload-links",
			HandlerFunc: admin.multiLinkUploadLinks,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/upload-params/:bundleId/:env",
			Handler:     "admin/multilink/upload-params",
			HandlerFunc: admin.multiLinkUploadParams,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/upload-white/:bundleId/:env",
			Handler:     "admin/multilink/upload-white",
			HandlerFunc: admin.multiLinkUploadWhite,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/delete-white/:bundleId/:env",
			Handler:     "admin/multilink/delete-white",
			HandlerFunc: admin.multiLinkDeleteWhite,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/switch-link/:bundleId/:env",
			Handler:     "admin/multilink/switch-link",
			HandlerFunc: admin.multiLinkSwitchLink,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/switch-params/:bundleId/:env",
			Handler:     "admin/multilink/switch-params",
			HandlerFunc: admin.multiLinkSwitchParams,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/down-link/:bundleId/:env",
			Handler:     "admin/multilink/down-link",
			HandlerFunc: admin.multiLinkDownLink,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/down-global-links/:bundleId/:env",
			Handler:     "admin/multilink/down-global-links",
			HandlerFunc: admin.multiLinkDownGlobalLink,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/down-link-type/:bundleId/:env/:linkType",
			Handler:     "multilink/down-link-type",
			HandlerFunc: admin.DownLinkType,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/publish/:bundleId/:env",
			Handler:     "admin/multilink/publish",
			HandlerFunc: admin.multiLinkPublish,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/get/:bundleId/:env",
			Handler:     "admin/multilink/get",
			HandlerFunc: admin.multiLinkGet,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/get-published/:bundleId/:env",
			Handler:     "admin/multilink/get-published",
			HandlerFunc: admin.multiLinkGetPulished,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/sync/:bundleId/:env",
			Handler:     "admin/multilink/sync",
			HandlerFunc: admin.multiLinkSync,
		},
	}
	admin.AddGroupRoutes("/admin/multilink", multiLinkRoutes, admin.multiLinkCheckBundleEnv)
}

func (admin *GameTesterAdmin) multiLinkCheckBundleEnv(c *gin.Context) {
	bundleId := c.Param("bundleId")
	env := c.Param("env")
	if bundleId == "" || env == "" {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": "缺少bundleId和env参数",
		})
		return
	}

	if _, ok := admin.linksData[bundleId][env]; !ok {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": fmt.Sprintf("MultiLink Not Support Bundle:%s, Env:%s", bundleId, env),
		})
	}
}

func (admin *GameTesterAdmin) multiLinkUploadLink(c *gin.Context) {
	linksParams := make(map[string]testergame.LinkParams)
	err := c.BindJSON(&linksParams)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
		return
	}
	err = admin.linksData[c.Param("bundleId")][c.Param("env")].UploadLink(linksParams)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
		})
	}
}

func (admin *GameTesterAdmin) multiLinkUploadLinks(c *gin.Context) {
	linksParams := make([]testergame.UploadLinksRequest, 0)
	err := c.BindJSON(&linksParams)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
		return
	}
	result := admin.linksData[c.Param("bundleId")][c.Param("env")].UploadLinks(linksParams)
	if result == nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": "no updated",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
			"data":    result,
		})
	}
}

func (admin *GameTesterAdmin) multiLinkUploadParams(c *gin.Context) {
	linksParams := testergame.LinkParams{}
	err := c.BindJSON(&linksParams)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		if len(linksParams.CustomParams) == 0 {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"code":    0,
				"message": "no params",
			})
			return
		}
		err = admin.linksData[c.Param("bundleId")][c.Param("env")].UploadGlobalParams(linksParams)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"code":    0,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
		})
	}
}

func (admin *GameTesterAdmin) multiLinkUploadWhite(c *gin.Context) {
	whiteList := make([]string, 0)
	err := c.BindJSON(&whiteList)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		admin.linksData[c.Param("bundleId")][c.Param("env")].AddWhite(whiteList)
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
		})
	}
}

func (admin *GameTesterAdmin) multiLinkDeleteWhite(c *gin.Context) {
	whiteList := make([]string, 0)
	err := c.BindJSON(&whiteList)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		admin.linksData[c.Param("bundleId")][c.Param("env")].RemoveWhite(whiteList)
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
		})
	}
}

func (admin *GameTesterAdmin) multiLinkSwitchLink(c *gin.Context) {
	exps := make([]testergame.SwitchLinksRequest, 0)
	err := c.BindJSON(&exps)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		result := admin.linksData[c.Param("bundleId")][c.Param("env")].SwitchLinks(exps)
		if result == nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"code":    0,
				"message": "no link switched",
			})
		} else {
			c.JSON(http.StatusOK, gin.H{
				"code":    1,
				"message": "success",
				"data":    result,
			})
		}
	}
}

func (admin *GameTesterAdmin) multiLinkSwitchParams(c *gin.Context) {
	err := admin.linksData[c.Param("bundleId")][c.Param("env")].SwitchGlobalParams()
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
		})
	}
}

func (admin *GameTesterAdmin) multiLinkDownLink(c *gin.Context) {
	exps := make([]string, 0)
	err := c.BindJSON(&exps)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
	} else {
		result, err := admin.linksData[c.Param("bundleId")][c.Param("env")].DownLink(exps)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"code":    0,
				"message": err.Error(),
			})
		} else {
			c.JSON(http.StatusOK, gin.H{
				"code":    1,
				"message": "success",
				"data":    result,
			})
		}
	}
}

// DownLinkType 处理根据链接类型下架链接的HTTP请求
// 该函数接收一个gin.Context对象，从请求中解析出链接列表和链接类型，
// 并调用linksData中的DownLinks方法来执行下架操作。
// 如果解析失败或链接列表为空，将返回错误响应。
// 如果下架操作成功，将返回包含结果的成功响应。
func (admin *GameTesterAdmin) DownLinkType(c *gin.Context) {
	// 初始化一个空的字符串切片，用于存储从请求中解析出的链接列表
	links := make([]string, 0)
	// 尝试从请求的JSON体中解析出链接列表
	err := c.BindJSON(&links)
	// 如果解析过程中出现错误
	if err != nil {
		// 返回HTTP 500状态码和错误信息
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
		return
	}
	// 检查解析出的链接列表是否为空
	if len(links) == 0 {
		// 如果为空，返回HTTP 500状态码和错误信息
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": "links is nil",
		})
		return
	}
	// 从请求的URL参数中获取链接类型
	linkType := c.Param("linkType")
	// 检查链接类型是否为空
	if linkType == "" {
		// 如果为空，返回HTTP 500状态码和错误信息
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": "linkType required",
		})
		return
	}
	// 调用linksData中的DownLinks方法，根据链接类型和链接列表执行下架操作
	result := admin.linksData[c.Param("bundleId")][c.Param("env")].DownLinks(linkType, links)
	// 检查下架操作的结果是否为nil
	if result == nil {
		// 如果为nil，返回HTTP 500状态码和错误信息
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": fmt.Sprintf("links:%v no down", links),
		})
	} else {
		// 如果操作成功，返回HTTP 200状态码和包含结果的成功信息
		c.JSON(http.StatusOK, gin.H{
			"code":    1,
			"message": "success",
			"data":    result,
		})
	}
}

func (admin *GameTesterAdmin) multiLinkPublish(c *gin.Context) {
	isForce := false
	if c.Query("is_force") == "1" {
		isForce = true
	}
	err := admin.linksData[c.Param("bundleId")][c.Param("env")].Publish(isForce)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": err.Error(),
		})
		return
	}
	if c.Param("env") == testergame.EnvProd {
		if grayLinksData, ok := admin.linksData[c.Param("bundleId")][testergame.EnvGray]; ok {
			err = grayLinksData.Sync(admin.linksData[c.Param("bundleId")][c.Param("env")])
			if err != nil {
				c.JSON(http.StatusOK, gin.H{
					"code":    1,
					"message": "同步到灰度服错误",
				})
				return
			}
		} else {
			c.JSON(http.StatusOK, gin.H{
				"code":    1,
				"message": "灰度服不存在",
			})
			return
		}

	}
	c.JSON(http.StatusOK, gin.H{
		"code":    1,
		"message": "success",
	})
}
func (admin *GameTesterAdmin) multiLinkGet(c *gin.Context) {
	c.JSON(http.StatusOK, admin.linksData[c.Param("bundleId")][c.Param("env")])
}

func (admin *GameTesterAdmin) multiLinkGetPulished(c *gin.Context) {
	c.JSON(http.StatusOK, admin.linksData[c.Param("bundleId")][c.Param("env")].GetPulished())
}

func (admin *GameTesterAdmin) multiLinkDownGlobalLink(c *gin.Context) {
	admin.linksData[c.Param("bundleId")][c.Param("env")].DownGlobalWhiteLink()
	c.JSON(http.StatusOK, gin.H{
		"code":    1,
		"message": "success",
	})
}

func (admin *GameTesterAdmin) multiLinkSync(c *gin.Context) {
	fromEnv := c.Query("from")
	if fromEnv == "" {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    0,
			"message": "from env required",
		})
		return
	}
	if fromLinksData, ok := admin.linksData[c.Param("bundleId")][fromEnv]; ok {
		pushlishedFromLinksData := fromLinksData.GetPulished()
		if err := admin.linksData[c.Param("bundleId")][c.Param("env")].Sync(pushlishedFromLinksData); err == nil {
			c.JSON(http.StatusOK, gin.H{
				"code":    1,
				"message": "success",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    0,
				"message": err.Error(),
			})
		}
	}
}
