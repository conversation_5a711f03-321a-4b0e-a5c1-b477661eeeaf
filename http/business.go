package http

import (
	"encoding/base64"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"hungrystudio.com/core/utils"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/business"
	"io"
	"net/http"
	"slices"
)

func (t *DataTesterServer) businessRoutes(appModel string) {
	t.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/business/init",
			Handler:     "business/init",
			HandlerFunc: t.businessInit,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/business/init.v2",
			Handler:     "business/init.v2",
			HandlerFunc: t.businessInitV2,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/business/get_config",
			Handler:     "business/get_config",
			HandlerFunc: t.businessConfigs,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/business/get_whiteconfig",
			Handler:     "business/get_whiteconfig",
			HandlerFunc: t.businessWhiteConfigs,
		},
	)
	if appModel == gin.DebugMode {
		t.AddRoutes(
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/business/init_test",
				Handler:     "business/init_test",
				HandlerFunc: t.businessInitTest,
			},
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/business/addqa",
				Handler:     "business/addqa",
				HandlerFunc: t.addBusinessQa,
			},
		)
	}
}

func (t *DataTesterServer) businessConfigs(c *gin.Context) {
	ip := utils.RemoteIp(c.Request)
	if !slices.Contains(limitIps, ip) {
		c.JSON(http.StatusOK, nil)
		return
	}
	c.JSON(http.StatusOK, bsns.GetServerConfigs())
}

func (t *DataTesterServer) businessWhiteConfigs(c *gin.Context) {
	ip := utils.RemoteIp(c.Request)
	if !slices.Contains(limitIps, ip) {
		c.JSON(http.StatusOK, nil)
		return
	}
	c.JSON(http.StatusOK, bsns.GetServerWhiteConfigs())
}

type BusinessResult struct {
	Code    string                    `json:"status"`
	Message string                    `json:"message"`
	Data    map[string]map[string]any `json:"data"`
}

func (t *DataTesterServer) businessInit(c *gin.Context) {
	body, err := t.decodeRequestBody(c.Request.Body)
	if err != nil {
		c.String(http.StatusOK, t.encodeBusinessResult(BusinessResult{Code: "1", Message: err.Error()}))
		return
	}
	bReq := business.Request{}
	err = json.Unmarshal(body, &bReq)
	if err != nil {
		sugared.Errorf("unmarshal business init request: %s, err: %v", string(body), err)
		c.String(http.StatusOK, t.encodeBusinessResult(BusinessResult{Code: "1", Message: err.Error()}))
		return
	}
	ip := utils.RemoteIp(c.Request)
	countryCode, err := ipData.GetCountry(ip)
	if err != nil {
		sugared.Errorf("get country code: %s, err: %v", ip, err)
		countryCode = models.CountryCodeDefault
	}
	bReq.CountryCode = countryCode
	result, err := bsns.Init(bReq)
	if err != nil {
		sugared.Errorf("init business result: %s, err: %v", string(body), err)
		c.String(http.StatusOK, t.encodeBusinessResult(BusinessResult{Code: "1", Message: err.Error()}))
		return
	}
	resultContent := t.encodeBusinessResult(BusinessResult{Code: "0", Message: "success", Data: result})
	sugared.Infof("Request: %s, ResultOrigin: %+v,Result: %s", string(body), result, resultContent)
	c.String(http.StatusOK, resultContent)
}

func (t *DataTesterServer) businessInitV2(c *gin.Context) {
	body, err := t.decodeRequestBody(c.Request.Body)
	if err != nil {
		c.String(http.StatusOK, t.encodeBusinessResult(BusinessResult{Code: "1", Message: err.Error()}))
		return
	}
	bReq := business.Request{}
	err = json.Unmarshal(body, &bReq)
	if err != nil {
		sugared.Errorf("unmarshal business init request: %s, err: %v", string(body), err)
		c.String(http.StatusOK, t.encodeBusinessResult(BusinessResult{Code: "1", Message: err.Error()}))
		return
	}
	ip := utils.RemoteIp(c.Request)
	countryCode, err := ipData.GetCountry(ip)
	if err != nil {
		sugared.Errorf("get country code: %s, err: %v", ip, err)
		countryCode = models.CountryCodeDefault
	}
	bReq.CountryCode = countryCode
	result, err := bsns.InitV2(bReq)
	if err != nil {
		sugared.Errorf("init business result: %s, err: %v", string(body), err)
		c.String(http.StatusOK, t.encodeBusinessResult(BusinessResult{Code: "1", Message: err.Error()}))
		return
	}
	resultContent := t.encodeBusinessResult(BusinessResult{Code: "0", Message: "success", Data: result})
	sugared.Infof("Request: %s, ResultOrigin: %+v,Result: %s", string(body), result, resultContent)
	c.String(http.StatusOK, resultContent)
}

func (t *DataTesterServer) businessInitTest(c *gin.Context) {
	defer c.Request.Body.Close()
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusOK, BusinessResult{Code: "0", Message: err.Error()})
		return
	}
	bReq := business.Request{}
	err = json.Unmarshal(body, &bReq)
	if err != nil {
		sugared.Errorf("unmarshal business init request: %s, err: %v", string(body), err)
		c.JSON(http.StatusOK, BusinessResult{Code: "1", Message: err.Error()})
		return
	}
	ip := utils.RemoteIp(c.Request)
	countryCode, err := ipData.GetCountry(ip)
	if err != nil {
		sugared.Errorf("get country code: %s, err: %v", ip, err)
		countryCode = models.CountryCodeDefault
	}
	bReq.CountryCode = countryCode
	//c.JSON(http.StatusOK, gin.H{"Code": 0, "Message": "success", "Data": bReq})
	//return
	result, err := bsns.Init(bReq)
	if err != nil {
		sugared.Errorf("init business result: %s, err: %v", string(body), err)
		c.JSON(http.StatusOK, BusinessResult{Code: "1", Message: err.Error()})
		return
	}

	sugared.Infof("Request: %v, Result:%v", bReq, result)
	c.JSON(http.StatusOK, BusinessResult{
		Code:    "0",
		Message: "success",
		Data:    result,
	})
}

func (t *DataTesterServer) addBusinessQa(c *gin.Context) {
	defer c.Request.Body.Close()
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusOK, BusinessResult{Code: "1", Message: err.Error()})
		return
	}
	err = bsns.AddQa(body)
	if err != nil {
		c.JSON(http.StatusOK, BusinessResult{Code: "1", Message: err.Error()})
	} else {
		c.JSON(http.StatusOK, BusinessResult{Code: "0", Message: "success"})
	}
}

func (t *DataTesterServer) encodeBusinessResult(result BusinessResult) string {
	jsonData, err := json.Marshal(result)
	if err != nil {
		sugared.Errorf("businessExperiments json.Marshal Error: %v", err)
		result.Code = "1"
		result.Message = err.Error()
		result.Data = nil
		jsonData, _ = json.Marshal(result)
	}
	dst := make([]byte, base64.StdEncoding.EncodedLen(len(jsonData)))
	base64.StdEncoding.Encode(dst, jsonData)
	return string(t.encode(string(dst)))
}
