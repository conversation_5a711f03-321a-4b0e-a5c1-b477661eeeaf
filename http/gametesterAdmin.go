package http

import (
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"slices"
	"strconv"

	"github.com/gin-gonic/gin"
	"hungrystudio.com/core/config"
	hsGin "hungrystudio.com/core/gin"
	"hungrystudio.com/core/utils"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/testergame"
)

type GameTesterAdmin struct {
	*hsGin.HttpServer
	//localRedis cache.RedisClient
	//grayRedis  cache.RedisClient
	//prodRedis  cache.RedisClient
	limitIPs        []string
	gametesterAdmin *models.GametesterAdmin
	linksData       map[string]map[string]*testergame.LinksData
}

func NewGameTesterAdmin(appModel string, httpConfig config.HttpServerConfig, middleware ...gin.HandlerFunc) *GameTesterAdmin {
	httpServer := hsGin.NewHttpServer(appModel, httpConfig, middleware...)
	admin := &GameTesterAdmin{
		HttpServer: httpServer,
	}
	admin.Use(admin.limitIPMiddleware)
	admin.initRoutes()
	admin.initLookupGroupRoutes()
	admin.initMultiLinkRoutes()
	return admin
}

//func (admin *GameTesterAdmin) SetLocalRedis(localRedis cache.RedisClient) {
//	admin.localRedis = localRedis
//}
//func (admin *GameTesterAdmin) SetGrayRedis(grayRedis cache.RedisClient) {
//	admin.grayRedis = grayRedis
//}
//func (admin *GameTesterAdmin) SetProdRedis(prodRedis cache.RedisClient) {
//	admin.prodRedis = prodRedis
//}

func (admin *GameTesterAdmin) limitIPMiddleware(c *gin.Context) {
	ip := utils.RemoteIp(c.Request)
	nIP := net.ParseIP(ip)
	if !nIP.IsPrivate() {
		if len(admin.limitIPs) > 0 {
			if !slices.Contains(admin.limitIPs, ip) {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": fmt.Sprintf("ip %s not allowed", ip),
				})
				c.Abort()
			}
		}
	}
}

func (admin *GameTesterAdmin) SetLimitIPs(limitIPs []string) {
	admin.limitIPs = limitIPs
}

func (admin *GameTesterAdmin) SetGametesterAdmin(gameAdmin *models.GametesterAdmin) {
	admin.gametesterAdmin = gameAdmin
}

func (admin *GameTesterAdmin) SetLinksData(linksData map[string]map[string]*testergame.LinksData) {
	admin.linksData = linksData
}

func (admin *GameTesterAdmin) initRoutes() {

	admin.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/admin/gametester/save",
			Handler:     "admin/gametester/save",
			HandlerFunc: admin.save,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/admin/gametester/push-gray",
			Handler:     "admin/gametester/push-gray",
			HandlerFunc: admin.pushGray,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/admin/gametester/set-bucket-flow",
			Handler:     "admin/gametester/set-bucket-flow",
			HandlerFunc: admin.setBucketFlow,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/admin/gametester/add-white-uid",
			Handler:     "admin/gametester/add-white-uid",
			HandlerFunc: admin.addWhiteUid,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/admin/gametester/set-user",
			Handler:     "admin/gametester/set-user",
			HandlerFunc: admin.setUserDistribute,
		},
	)
}

func (admin *GameTesterAdmin) initLookupGroupRoutes() {
	lookupGroup := gin.RoutesInfo{
		{
			Method:      http.MethodGet,
			Path:        "/user-info",
			Handler:     "/user-info",
			HandlerFunc: admin.getUserInfo,
		},
		{
			Method:      http.MethodGet,
			Path:        "/bucket-flow",
			Handler:     "/bucket-flow",
			HandlerFunc: admin.getBucketFlow,
		},
		{
			Method:      http.MethodGet,
			Path:        "/exps-state",
			Handler:     "/exps-state",
			HandlerFunc: admin.getExpsState,
		},
		{
			Method:      http.MethodGet,
			Path:        "/bucket-config",
			Handler:     "/bucket-config",
			HandlerFunc: admin.getBucketConfigs,
		},
		{
			Method:      http.MethodGet,
			Path:        "/white-uid",
			Handler:     "/white-uid",
			HandlerFunc: admin.getWhiteUid,
		},
		{
			Method:      http.MethodPost,
			Path:        "/server-decrypt/:bundleId/:env",
			Handler:     "/server-decrypt",
			HandlerFunc: admin.serverDecrypt,
		},
	}
	admin.AddGroupRoutes("/lookup", lookupGroup)
}

func (admin *GameTesterAdmin) addWhiteUid(c *gin.Context) {
	defer c.Request.Body.Close()
	uid := c.Query("uid")
	env := c.Query("env")
	if uid == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "uid is required"})
		return
	}
	admin.gametesterAdmin.AddWhiteUid(uid, env)
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

func (admin *GameTesterAdmin) getWhiteUid(c *gin.Context) {
	defer c.Request.Body.Close()
	env := c.Query("env")
	c.JSON(http.StatusOK, admin.gametesterAdmin.GetWhiteUid(env))
}

func (admin *GameTesterAdmin) save(c *gin.Context) {
	defer c.Request.Body.Close()
	bundleId := c.Query("bundle_id")
	env := c.Query("env")
	if env == testergame.EnvProd {
		c.JSON(http.StatusBadRequest, gin.H{"error": "生产环境配置不能直接上传"})
		return
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	bundleConfigs := make([]testergame.BucketConfig, 0)
	err = json.Unmarshal(body, &bundleConfigs)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err = admin.gametesterAdmin.SaveBucketConfigs(bundleConfigs, bundleId, env)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	// 上传重构2.0
	//if bundleId == "com.block.juggle" {
	//	err = admin.gametesterAdmin.UploadToRefactor2(body, env)
	//	if err != nil {
	//		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("长传重构2.0失败:%s", err.Error())})
	//		return
	//	}
	//}
	//if env == testergame.EnvGray {
	//	if validResult, ok := admin.gametesterAdmin.ValidateBucketConfigs(bundleId, bundleConfigs); !ok {
	//		c.JSON(http.StatusOK, gin.H{"status": "ok", "validResults": validResult})
	//		return
	//	}
	//}
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

func (admin *GameTesterAdmin) pushGray(c *gin.Context) {
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "bundle_id is required",
		})
		return
	}
	if err := admin.gametesterAdmin.PushGray(bundleId); err == nil {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
	}
}

func (admin *GameTesterAdmin) getExpsState(c *gin.Context) {
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bundle_id is required"})
		return
	}
	isPush := c.Query("is_push") == "1"
	c.JSON(http.StatusOK, admin.gametesterAdmin.GetExpsStateFromCache(bundleId, isPush))
}

func (admin *GameTesterAdmin) getUserInfo(c *gin.Context) {
	bundleId := c.Query("bundle_id")
	uid := c.Query("uid")
	if bundleId == "" || uid == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bundle_id or uid is required"})
		return
	}
	env := c.Query("env")
	if env == "" {
		env = testergame.EnvProd
	}
	c.JSON(http.StatusOK, admin.gametesterAdmin.GetUserInfo(bundleId, uid, env))
}

func (admin *GameTesterAdmin) getBucketFlow(c *gin.Context) {
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bundle_id is required"})
		c.Abort()
	}
	bucket := c.Query("bucket")
	if bucket == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bucket is required"})
		c.Abort()
	}
	env := c.Query("env")
	if env == "" {
		env = testergame.EnvTesting
	}
	result := admin.gametesterAdmin.GetBucketFlow(bundleId, env, bucket)
	result["bundle"] = bundleId
	result["bucket"] = bucket
	c.JSON(http.StatusOK, result)
}

func (admin *GameTesterAdmin) getBucketConfigs(c *gin.Context) {
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bundle_id is required"})
		c.Abort()
	}
	env := c.Query("env")
	if env == "" {
		env = testergame.EnvTesting
	}
	if bucketConfigs := admin.gametesterAdmin.GetBucketConfigs(bundleId, env); bucketConfigs != nil {
		if c.Query("all") != "" {
			c.JSON(http.StatusOK, bucketConfigs)
		} else {
			for index, bConfig := range bucketConfigs {
				for exp, _ := range bConfig.ExperimentList {
					bConfig.ExperimentList[exp] = nil
				}
				bucketConfigs[index] = bConfig
			}
			c.JSON(http.StatusOK, bucketConfigs)
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "no bucket configs found"})
	}
}

func (admin *GameTesterAdmin) setBucketFlow(c *gin.Context) {
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bundle_id is required"})
		return
	}
	bucket, err := strconv.Atoi(c.Query("bucket"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	flowType := c.Query("flow_type")
	if !slices.Contains([]string{testergame.BucketFlowTypeHour, testergame.BucketFlowTypeTotal}, flowType) {
		flowType = testergame.BucketFlowTypeHour
	}
	count, err := strconv.Atoi(c.Query("count"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err = admin.gametesterAdmin.SetBucketFlow(bundleId, flowType, bucket, count)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "ok"})

}

func (admin *GameTesterAdmin) setUserDistribute(c *gin.Context) {
	bundleId := c.PostForm("bundle_id")
	uid := c.PostForm("uid")
	bucket := c.PostForm("bucket")
	experiment := c.PostForm("exp")
	expermentType := c.PostForm("exp_type")
	installTime := c.PostForm("install_time")
	if bundleId == "" || uid == "" || bucket == "" || experiment == "" || expermentType == "" || installTime == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "bundle_id or uid or bucket or exp  or exp_type or install_time is required",
		})
		return
	}
	bucketId, err := strconv.Atoi(bucket)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = admin.gametesterAdmin.SetUserDistribute(bundleId, uid, bucketId, experiment, expermentType, installTime)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

func (admin *GameTesterAdmin) serverDecrypt(c *gin.Context) {
	bundleId := c.Param("bundleId")
	env := c.Param("env")
	if env == "" || bundleId == "" {
		c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "bundle_id,env is required"})
		return
	}
	deText := c.PostForm("ds")
	//defer c.Request.Body.Close()
	//body, err := io.ReadAll(c.Request.Body)
	//if err != nil {
	//	c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
	//	return
	//}
	sd, err := admin.gametesterAdmin.ServerDecrypt(bundleId, env, deText)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "ok", "data": sd})
}
