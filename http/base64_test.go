package http

import (
	"encoding/base64"
	"testing"
)

func TestBase64Decode(t *testing.T) {
	src := []byte("eyJwbGF0Zm9ybSI6ImFuZHJvaWQiLCJidW5kbGVJZCI6ImNvbS5ibG9jay5qdWdnbGUiLCJ0aGlua3VpZCI6IjMzYWIzNzMyLTgyYmMtNGFmOS1hYjUzLWQ4NmMzMDcwNmE3YiIsImFwaV92ZXJzaW9uIjoidjU4IiwiYWR3YXlzIjp7fSwibmV0d29yayI6IldJRkkiLCJvc3ZlcnNpb24iOiIxMiIsImFwcFZlcnNpb24iOiI1LjQuOCIsImlkZmEiOiI3OTYzNzZmYTk3NDU5ODEwIiwiZG5hbWUiOiJjbWkiLCJzeXN0ZW1QbGF0Zm9ybSI6ImFuZHJvaWQiLCJsYW5ndWFnZSI6InpoLUNOIiwiaWRmdiI6Ijc5NjM3NmZhOTc0NTk4MTAiLCJ0aW1lem9uZSI6IkFzaWFcL1NoYW5naGFpIiwidGltZXRhbXAiOiIxNzI3NDE1ODg0NzcxIiwiY291bnRyeSI6IkNOIiwibW9kZWxuYW1lIjoiTWkgMTAgUHJvIn0=")
	encoding := base64.StdEncoding
	decodeLen := encoding.DecodedLen(len(src))
	dst := make([]byte, decodeLen)
	n, err := encoding.Decode(dst, src)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	if n != len(dst) {
		t.Logf("expecting %d bytes, got %d", len(dst), n)
		dst = dst[:n]
	}
	t.Log(string(dst))

}
