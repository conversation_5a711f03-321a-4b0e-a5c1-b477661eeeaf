package http

import (
	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"hungrystudio.com/core/config"
	hsGin "hungrystudio.com/core/gin"
	"io"
	"net/http"
	"strconv"
)

type AdminServer struct {
	*hsGin.HttpServer
}

type AdminResult struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

func NewAdminServer(appModel string, httpConfig config.HttpServerConfig) *AdminServer {
	server := hsGin.NewHttpServer(appModel, httpConfig)
	adminServer := &AdminServer{
		server,
	}
	adminServer.initRoutes()
	return adminServer
}

func (as *AdminServer) initRoutes() {
	as.AddRoutes(
		//gin.RouteInfo{
		//	Method:      "POST",
		//	Path:        "/gametester/save",
		//	Handler:     "gametester/save",
		//	HandlerFunc: as.saveGameTester,
		//},
		//gin.RouteInfo{
		//	Method:      "GET",
		//	Path:        "/gametester/createTables",
		//	Handler:     "gametester/createTables",
		//	HandlerFunc: as.createGameTesterTables,
		//},
		//gin.RouteInfo{
		//	Method:      http.MethodPost,
		//	Path:        "/gametester/saveqa",
		//	Handler:     "gametester/saveqa",
		//	HandlerFunc: as.gametesterSaveQA,
		//},
		gin.RouteInfo{
			Method:      "POST",
			Path:        "/defend/save",
			Handler:     "defend/save",
			HandlerFunc: as.saveDefend,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/business/save",
			Handler:     "business/save",
			HandlerFunc: as.businessSave,
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/business/get",
			Handler:     "business/get",
			HandlerFunc: as.getBusiness,
		},
	)

}

//func (as *AdminServer) saveGameTester(c *gin.Context) {
//	defer c.Request.Body.Close()
//	body, err := io.ReadAll(c.Request.Body)
//	if err != nil {
//		sugared.Errorf("read body err: %v", err)
//		c.JSON(http.StatusOK, AdminResult{
//			Code:    0,
//			Message: err.Error(),
//		})
//		return
//	}
//	if err = testerTraiffic.SaveGameTesterConfigs(c.Query("bundle_id"), body); err == nil {
//		c.JSON(http.StatusOK, AdminResult{
//			Code:    1,
//			Message: "success",
//		})
//	} else {
//		c.JSON(http.StatusOK, AdminResult{
//			Code:    0,
//			Message: err.Error(),
//			Data:    body,
//		})
//	}
//}

//func (as *AdminServer) gametesterSaveQA(c *gin.Context) {
//	defer c.Request.Body.Close()
//	data, err := io.ReadAll(c.Request.Body)
//	if err != nil {
//		sugared.Errorf("read body err: %v", err)
//		c.JSON(http.StatusOK, AdminResult{Code: 0, Message: err.Error()})
//		return
//	}
//	err = testerTraiffic.SaveQA(data)
//	if err != nil {
//		sugared.Errorf("save qa err: %v", err)
//		c.JSON(http.StatusOK, AdminResult{Code: 0, Message: err.Error()})
//	} else {
//		c.JSON(http.StatusOK, AdminResult{Code: 1, Message: "success"})
//	}
//}

func (as *AdminServer) saveDefend(c *gin.Context) {
	defer c.Request.Body.Close()
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("read body err: %v", err)
		c.JSON(http.StatusOK, AdminResult{
			Code:    0,
			Message: err.Error(),
		})
	} else {
		err = defend.Save(body)
		if err != nil {
			sugared.Errorf("save defend err: %v", err)
			c.JSON(http.StatusOK, AdminResult{
				Code:    0,
				Message: err.Error(),
			})
		} else {
			c.JSON(http.StatusOK, AdminResult{
				Code:    1,
				Message: "success",
			})
		}
	}
}

//func (as *AdminServer) createGameTesterTables(c *gin.Context) {
//	// Read the SQL file
//	sqlFile, err := os.ReadFile("configs/gametester.sql")
//	if err != nil {
//		sugared.Errorf("Failed to read SQL file: %v", err)
//		c.JSON(http.StatusInternalServerError, AdminResult{
//			Code:    0,
//			Message: "Failed to read SQL file",
//		})
//		return
//	}
//
//	// Split the SQL file into individual statements
//	statements := strings.Split(string(sqlFile), ";")
//
//	// Execute each SQL statement
//	for _, stmt := range statements {
//		stmt = strings.TrimSpace(stmt)
//		if stmt == "" {
//			continue
//		}
//		_, err := gameTesterDBEngine.Exec(stmt)
//		if err != nil {
//			sugared.Errorf("Failed to execute SQL statement: %v", err)
//			c.JSON(http.StatusInternalServerError, AdminResult{
//				Code:    0,
//				Message: fmt.Sprintf("Failed to execute SQL statement: %v", err),
//			})
//			return
//		}
//	}
//
//	c.JSON(http.StatusOK, AdminResult{
//		Code:    1,
//		Message: "Successfully created game tester tables",
//	})
//}

func (as *AdminServer) businessSave(c *gin.Context) {
	defer c.Request.Body.Close()
	jsonData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusOK, AdminResult{Code: 0, Message: err.Error()})
		return
	}
	err = bsns.SaveBusinessConfigToDB(jsonData)
	if err != nil {
		c.JSON(http.StatusOK, AdminResult{Code: 0, Message: err.Error()})
	} else {
		c.JSON(http.StatusOK, AdminResult{Code: 1, Message: "success"})
	}
}

func (as *AdminServer) getBusiness(c *gin.Context) {
	page, err := strconv.Atoi(c.Query("page"))
	if err != nil {
		sugared.Errorf("query param page error: %v", err)
		page = 1
	}
	size, err := strconv.Atoi(c.Query("size"))
	if err != nil {
		sugared.Errorf("query param size error: %v", err)
		size = 50
	}
	configs, err := bsns.GetBusinessConfigs(page, size)
	if err != nil {
		c.JSON(http.StatusOK, AdminResult{Code: 0, Message: err.Error()})
	} else {
		c.JSON(http.StatusOK, configs)
	}
}
