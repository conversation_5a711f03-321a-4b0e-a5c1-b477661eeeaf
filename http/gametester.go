package http

import (
	"encoding/json"
	"io"
	"net/http"
	"slices"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"hungrystudio.com/core/utils"
)

func (t *DataTesterServer) gametesterRoutes(appModel string) {
	t.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/game_init",
			HandlerFunc: t.gameInit,
			Handler:     "game_init",
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/gametester_configs",
			Handler:     "gametester_configs",
			HandlerFunc: t.getGameTesterConfigsWan,
		})
	if appModel == gin.DebugMode {
		t.AddRoutes(
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/game_init_test",
				HandlerFunc: t.gameInitTest,
				Handler:     "game_init_test",
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/game_tester_configs",
				Handler:     "game_tester_configs",
				HandlerFunc: t.getGameTesterConfigs,
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/game_tester_all_configs",
				Handler:     "game_tester_all_configs",
				HandlerFunc: t.getGameTesterAllConfigs,
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/gametester/bucketflow",
				Handler:     "gametester_bucketflow",
				HandlerFunc: t.getBucketFlow,
			},
			//gin.RouteInfo{
			//	Method:      http.MethodGet,
			//	Path:        "/gametester/qaconfigs",
			//	Handler:     "gametester_qaconfigs",
			//	HandlerFunc: t.getGameTesterQAConfigs,
			//},
			//gin.RouteInfo{
			//	Method:      http.MethodGet,
			//	Path:        "/gametester/qamappingconfigs",
			//	Handler:     "gametester_qamappingconfigs",
			//	HandlerFunc: t.getGameTesterQAMappingConfigs,
			//},
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/gametester/addqa",
				Handler:     "gametester/addqa",
				HandlerFunc: t.addQaConfig,
			},
			gin.RouteInfo{
				Method:      http.MethodPost,
				Path:        "/gametester/aim_bucket_qa",
				Handler:     "gametester/aim_bucket_qa",
				HandlerFunc: t.aimBucketQa,
			},
			gin.RouteInfo{
				Method:      http.MethodGet,
				Path:        "/gametester_bucketflow",
				Handler:     "gametester_bucketflow",
				HandlerFunc: t.getBucketFlow,
			})
	}
}

func (t *DataTesterServer) gameInit(c *gin.Context) {
	defer c.Request.Body.Close()
	requestId := time.Now().UnixNano()
	serverLog := `{"server": "2.0"}`
	experimentDataMap := map[string]any{
		"defaultLayer": nil,
	}
	result := map[string]any{
		"serverLog":         string(serverLog),
		"experimentDataMap": experimentDataMap,
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("io.ReadAll c.Request.Body Error: %v", err)
		c.JSON(http.StatusOK, result)
		return
	}
	sugared.Infof("GameInit RequestId: %s, Body: %s", requestId, string(body))
	if json.Valid(body) {
		exp, sl, err := testerTraiffic.GameInitWithLog(body, requestId)
		if err != nil {
			sugared.Errorf("testerTraiffic.GameInit Error: %v", err)
			c.JSON(http.StatusOK, result)
			return
		}

		experimentDataMap["defaultLayer"] = exp
		result["experimentDataMap"] = experimentDataMap
		if sl != "" {
			result["serverLog"] = sl
		}
		if serverKey, err := testerTraiffic.MakeServerSecret(exp.RequestId, exp.BundleId, exp.Uid, exp.GameWayNum, exp.BucketId); err == nil {
			result["serverKey"] = serverKey
		}
		sugared.Infof("Body: %s,RequestId:%s, result: %+v", string(body), requestId, result)
		c.JSON(http.StatusOK, result)
	} else {
		sugared.Errorf("request data not json")
		c.JSON(http.StatusOK, result)
	}
}

func (t *DataTesterServer) getGameTesterConfigs(c *gin.Context) {
	configs, err := testerTraiffic.GetGameTesterConfigs(c.Query("bundle_id"))
	if err != nil {
		sugared.Errorf("testerTraiffic.GetGameTesterConfigs Error: %v", err)
		c.String(http.StatusOK, "%v", err)
	} else {
		c.JSON(http.StatusOK, configs)
	}
}

func (t *DataTesterServer) getGameTesterAllConfigs(c *gin.Context) {
	configs, err := testerTraiffic.GetGameTesterAllConfigs(c.Query("bundle_id"))
	if err != nil {
		sugared.Errorf("testerTraiffic.GetGameTesterConfigs Error: %v", err)
		c.String(http.StatusOK, "%v", err)
	} else {
		c.JSON(http.StatusOK, configs)
	}
}

func (t *DataTesterServer) getGameTesterConfigsNew(c *gin.Context) {
	c.JSON(http.StatusOK, testerTraiffic.GetGameTesterConfigsNew())
}

func (t *DataTesterServer) getGameTesterConfigsWan(c *gin.Context) {
	ip := utils.RemoteIp(c.Request)
	if slices.Contains(limitIps, ip) {
		if configs, err := testerTraiffic.GetGameTesterConfigs(c.Query("bundle_id")); err == nil {
			c.JSON(http.StatusOK, configs)
		} else {
			c.String(http.StatusOK, err.Error())
		}

	} else {
		c.JSON(http.StatusOK, nil)
	}
}

//func (t *DataTesterServer) getGameTesterQAConfigs(c *gin.Context) {
//	ip := utils.RemoteIp(c.Request)
//	if slices.Contains(limitIps, ip) {
//		c.JSON(http.StatusOK, testerTraiffic.GetQAConfigs())
//	} else {
//		c.JSON(http.StatusOK, nil)
//	}
//}
//func (t *DataTesterServer) getGameTesterQAMappingConfigs(c *gin.Context) {
//	ip := utils.RemoteIp(c.Request)
//	if slices.Contains(limitIps, ip) {
//		c.JSON(http.StatusOK, testerTraiffic.GetQAMappingConfigs())
//	} else {
//		c.JSON(http.StatusOK, nil)
//	}
//}

func (t *DataTesterServer) addQaConfig(c *gin.Context) {
	defer c.Request.Body.Close()
	result := gin.H{
		"code":    1,
		"message": "success",
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("io.ReadAll c.Request.Body Error: %v", err)
		result["code"] = 0
		result["message"] = err.Error()
		c.JSON(http.StatusOK, result)
		return
	}
	err = testerTraiffic.AddQA(body)
	if err != nil {
		sugared.Errorf("testerTraiffic.AddQA Error: %v", err)
		result["code"] = 0
		result["message"] = err.Error()
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

func (t *DataTesterServer) aimBucketQa(c *gin.Context) {
	defer c.Request.Body.Close()
	result := gin.H{
		"code":    1,
		"message": "success",
	}
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("io.ReadAll c.Request.Body Error: %v", err)
		result["code"] = 0
		result["message"] = err.Error()
		c.JSON(http.StatusOK, result)
		return
	}
	sugared.Infof("aimBucketQa Body: %s", string(body))
	err = testerTraiffic.AimBucketQA(body)
	if err != nil {
		sugared.Errorf("testerTraiffic.AimBucketQA Error: %v", err)
		result["code"] = 0
		result["message"] = err.Error()
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

func (t *DataTesterServer) getBucketFlow(c *gin.Context) {
	//ip := utils.RemoteIp(c.Request)
	//if !slices.Contains(limitIps, ip) {
	//	c.JSON(http.StatusOK, gin.H{"code": 0, "message": "failed"})
	//	return
	//}
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusOK, gin.H{"code": 0, "message": "bundle_id 不能为空"})
		return
	}
	bucketId, err := strconv.Atoi(c.Query("bucket_id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"code": 0, "message": err.Error()})
		return
	}
	//flowType := c.Query("flow_type")
	//if flowType == "" {
	//	flowType = testergame.BucketFlowTypeTotal
	//}
	//flowCount := testerTraiffic.GetBundleBucketFlow(bundleId, bucketId, flowType)
	c.JSON(http.StatusOK, gin.H{
		"code":      1,
		"message":   "success",
		"hourFlow":  testerTraiffic.GetBundleBucketFlow(bundleId, bucketId, "hour"),
		"totalFlow": testerTraiffic.GetBundleBucketFlow(bundleId, bucketId, "total"),
	})
}

func (t *DataTesterServer) getBundleFlow(c *gin.Context) {
	//ip := utils.RemoteIp(c.Request)
	//if !slices.Contains(limitIps, ip) {
	//	c.JSON(http.StatusOK, gin.H{"code": 0, "message": "failed"})
	//	return
	//}
	bundleId := c.Query("bundle_id")
	if bundleId == "" {
		c.JSON(http.StatusOK, gin.H{"code": 0, "message": "bundle_id 不能为空"})
		return
	}

	flowCount := testerTraiffic.GetBundleFlow(bundleId)
	c.JSON(http.StatusOK, gin.H{
		"code":    1,
		"message": "success",
		"flow":    flowCount,
	})
}

func (t *DataTesterServer) gameInitTest(c *gin.Context) {}
