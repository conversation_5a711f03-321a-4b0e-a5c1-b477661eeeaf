package http

import (
	"github.com/gin-gonic/gin"
	"hungrystudio.com/core/utils"
	"io"
	"net/http"
	"slices"
)

func (t *DataTesterServer) defendInit(c *gin.Context) {
	defer c.Request.Body.Close()
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		sugared.Errorf("io.ReadAll c.Request.Body Error: %v", err)
		c.<PERSON>(http.StatusOK, gin.H{
			"code": 0,
			"msg":  "read body error",
		})
		return
	} else {
		c.JSON(http.StatusOK, defend.InitConfig(body))
	}
}

func (t *DataTesterServer) getDefendConfigs(c *gin.Context) {
	ip := utils.RemoteIp(c.Request)
	if slices.Contains(limitIps, ip) {
		c.JSON(http.StatusOK, defend.Get())
	} else {
		c.<PERSON>(http.StatusOK, nil)
	}
}

func (t *DataTesterServer) defendRoutes(appModel string) {
	t.AddRoutes(
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/defend/init",
			HandlerFunc: t.defendInit,
			Handler:     "defend_init",
		},
		gin.RouteInfo{
			Method:      http.MethodGet,
			Path:        "/defend/get_config",
			Handler:     "defend_get_config",
			HandlerFunc: t.getDefendConfigs,
		},
		gin.RouteInfo{
			Method:      http.MethodPost,
			Path:        "/defend/early-warning",
			Handler:     "defend_early_warning",
			HandlerFunc: t.earlyWarning,
		},
	)
}

func (t *DataTesterServer) earlyWarning(c *gin.Context) {
	go func() {
		defer c.Request.Body.Close()
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			sugared.Errorf("io.ReadAll c.Request.Body Error: %v", err)
		}
		defend.EarlyWarning(body)
	}()
	c.Status(http.StatusOK)
}
