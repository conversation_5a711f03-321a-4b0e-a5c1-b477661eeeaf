//go:build lookup && linux && amd64

package main

import (
	"context"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/config"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/global"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/testergame"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	appConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	appConfig.AppName = global.AppNameLookup

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	logger := log.NewLogger(appConfig.Logger)
	sugared := logger.Sugar()

	lookupConfig := models.LookupConfig{}
	err := config.ParseYamlOrJsonFile("configs/lookup.json", &lookupConfig)
	if err != nil {
		sugared.Errorf("Parse GameTesterConfig Error: %s", err.Error())
		panic(err)
	}

	models.SetSugaredLogger(sugared)

	redisClient := cache.NewRedisClient(appConfig.RedisConfig)

	models.SetRedisClient(redisClient)

	testergame.SetSugaredLogger(sugared)

	gameTesterConfig := testergame.TesterTrafficConfig{}
	err = config.ParseYamlOrJsonFile("configs/gametester.json", &gameTesterConfig)
	if err != nil {
		sugared.Errorf("Parse GameTesterConfig Error: %s", err.Error())
		panic(err)
	}

	gameRedisConfig, ok := appConfig.GetRedisConfig(gameTesterConfig.UserCache)
	if !ok {
		panic("gameTesterConfig.userCache not set")
	}

	gameRedisClient := cache.NewRedisClient(gameRedisConfig)
	testergame.SetRedisClient(gameRedisClient)

	testTraffic := models.NewTesterTraffic(shutdownCtx, gameTesterConfig)

	http.SetTesterTraiffic(testTraffic)
	http.SetSugaredLogger(sugared)

	//go func() {
	//	sugared.Infof("MonitorBucketMaxFlow")
	//	ticker := time.NewTicker(time.Minute)
	//	for {
	//		select {
	//		case <-shutdownCtx.Done():
	//			return
	//		case <-ticker.C:
	//			sugared.Infof("MonitorBucketMaxFlowExecTime: %s", time.Now().Format("2006-01-02 15:04:05"))
	//			for _, monitorConfig := range lookupConfig.MonitorBucketMaxFlows {
	//				testTraffic.CheckBucketFlowLimit(monitorConfig.BundleId, monitorConfig.DDUrl, monitorConfig.DDAt, monitorConfig.KeyContent)
	//			}
	//
	//		}
	//	}
	//}()

	httpServer := http.NewDataTesterServer(appConfig.AppModel, appConfig.AppName, appConfig.HttpConfig)

	errGroup.Go(func() error {
		return httpServer.StartTCP()
	})

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	<-signalChan
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 5*time.Second)
	err = httpServer.Stop(stopCtx)
	if err != nil {
		sugared.Errorf("Error stopping http server: %s", err)
	}
	stopCancel()
	shutdownCancel()

	err = errGroup.Wait()
	if err != nil {
		sugared.Errorf("Error stopping http server: %s", err)
	} else {
		sugared.Info("Lookup server stopped")
	}

}
