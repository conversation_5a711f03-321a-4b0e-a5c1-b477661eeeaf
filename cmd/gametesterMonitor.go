//go:build gametesterMonitor && linux && amd64

package main

import (
	"context"
	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/config"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/models/testergame"
	"hungrystudio.com/datatester/services"
	"os"
	"os/signal"
	"syscall"
)

type GameTesterMonitorConfig struct {
	WriteLogConfigs []testergame.WriteLogConfig `yaml:"writeLogConfigs" json:"writeLogConfigs"`
}

func main() {
	appCofing := new(common.DataTesterServerConfig)
	appConfigFilename := "configs/app-gametesterMonitor.yaml"
	_, err := os.Stat(appConfigFilename)
	if err != nil {
		appConfigFilename = "configs/app.yaml"
	}
	err = config.ParseYamlOrJsonFile(appConfigFilename, appCofing)
	if err != nil {
		panic(err)
	}

	monitorConfig := new(GameTesterMonitorConfig)
	err = config.ParseYamlOrJsonFile("configs/gametesterMonitor.yaml", monitorConfig)
	if err != nil {
		panic(err)
	}

	logger := log.NewLogger(appCofing.Logger)
	sugared := logger.Sugar()

	sugared.Infof("Start GametesterMonitor")

	if err = services.InitRedisServices(appCofing.RedisCaches); err != nil {
		panic(err)
	}
	if err = services.InitMysqlServices(appCofing.RDBs); err != nil {
		panic(err)
	}

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	testergame.SetSugaredLogger(sugared)
	errGroup.Go(func() error {
		testergame.MonitorWriteUserLog(shutdownCtx, monitorConfig.WriteLogConfigs)
		return nil
	})

	closeSignal := make(chan os.Signal, 1)
	signal.Notify(closeSignal, syscall.SIGTERM, syscall.SIGINT)

	<-closeSignal
	shutdownCancel()

	err = errGroup.Wait()
	if err != nil {
		panic(err)
	}
	sugared.Infof("Shutting down GametesterMonitor")
}
