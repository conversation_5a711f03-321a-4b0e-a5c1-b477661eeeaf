//go:build upload_gametester_config && linux && amd64

package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"io"
	"log"
	"net/http"
	"os"
)

var bundleId string
var env string
var filename string
var configFilename string

type UploadConfig struct {
	BundleId string `json:"bundleId"`
	Env      string `json:"env"`
	Filename string `json:"filename"`
}

const (
	EnvDev     string = "dev"
	EnvRelease string = "release"
)

const (
	DevUrl     string = "http://datatester-admin-test.afafb.com/gametester/save"
	ReleaseUrl string = "http://datatester-admin.afafb.com/gametester/save"
)

func init() {
	flag.StringVar(&bundleId, "bundle", "", "包名")
	flag.StringVar(&env, "env", "dev", "which environment to use:dev 测试服，release 正式服")
	flag.StringVar(&filename, "filename", "", "the filename to upload")
	flag.StringVar(&configFilename, "config", "./config.json", "config the filename to upload")
}

type Response struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

func main() {
	flag.Parse()
	configFile, err := os.Open(configFilename)
	if err == nil {
		uploadConfig := UploadConfig{}
		if err := json.NewDecoder(configFile).Decode(&uploadConfig); err == nil {
			bundleId = uploadConfig.BundleId
			env = uploadConfig.Env
			filename = uploadConfig.Filename
		}
	}
	log.Printf("uploading config from %s to %s", filename, env)
	jsonFile, err := os.Open(filename)
	if err != nil {
		log.Fatal(err)
	}
	defer jsonFile.Close()
	byteValue, _ := io.ReadAll(jsonFile)
	if !json.Valid(byteValue) {
		log.Fatal("invalid json")
	}
	var postUrl string
	switch env {
	case EnvDev:
		postUrl = DevUrl
	case EnvRelease:
		postUrl = ReleaseUrl
	}
	if postUrl == "" {
		log.Fatal("no post url")
	}
	if bundleId != "" {
		postUrl += "?bundle_id=" + bundleId
	}
	req, err := http.NewRequest("POST", postUrl, bytes.NewBuffer(byteValue))
	if err != nil {
		log.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Fatalf("bad status: %s", resp.Status)
	}
	respBody, _ := io.ReadAll(resp.Body)
	if !json.Valid(respBody) {
		log.Fatal("invalid json")
	}
	response := Response{}
	if err := json.Unmarshal(respBody, &response); err != nil {
		log.Fatal(err)
	}
	if response.Code != 1 {
		log.Fatalf("bad code: %d,message: %s", response.Code, response.Message)
	}
	log.Printf("Upload Config File: %s To %s[%s] Success\n", filename, env, postUrl)
}
