//go:build gametester && linux && amd64

package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"syscall"
	"time"

	"go.uber.org/zap"
	"hungrystudio.com/core/config"
	"hungrystudio.com/datatester/global"
	"hungrystudio.com/datatester/kafka"
	"hungrystudio.com/datatester/whiteuid"
	"xorm.io/xorm"

	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/distributes"
	"hungrystudio.com/datatester/models/testergame"
)

func main() {
	dataTesterConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	dataTesterConfig.AppName = global.AppNameGameTester
	logger := log.NewLogger(dataTesterConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	//gameTesterConfig := dataTesterConfig.GameTesterConfig

	//gameTesterConfig, err := testergame.NewTesterTrafficConfig("configs/" + global.AppNameGameTester + ".json")
	//if err != nil {
	//	sugared.Errorf("NewTesterTrafficConfig() error: %v", err)
	//	panic(err)
	//}

	gameTesterConfig := testergame.TesterTrafficConfig{}
	err := config.ParseYamlOrJsonFile("configs/gametester.json", &gameTesterConfig)
	if err != nil {
		sugared.Errorf("Parse GameTesterConfig Error: %s", err.Error())
		panic(err)
	}

	gameTesterConfig.BundleId = os.Getenv("GAME_BUNDLE_ID")

	models.SetSugaredLogger(sugared)
	distributes.SetSugaredLogger(sugared)
	kafka.SetSugaredLogger(sugared)

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	// 添加KafkaRemote
	if dataTesterConfig.KafkaRemote {
		k := kafka.NewKafkaRemote(dataTesterConfig.KafkaOption)
		errGroup.Go(func() error {
			return k.Close(shutdownCtx)
		})
		models.SetKafkaRemote(k)
	}

	//ipData := models.NewIPData(shutdownCtx, dataTesterConfig.IPDataUrl)
	//http.SetIPData(ipData)
	dbPasswordGameTester := os.Getenv("DB_PASSWORD_GAMETESTER")
	if dbPasswordGameTester == "" {
		panic("DB_PASSWORD_GAMETESTER environment variable not set")
	}
	gameTesterDBConfigs, ok := dataTesterConfig.GetRDBConfig(gameTesterConfig.UserRDB)
	if !ok {
		panic("gameTesterConfig.userRDB not set")
	}
	gameTesterDBEngine, err := common.NewXormEngineWithPassword(gameTesterDBConfigs, dbPasswordGameTester)
	if err != nil {
		sugared.Errorf("NewGameTesterXormEngine err: %v", err)
		panic(err)
	}
	if groupEngines, ok := gameTesterDBEngine.(*xorm.EngineGroup); ok {
		sugared.Infof("Use EngineGroup")
		masterEngine := groupEngines.Master()
		if err = masterEngine.Ping(); err != nil {
			sugared.Errorf("MasterEngine ping err: %v", err)
			panic("MasterEngine ping Failed")
		} else {
			sugared.Infof("MasterEngine ping OK. DBStats: %+v", masterEngine.DB().Stats())
		}
		for _, engine := range groupEngines.Slaves() {
			if err = engine.Ping(); err != nil {
				sugared.Errorf("SlaveEngine ping err: %v", err)
				panic("SlaveEngine Ping Failed")
			} else {
				sugared.Infof("SlaveEngine Ping OK. DBStats: %+v", engine.DB().Stats())
			}
		}
	}
	sugared.Infof("gameTesterDBEngine start success. gameTesterDBConfigs:%+v", gameTesterDBConfigs)
	testergame.SetGameTesterDBEngine(gameTesterDBEngine)

	//dbconfigs.SetEngine(xormEngine)
	//dbconfigs.SetSugaredLogger(sugared)

	redisConfig, ok := dataTesterConfig.GetRedisConfig(gameTesterConfig.UserCache)
	if !ok {
		panic("gameTesterConfig.userCache not set")
	}

	redisConfig.DB = getRedisDBFromEnv()

	redisClient := cache.NewRedisClient(redisConfig)
	models.SetRedisClient(redisClient)
	testergame.SetRedisClient(redisClient)
	whiteuid.Init(gameTesterConfig.UpdateInterval, redisClient, sugared)

	if gameTesterConfig.UseUserBackupCache {
		backupRedisConfig, ok := dataTesterConfig.GetRedisConfig(gameTesterConfig.UserBackupCache)
		if !ok {
			panic("gameTesterConfig.userBackupCache not set")
		}
		backupRedisConfig.DB = getRedisDBFromEnv()
		backupRedisClient := cache.NewRedisClient(backupRedisConfig)
		testergame.SetBackupRedisClient(backupRedisClient)
	}

	// 在缓存配置相同的情况下用不同的缓存配置，csr在本地测试的时候，也需要线上读取数据，需要分开设置
	if gameTesterConfig.UserCache == gameTesterConfig.CSRRedis {
		testergame.SetCsrRedisClient(redisClient)
	} else {
		csrRedisConfig, ok := dataTesterConfig.GetRedisConfig(gameTesterConfig.CSRRedis)
		if !ok {
			panic("gameTesterConfig.CSRRedis not set")
		}
		csrRedisClient := cache.NewRedisClient(csrRedisConfig)
		testergame.SetCsrRedisClient(csrRedisClient)
	}

	// 初始化bundleConfigs
	//models.InitBundleConfigs(shutdownCtx)

	http.SetLimitIps(dataTesterConfig.LimitIps)

	http.SetSugaredLogger(sugared)
	metrics.SetSugaredLogger(sugared)

	// 游戏测试分流
	testergame.SetSugaredLogger(sugared)
	sugared.Infof("gametester qa: %v", gameTesterConfig.QA)
	testerTraffic := models.NewTesterTraffic(shutdownCtx, gameTesterConfig)
	http.SetTesterTraiffic(testerTraffic)
	if gameTesterConfig.ABTestEnabled {
		//httpClient := client2.NewHttpClient(gameTesterConfig.HttpClientConfig)
		abtestFeatures := testergame.NewABTestFeatures(gameTesterConfig.AbtestFeatures, gameTesterConfig.HttpClientConfig)
		testerTraffic.SetABTestFeatures(abtestFeatures)
	}
	// 分流拦截器
	if gameTesterConfig.UseInterceptor {
		for _, intercepterConfig := range gameTesterConfig.Interceptors {
			if intercepterConfig.Enabled {
				switch intercepterConfig.Name {
				case testergame.OverScoreInterceptorName:
					i := testergame.NewOverScoreInterceptor(intercepterConfig, redisClient)
					testerTraffic.AddInterceptor(i)
				}
			}
		}
	}

	// 多链路配置
	if gameTesterConfig.MultiLinkEnabled {
		serverEnv := os.Getenv("ENV")
		if serverEnv == "" {
			serverEnv = "prod"
		}
		//linksData := testergame.NewLinksData(redisClient, gameTesterConfig.BundleId, serverEnv)
		multiLink := testergame.NewMultiLink(gameTesterConfig.BundleId, serverEnv, redisClient)
		//multiLink.SetLinksData(linksData)
		errGroup.Go(func() error {
			return multiLink.Update(shutdownCtx)
		})
		testerTraffic.AddMultiLink(multiLink)

		//if len(gameTesterConfig.MultiLinks) > 0 {
		//	for _, multiLinkConfig := range gameTesterConfig.MultiLinks {
		//		multiLink := testergame.NewMultiLink(multiLinkConfig.BunldeId, serverEnv)
		//		testerTraffic.AddMultiLink(multiLink)
		//	}
		//} else {
		//	multiLink := testergame.NewMultiLink(gameTesterConfig.BundleId, serverEnv)
		//	testerTraffic.AddMultiLink(multiLink)
		//}
	}

	go MonitorGoroutineCount(sugared)

	dataTesterServer := http.NewDataTesterServer(dataTesterConfig.AppModel, dataTesterConfig.AppName, dataTesterConfig.HttpConfig)
	errGroup.Go(func() error {
		return dataTesterServer.Start()
	})

	errGroup.Go(func() error {
		return metrics.Start(dataTesterConfig.Metrics.Addr)
	})

	errGroup.Go(func() error {
		<-shutdownCtx.Done()
		return metrics.Stop()
	})

	// 关闭信号
	shutSignal := make(chan os.Signal, 1)
	signal.Notify(shutSignal, syscall.SIGTERM, syscall.SIGINT)
	errGroup.Go(func() error {
		<-shutSignal
		sugared.Infof("Start Shutdown DataTesterServer")
		timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*2)
		defer timeoutCancel()
		err = dataTesterServer.Stop(timeoutCtx)
		if err != nil {
			sugared.Errorf("dataTesterServer.Stop Error: %v", err)
		} else {
			sugared.Infof("dataTesterServer.Stop Success.")
		}
		shutdownCancel()
		return err
	})

	if err = errGroup.Wait(); err != nil {
		sugared.Errorf("errGroup.Wait Error: %v", err)
	} else {
		sugared.Infof("DataTesterServer Exit")
	}
}

func getRedisDBFromEnv() int {
	if redisDBValue := os.Getenv("REDIS_DB"); redisDBValue != "" {
		if redisDBInt, err := strconv.Atoi(redisDBValue); err == nil {
			return redisDBInt
		}
	}
	return 0
}

func MonitorGoroutineCount(sugared *zap.SugaredLogger) {
	for range time.Tick(time.Second) {
		count := runtime.NumGoroutine()
		fmt.Println("Goroutine Count: ", count)
		sugared.Infof("Goroutine Count: %d", count)
		// 如果goroutine数量超过5000，直接退出
		if count >= 5000 {
			fmt.Println("Goroutine Count is too large: ", count)
			sugared.Errorf("Goroutine Count is too large: %d", count)
			sugared.Sync()
			os.Exit(0)
		}
	}
}
