//go:build business && linux && amd64

package main

import (
	"context"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/global"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/business"
	"hungrystudio.com/datatester/models/distributes"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	appConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	appConfig.AppName = global.AppNameBusiness
	logger := log.NewLogger(appConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownContext, cancel := context.WithCancel(ctx)

	dbPassword := os.Getenv("DB_PASSWORD")
	if dbPassword == "" {
		panic("DB_PASSWORD environment variable not set")
	}
	dbConfigs, ok := appConfig.GetRDBConfig(appConfig.BusinessConfig.RDB)
	if !ok {
		panic("RDB config not found in appConfig.BusinessConfig.RDB")
	}
	xormEngine, err := common.NewXormEngineWithPassword(dbConfigs, dbPassword)
	if err != nil {
		sugared.Errorf("NewXormEngine Error: %v", err)
	}

	models.SetSugaredLogger(sugared)

	ipData := models.NewIPData(shutdownContext, appConfig.IPDataUrl)

	if appConfig.BusinessConfig.QaEnable {
		if redisConfig, ok := appConfig.RedisCaches[appConfig.BusinessConfig.Cache]; ok {
			redisClient := cache.NewRedisClient(redisConfig)
			business.SetRedisClient(redisClient)
		}
	}

	business.SetSugaredLogger(sugared)
	business.SetXormEngine(xormEngine)
	bsns := models.NewBusiness(shutdownContext, appConfig.BusinessConfig)

	http.SetSugaredLogger(sugared)
	http.SetLimitIps(appConfig.LimitIps)
	http.SetBusiness(bsns)
	http.SetIPData(ipData)

	distributes.SetSugaredLogger(sugared)

	metrics.SetSugaredLogger(sugared)
	errGroup.Go(func() error {
		return metrics.Start(appConfig.Metrics.Addr)
	})

	errGroup.Go(func() error {
		<-shutdownContext.Done()
		return metrics.Stop()
	})

	httpServer := http.NewDataTesterServer(appConfig.AppModel, appConfig.AppName, appConfig.HttpConfig)
	errGroup.Go(func() error {
		return httpServer.Start()
	})

	// 关闭信号
	shutSignal := make(chan os.Signal, 1)
	signal.Notify(shutSignal, syscall.SIGTERM, syscall.SIGINT)
	errGroup.Go(func() error {
		<-shutSignal
		sugared.Infof("Start Shutdown DataTesterServer")
		timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*2)
		defer timeoutCancel()
		err = httpServer.Stop(timeoutCtx)
		if err != nil {
			sugared.Errorf("dataTesterServer.Stop Error: %v", err)
		} else {
			sugared.Infof("dataTesterServer.Stop Success.")
		}
		cancel()
		return err
	})

	if err = errGroup.Wait(); err != nil {
		sugared.Errorf("errGroup.Wait Error: %v", err)
	} else {
		sugared.Infof("BusinessServer Exit")
	}
}
