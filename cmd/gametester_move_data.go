//go:build gametester_move_data && linux && amd64

package main

import (
	"context"
	"encoding/json"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	"github.com/twmb/murmur3"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/models/testergame"
	"strings"
	"time"
)

//var uid string
//var exp string
//var bucket int
//var installTime string

//func init() {
//	//flag.StringVar(&uid, "uid", "b96845a0-7808-43b0-92da-bdf4725e0791", "用户访客ID")
//	//flag.StringVar(&exp, "exp", "144_32", "方案号")
//	//flag.IntVar(&bucket, "bucket", 40017, "桶ID")
//	//flag.StringVar(&installTime, "install", "2024-12-01 08:00:00", "安装时间")
//}

func main() {
	//flag.Parse()
	//if uid == "" {
	//	fmt.Println("--uid 参数不能为空")
	//	os.Exit(1)
	//}
	dataTesterConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	//dataTesterConfig.AppName = global.AppNameGameTester
	logger := log.NewLogger(dataTesterConfig.Logger)
	sugared := logger.Sugar()

	//gameTesterConfig := testergame.TesterTrafficConfig{}
	//err := config.ParseYamlOrJsonFile("configs/gametester.json", &gameTesterConfig)
	//if err != nil {
	//	sugared.Errorf("Parse GameTesterConfig Error: %s", err.Error())
	//	panic(err)
	//}

	//localDBPassword := os.Getenv("DB_PASSWORD")
	//if localDBPassword == "" {
	//	panic("DB_PASSWORD environment variable not set")
	//}

	localGametesterDBConfig := dataTesterConfig.RDBs["gametester_local"]
	localDB, err := common.NewXormEngineWithPassword(localGametesterDBConfig, "BUC0TG0aWCVaLGaM")
	if err != nil {
		sugared.Errorf("NewXormEngine Error: %s", err.Error())
		panic(err)
	}
	onlineDBConfigs := dataTesterConfig.RDBs["gametester"]
	onlineDB, err := common.NewXormEngineWithPassword(onlineDBConfigs, "DTAfBPBHbDnGzCHS")
	if err != nil {
		sugared.Errorf("NewXormEngine Error: %s", err.Error())
		panic(err)
	}
	redisConfig := dataTesterConfig.RedisCaches["prodBackup"]
	redisClient := cache.NewRedisClient(redisConfig)
	if redisClient == nil {
		sugared.Errorf("NewRedisClient nil")
		panic(err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()

	//disTime, _ := time.Parse(time.DateTime, installTime)
	////uid := ""
	//userExp := testergame.UserExperiment{
	//	ExperimentId:   exp,
	//	BucketId:       bucket,
	//	ExperimentType: 1,
	//	DistributeTime: disTime.UnixMilli(),
	//}
	//userJson, _ := json.Marshal(userExp)
	//uCacheKey := fmt.Sprintf("hs:gametester:userexperiment:com.block.juggle:%s", uid)
	//err = redisClient.Set(ctx, uCacheKey, userJson, time.Hour).Err()
	//if err != nil {
	//	sugared.Errorf("Set Error: %s", err.Error())
	//} else {
	//	sugared.Infof("Set Success")
	//}
	//os.Exit(1)
	count := 0
	oCount := 0
	cCount := 0
	tableNamePrefix := "gt_user_experiments_"
	for i := 0; i < 64; i++ {
		tablName := fmt.Sprintf("%s%d", tableNamePrefix, i)
		gtUsers := make([]testergame.GtUserExperiments, 0)
		err = localDB.Where("bundle_id=? and bucket_id=?", "com.block.juggle", 40019).Table(tablName).Find(&gtUsers)
		if err != nil {
			sugared.Errorf("Get rows Error: %s", err.Error())
			return
		}
		for _, gtUser := range gtUsers {
			if len(gtUser.Uid) > 3 {
				if strings.Contains(gtUser.Uid, "test_") {
					continue
				}
				sugared.Infof("DB Users: %v", gtUser)
				hash := murmur3.Sum32([]byte(gtUser.Uid))
				tableName := fmt.Sprintf("%s%d", tableNamePrefix, hash%64)
				onlineGtUser := testergame.GtUserExperiments{}
				ok, err := onlineDB.Where("bundle_id=? and uid=?", "com.block.juggle", gtUser.Uid).Table(tableName).Get(&onlineGtUser)
				if err != nil {
					sugared.Errorf("Get rows Error: %s", err.Error())
					continue
				}
				if ok {
					sugared.Infof("OnlineGtUser: %v", onlineGtUser)
					n, err := onlineDB.Where("bundle_id=? and uid=?", "com.block.juggle", gtUser.Uid).Table(tableName).Update(gtUser)
					if err == nil {
						sugared.Infof("Update online user %v n: %v", gtUser, n)
					}
					oCount++
				} else {
					n, err := onlineDB.Table(tableName).Insert(&gtUser)
					if err == nil {
						sugared.Infof("Insert online user %v n: %v", gtUser, n)
					}
				}
				uCacheKey := fmt.Sprintf("hs:gametester:userexperiment:com.block.juggle:%s", gtUser.Uid)
				userCacheJson, err := redisClient.Get(ctx, uCacheKey).Result()
				if err == nil {
					userExp := testergame.UserExperiment{}
					err = json.Unmarshal([]byte(userCacheJson), &userExp)
					if err == nil {
						sugared.Infof("CacheUser: %v", userExp)
						cCount++
					}
				}
				ctx1, cancel1 := context.WithTimeout(context.Background(), time.Second*2)

				gtUserJson, _ := json.Marshal(gtUser)
				err = redisClient.Set(ctx1, uCacheKey, gtUserJson, time.Hour).Err()
				if err != nil {
					sugared.Errorf("Set Error: %s", err.Error())
				} else {
					sugared.Infof("Set Success")
				}
				fmt.Println(gtUser.Uid)
				cancel1()
				count++
			}

		}
	}
	fmt.Printf("Count: %d,OnlineCount: %d, CacheCount: %d\n", count, oCount, cCount)
	//dbConfig, ok := dataTesterConfig.GetRDBConfig(gameTesterConfig.UserRDB)
	//if !ok {
	//	sugared.Errorf("Get RDB Config: %s failed!", gameTesterConfig.UserRDB)
	//}
	//
	//dbPassword := os.Getenv("DB_PASSWORD")
	//if dbPassword == "" {
	//	panic("DB_PASSWORD environment variable not set")
	//}
	//xormEngine, err := common.NewXormEngineWithPassword(dbConfig, dbPassword)
	//if err != nil {
	//	panic(err)
	//}
	//
	////xormEngine.ShowSQL(true)
	//
	//redisConfig, ok := dataTesterConfig.GetRedisConfig(gameTesterConfig.UserBackupCache)
	//if !ok {
	//	panic("Get Redis Config Error")
	//}
	//
	//redisClient := cache.NewRedisClient(redisConfig)
	//testergame.SetBackupRedisClient(redisClient)
	//
	////size := 50
	//
	//userExperiment := &testergame.UserExperiment{}
	//totalCount := 0
	//sugared.Infof("开始把数据库中的用户数据导入缓存")
	//for i := 0; i < 64; i++ {
	//	tableName := fmt.Sprintf(testergame.GtUserExperimentsTableName, i)
	//	sugared.Infof("开始操作表：%s", tableName)
	//	page := 1
	//	start := (page - 1) * size
	//	userExps := make([]testergame.GtUserExperiments, 0)
	//	tableErrCount := 0
	//	tableCount := 0
	//	err = xormEngine.Table(tableName).Limit(size, start).Find(&userExps)
	//	for err == nil && len(userExps) > 0 {
	//		for _, userExp := range userExps {
	//			if userExp.Uid == "" {
	//				continue
	//			}
	//			uExp := testergame.UserExperiment{
	//				BucketId:       userExp.BucketId,
	//				ExperimentId:   userExp.ExperimentId,
	//				ExperimentType: userExp.ExperimentType,
	//				DistributeTime: userExp.DistributeTime,
	//			}
	//			err = userExperiment.SaveUserToBackupCache(userExp.BundleId, userExp.Uid, uExp)
	//			if err != nil {
	//				tableErrCount++
	//			} else {
	//				tableCount++
	//				totalCount++
	//			}
	//		}
	//		page++
	//		start = (page - 1) * size
	//		userExps = userExps[:0]
	//		if interval > 0 {
	//			time.Sleep(time.Duration(interval) * time.Millisecond)
	//		}
	//		err = xormEngine.Table(tableName).Limit(size, start).Find(&userExps)
	//	}
	//	sugared.Infof("表: %s 导入缓存完成,导入总数：%d,错误数: %d", tableName, tableCount, tableErrCount)
	//}
	//
	//sugared.Infof("导入数据总数：%d", totalCount)
}
