//go:build defend && ((linux && amd64) || (darwin && arm64))

package main

import (
	"context"
	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/global"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models"
	defend2 "hungrystudio.com/datatester/models/defend"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	dataTesterConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	dataTesterConfig.AppName = global.AppNameDefend
	logger := log.NewLogger(dataTesterConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	eventLog := log.NewZapLog(dataTesterConfig.EventLog)

	models.SetSugaredLogger(sugared)

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	dbPassword := os.Getenv("DB_PASSWORD")
	if dbPassword == "" {
		panic("DB_PASSWORD environment variable not set")
	}
	dbConfigs, ok := dataTesterConfig.GetRDBConfig(dataTesterConfig.DefendConfig.RDB)
	if !ok {
		panic("DefendConfig.RDB configuration not found")
	}
	xormEngine, err := common.NewXormEngineWithPassword(dbConfigs, dbPassword)
	if err != nil {
		sugared.Errorf("NewXormEngine err: %v", err)
	}
	if dataTesterConfig.AppModel == gin.DebugMode {
		xormEngine.ShowSQL(true)
	}
	defend2.SetXormEngine(xormEngine)

	if dataTesterConfig.DefendConfig.UseCacheRewrite {
		redisCacheConfig, ok := dataTesterConfig.GetRedisConfig(dataTesterConfig.DefendConfig.RedisCache)
		if ok {
			redisClient := cache.NewRedisClient(redisCacheConfig)
			models.SetRedisClient(redisClient)
		}
	}

	// 初始化bundleConfigs
	//models.InitBundleConfigs(shutdownCtx)

	http.SetLimitIps(dataTesterConfig.LimitIps)

	http.SetSugaredLogger(sugared)
	metrics.SetSugaredLogger(sugared)

	// 黑天鹅防护

	defend := models.NewDefendConfigs(dataTesterConfig.DefendConfig)
	defend.SetEventLogger(eventLog.Sugar())
	http.SetDefendConfigs(defend)

	dataTesterServer := http.NewDataTesterServer(dataTesterConfig.AppModel, dataTesterConfig.AppName, dataTesterConfig.HttpConfig)
	errGroup.Go(func() error {
		return dataTesterServer.Start()
	})

	errGroup.Go(func() error {
		return metrics.Start(dataTesterConfig.Metrics.Addr)
	})

	errGroup.Go(func() error {
		<-shutdownCtx.Done()
		return metrics.Stop()
	})

	// 关闭信号
	shutSignal := make(chan os.Signal, 1)
	signal.Notify(shutSignal, syscall.SIGTERM, syscall.SIGINT)
	errGroup.Go(func() error {
		<-shutSignal
		sugared.Infof("Start Shutdown DataTesterServer")
		timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*2)
		defer timeoutCancel()
		err := dataTesterServer.Stop(timeoutCtx)
		if err != nil {
			sugared.Errorf("dataTesterServer.Stop Error: %v", err)
		} else {
			sugared.Infof("dataTesterServer.Stop Success.")
		}
		shutdownCancel()
		return err
	})

	if err := errGroup.Wait(); err != nil {
		sugared.Errorf("errGroup.Wait Error: %v", err)
	} else {
		sugared.Infof("DataTesterServer Exit")
	}
}
