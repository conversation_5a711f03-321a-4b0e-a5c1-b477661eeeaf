//go:build csr && linux && amd64

package main

import (
	"context"
	"github.com/gin-gonic/gin"
	"hungrystudio.com/core/config"
	"hungrystudio.com/datatester/global"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/dbconfigs"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/distributes"
)

func main() {
	appConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	appConfig.AppName = global.AppNameCsr

	logger := log.NewLogger(appConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	csrTrafficConfigs := models.CSRTrafficConfig{}
	err := config.ParseYamlOrJsonFile("configs/csr.json", &csrTrafficConfigs)
	if err != nil {
		sugared.Errorf("Parse configs/csr.json err: %v", err)
		panic(err)
	}

	models.SetSugaredLogger(sugared)

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	configDBName := csrTrafficConfigs.ConfigDB
	var dbConfigs common.DBConfigs
	var ok bool
	if dbConfigs, ok = appConfig.RDBs[configDBName]; !ok {
		panic("configName: " + configDBName + " not exist")
	}

	xormEngine, err := common.NewXormEngine(dbConfigs)
	if err != nil {
		sugared.Errorf("NewXorgEngine err: %v", err)
		panic(err)
	}
	if appConfig.AppModel == gin.DebugMode {
		xormEngine.ShowSQL(true)
	}
	//if appConfig.UseDBGameTester {
	//	sugared.Infof("Do Use DBGameTester")
	//	gameTesterDBEngine, err := common.NewGameTesterXormEngine(appConfig.DBGameTester)
	//	if err != nil {
	//		sugared.Errorf("NewGameTesterXormEngine err: %v", err)
	//		panic(err)
	//	}
	//	testergame.SetGameTesterDBEngine(gameTesterDBEngine)
	//} else {
	//	sugared.Infof("Don't Use DBGameTester")
	//}

	dbconfigs.SetEngine(xormEngine)
	dbconfigs.SetSugaredLogger(sugared)

	//adconfigs.SetSugaredLogger(sugared)
	redisConfig, ok := appConfig.RedisCaches[csrTrafficConfigs.UserCache]
	if !ok {
		panic("redis config not found")
	}
	redisClient := cache.NewRedisClient(redisConfig)
	models.SetRedisClient(redisClient)
	// 初始化bundleConfigs
	//models.InitBundleConfigs(shutdownCtx)

	// 分流设置
	distributes.SetSugaredLogger(sugared)

	http.SetLimitIps(appConfig.LimitIps)

	http.SetSugaredLogger(sugared)
	metrics.SetSugaredLogger(sugared)

	// 方块csr分流测试
	csrTraffic := models.NewCSRTraffic(csrTrafficConfigs)
	//csrRedis := cache.NewRedisClient(appConfig.CsrParamRedis)
	//models.SetCsrParamRedis(csrRedis)
	http.SetCSRTraffic(csrTraffic)

	// 游戏测试分流
	//testergame.SetRedisClient(redisClient)
	//testergame.SetSugaredLogger(sugared)
	//testerTraffic := models.NewTesterTraffic(shutdownCtx)
	//http.SetTesterTraiffic(testerTraffic)

	errGroup.Go(func() error {
		return metrics.Start(appConfig.Metrics.Addr)
	})

	errGroup.Go(func() error {
		<-shutdownCtx.Done()
		return metrics.Stop()
	})

	//ipData := models.NewIPData(shutdownCtx, appConfig.IPDataUrl)
	//
	//http.SetIPData(ipData)

	// 黑天鹅防护
	//defend := models.NewDefendConfigs()
	//http.SetDefendConfigs(defend)

	dataTesterServer := http.NewDataTesterServer(appConfig.AppModel, appConfig.AppName, appConfig.HttpConfig)

	errGroup.Go(func() error {
		return dataTesterServer.Start()
	})

	// 关闭信号
	shutSignal := make(chan os.Signal, 1)
	signal.Notify(shutSignal, syscall.SIGTERM, syscall.SIGINT)
	errGroup.Go(func() error {
		<-shutSignal
		sugared.Infof("Start Shutdown DataTesterServer")
		timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*2)
		defer timeoutCancel()
		err := dataTesterServer.Stop(timeoutCtx)
		if err != nil {
			sugared.Errorf("dataTesterServer.Stop Error: %v", err)
		} else {
			sugared.Infof("dataTesterServer.Stop Success.")
		}
		shutdownCancel()
		return err
	})

	if err := errGroup.Wait(); err != nil {
		sugared.Errorf("errGroup.Wait Error: %v", err)
	} else {
		sugared.Infof("DataTesterServer Exit")
	}
}
