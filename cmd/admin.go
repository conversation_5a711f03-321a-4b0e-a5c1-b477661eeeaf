//go:build admin && linux && amd64

package main

import (
	"context"
	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/business"
	"hungrystudio.com/datatester/models/testergame"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	dataTesterServerConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	logger := log.NewLogger(dataTesterServerConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)

	http.SetSugaredLogger(sugared)
	models.SetSugaredLogger(sugared)

	// 初始化bundleConfigs
	//models.InitBundleConfigs(ctx)

	dbPassword := os.Getenv("DB_PASSWORD")
	if dbPassword == "" {
		panic("DB_PASSWORD environment variable not set")
	}

	dbConfigs, ok := dataTesterServerConfig.GetRDBConfig(dataTesterServerConfig.AdminConfig.RDB)
	if !ok {
		panic("AdminConfig RDB Config not found")
	}

	xormEngine, err := common.NewXormEngineWithPassword(dbConfigs, dbPassword)
	if err != nil {
		sugared.Errorf("NewXorgEngine err: %v", err)
		panic(err)
	}

	if dataTesterServerConfig.AppModel == gin.DebugMode {
		xormEngine.ShowSQL(true)
	}

	redisCacheConfig, ok := dataTesterServerConfig.GetRedisConfig(dataTesterServerConfig.AdminConfig.RedisCache)
	if !ok {
		panic("RedisCache Config not found")
	}

	sugared.Infof("RedisCache Config: %+v", redisCacheConfig)

	redisClient := cache.NewRedisClient(redisCacheConfig)
	models.SetRedisClient(redisClient)

	testergame.SetRedisClient(redisClient)
	testergame.SetSugaredLogger(sugared)

	if dataTesterServerConfig.AdminConfig.UseRedisCacheBackup {
		redisConfigBackup, ok := dataTesterServerConfig.GetRedisConfig(dataTesterServerConfig.AdminConfig.RedisCacheBackup)
		if !ok {
			panic("adminConfig.RedisCacheBackup Config not found")
		}
		redisClientBackup := cache.NewRedisClient(redisConfigBackup)
		testergame.SetBackupRedisClient(redisClientBackup)
	}

	// 客户端版本下发
	//testerTraffic := models.NewTesterTraffic(ctx, dataTesterServerConfig.GameTesterConfig)
	//
	//http.SetTesterTraiffic(testerTraffic)

	// 黑天鹅防护
	defend := models.NewDefendConfigs(dataTesterServerConfig.DefendConfig)
	http.SetDefendConfigs(defend)

	// 商业户分流
	business.SetXormEngine(xormEngine)
	business.SetSugaredLogger(sugared)
	business.SetRedisClient(redisClient)
	bsns := models.NewBusiness(ctx, dataTesterServerConfig.BusinessConfig)
	http.SetBusiness(bsns)

	adminServer := http.NewAdminServer(dataTesterServerConfig.AppModel, dataTesterServerConfig.AdminHttpConfig)
	go func() {
		err := adminServer.Start()
		if err != nil {
			sugared.Errorf("Admin Server Start Error: %s", err.Error())
		}
	}()

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	<-c

	err = adminServer.Stop(ctx)
	if err != nil {
		sugared.Errorf("Admin Server Stop Error: %s", err.Error())
	} else {
		sugared.Infof("Admin Server Stop Success")
	}
	cancel()
}
