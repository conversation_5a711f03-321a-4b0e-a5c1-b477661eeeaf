//go:build datatester && linux && amd64

package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/dbconfigs"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/metrics"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/adconfigs"
	"hungrystudio.com/datatester/models/distributes"
	"hungrystudio.com/datatester/models/testergame"
)

func main() {
	dataTesterServerConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	logger := log.NewLogger(dataTesterServerConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	models.SetSugaredLogger(sugared)

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	xormEngine, err := common.NewXormEngine(dataTesterServerConfig.DBConfigs)
	if err != nil {
		sugared.Errorf("NewXorgEngine err: %v", err)
		panic(err)
	}
	if dataTesterServerConfig.UseDBGameTester {
		sugared.Infof("Do Use DBGameTester")
		gameTesterDBEngine, err := common.NewGameTesterXormEngine(dataTesterServerConfig.DBGameTester)
		if err != nil {
			sugared.Errorf("NewGameTesterXormEngine err: %v", err)
			panic(err)
		}
		testergame.SetGameTesterDBEngine(gameTesterDBEngine)
	} else {
		sugared.Infof("Don't Use DBGameTester")
	}

	dbconfigs.SetEngine(xormEngine)
	dbconfigs.SetSugaredLogger(sugared)

	adconfigs.SetSugaredLogger(sugared)

	redisClient := cache.NewRedisClient(dataTesterServerConfig.RedisConfig)
	models.SetRedisClient(redisClient)
	// 初始化bundleConfigs
	models.InitBundleConfigs(shutdownCtx)

	// 分流设置
	distributes.SetSugaredLogger(sugared)
	distributeFactory := distributes.NewDistributeFactory()
	//dayDistributeProvider := distributes.NewDayDistributeProvider(shutdownCtx, 48)
	//distributeFactory.Register(distributes.DayDistributeProviderInstance, dayDistributeProvider)
	//CSR分流
	sudoDistributeProvider := distributes.NewSudoRoBinDistributeProvider(shutdownCtx, 48)
	distributeFactory.Register(distributes.SudoDistributeProviderRobinInstance, sudoDistributeProvider)
	// 日分流
	//dayDistributeProvider := models.NewDayDistributeProvider()
	//errGroup.Go(func() error {
	//	return dayDistributeProvider.Close(shutdownCtx)
	//})
	//models.SetDistributeProvider(dayDistributeProvider)
	models.SetDistributeFactory(distributeFactory)

	// 滴滴分流
	//didiDistributeProvider := models.NewDidiDistributeProvider(dataTesterServerConfig.Didi)
	//models.SetDidiDistributeProvider(didiDistributeProvider)

	traffics := models.NewTraffics(shutdownCtx, dataTesterServerConfig.AbLogWriter)
	http.SetTraiffics(traffics)

	http.SetLimitIps(dataTesterServerConfig.LimitIps)

	http.SetSugaredLogger(sugared)
	metrics.SetSugaredLogger(sugared)

	//// 方块csr分流测试
	//csrTraffic := models.NewCSRTraffic()
	//csrRedis := cache.NewRedisClient(dataTesterServerConfig.CsrParamRedis)
	//models.SetCsrParamRedis(csrRedis)
	//http.SetCSRTraffic(csrTraffic)

	//// 游戏测试分流
	//testergame.SetRedisClient(redisClient)
	//testergame.SetSugaredLogger(sugared)
	//testerTraffic := models.NewTesterTraffic(shutdownCtx)
	//http.SetTesterTraiffic(testerTraffic)

	errGroup.Go(func() error {
		return metrics.Start(dataTesterServerConfig.Metrics.Addr)
	})

	errGroup.Go(func() error {
		<-shutdownCtx.Done()
		return metrics.Stop()
	})

	ipData := models.NewIPData(shutdownCtx, dataTesterServerConfig.IPDataUrl)

	http.SetIPData(ipData)

	//// 黑天鹅防护
	//defend := models.NewDefendConfigs()
	//http.SetDefendConfigs(defend)

	dataTesterServer := http.NewDataTesterServer(dataTesterServerConfig.AppModel, dataTesterServerConfig.AppName, dataTesterServerConfig.HttpConfig)

	errGroup.Go(func() error {
		return dataTesterServer.Start()
	})

	// 关闭信号
	shutSignal := make(chan os.Signal, 1)
	signal.Notify(shutSignal, syscall.SIGTERM, syscall.SIGINT)
	errGroup.Go(func() error {
		<-shutSignal
		sugared.Infof("Start Shutdown DataTesterServer")
		timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*2)
		defer timeoutCancel()
		err = dataTesterServer.Stop(timeoutCtx)
		if err != nil {
			sugared.Errorf("dataTesterServer.Stop Error: %v", err)
		} else {
			sugared.Infof("dataTesterServer.Stop Success.")
		}
		shutdownCancel()
		return err
	})

	if err = errGroup.Wait(); err != nil {
		sugared.Errorf("errGroup.Wait Error: %v", err)
	} else {
		sugared.Infof("DataTesterServer Exit")
	}
}
