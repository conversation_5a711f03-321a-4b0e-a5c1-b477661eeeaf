//go:build recovery && linux && amd64

package main

import (
	"context"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/global"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/metrics"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	dataTesterConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	dataTesterConfig.AppName = global.AppNameRecovery
	logger := log.NewLogger(dataTesterConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("RecoveryServer Start")

	errGroup, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	redisConfig, ok := dataTesterConfig.GetRedisConfig("prodBackup")
	if !ok {
		panic("prodBackup config not found")
	}
	redisClient := cache.NewRedisClient(redisConfig)
	pCtx, pCancel := context.WithTimeout(context.Background(), time.Second*2)
	err := redisClient.Ping(pCtx).Err()
	if err != nil {
		panic("RedisClient Error: " + err.Error())
	}
	pCancel()

	metrics.SetSugaredLogger(sugared)

	recoveryServer := http.NewRecoveryServer(dataTesterConfig.AppModel, dataTesterConfig.HttpConfig)
	recoveryServer.RegisterRedisClient(redisClient)
	recoveryServer.RegisterSugaredLogger(sugared)

	errGroup.Go(func() error {
		return recoveryServer.Start()
	})

	errGroup.Go(func() error {
		return metrics.Start(dataTesterConfig.Metrics.Addr)
	})

	errGroup.Go(func() error {
		<-shutdownCtx.Done()
		return metrics.Stop()
	})

	// 关闭信号
	shutSignal := make(chan os.Signal, 1)
	signal.Notify(shutSignal, syscall.SIGTERM, syscall.SIGINT)
	errGroup.Go(func() error {
		<-shutSignal
		sugared.Infof("Start Shutdown RecoveryServer")
		timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*2)
		defer timeoutCancel()
		err = recoveryServer.Stop(timeoutCtx)
		if err != nil {
			sugared.Errorf("RecoveryServer.Stop Error: %v", err)
		} else {
			sugared.Infof("RecoveryServer.Stop Success.")
		}
		shutdownCancel()
		return err
	})

	if err = errGroup.Wait(); err != nil {
		sugared.Errorf("errGroup.Wait Error: %v", err)
	} else {
		sugared.Infof("RecoveryServer Exit")
	}
}
