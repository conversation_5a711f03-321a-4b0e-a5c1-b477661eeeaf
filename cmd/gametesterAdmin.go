//go:build gametesterAdmin && linux && amd64

package main

import (
	"context"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/sync/errgroup"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/config"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/common"
	"hungrystudio.com/datatester/console"
	"hungrystudio.com/datatester/http"
	"hungrystudio.com/datatester/models"
	"hungrystudio.com/datatester/models/testergame"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	dataTesterServerConfig := common.NewDataTesterServerConfig("configs/app.yaml")
	logger := log.NewLogger(dataTesterServerConfig.Logger)
	sugared := logger.Sugar()

	sugared.Infof("DataTesterServer Start")

	http.SetSugaredLogger(sugared)
	models.SetSugaredLogger(sugared)
	testergame.SetSugaredLogger(sugared)

	adminConfig := models.GametesterAdminConfig{}
	err := config.ParseYamlOrJsonFile("configs/gametesterAdmin.yaml", &adminConfig)
	if err != nil {
		sugared.Errorf("Parse config file error: %s", err.Error())
	}
	var localRedis, grayRedis, prodRedis cache.RedisClient
	localConfig, ok := dataTesterServerConfig.GetRedisConfig(adminConfig.LocalRedis)
	if !ok {
		panic(fmt.Sprintf("LocalRedis: %s not Setting", adminConfig.LocalRedis))
	}
	localRedis = cache.NewRedisClient(localConfig)
	if err = cache.RedisPing(localRedis); err != nil {
		sugared.Errorf("localRedis ping error: %s", err.Error())
		panic(err)
	}

	//if adminConfig.GrayRedis == adminConfig.LocalRedis {
	//	grayRedis = localRedis
	//} else {
	//	grayConfig, ok := dataTesterServerConfig.GetRedisConfig(adminConfig.GrayRedis)
	//	if !ok {
	//		panic(fmt.Sprintf("GrayRedis: %s not Setting", adminConfig.GrayRedis))
	//	}
	//	grayRedis = cache.NewRedisClient(grayConfig)
	//	if err = cache.RedisPing(grayRedis); err != nil {
	//		sugared.Errorf("grayRedis ping error: %s", err.Error())
	//		panic(err)
	//	}
	//}
	grayRedis = localRedis

	switch adminConfig.ProdRedis {
	case adminConfig.LocalRedis:
		prodRedis = localRedis
	case adminConfig.GrayRedis:
		prodRedis = grayRedis
	default:
		prodConfig, ok := dataTesterServerConfig.GetRedisConfig(adminConfig.ProdRedis)
		if !ok {
			panic(fmt.Sprintf("ProdRedis: %s not Setting", adminConfig.ProdRedis))
		}
		prodRedis = cache.NewRedisClient(prodConfig)
		if err = cache.RedisPing(prodRedis); err != nil {
			sugared.Errorf("prodRedis ping error: %s", err.Error())
			panic(err)
		}
	}
	testergame.SetRedisClient(prodRedis)

	prodMysqlConfig, ok := dataTesterServerConfig.GetRDBConfig(adminConfig.ProdMysql)
	if !ok {
		sugared.Errorf("ProdMysql: %s not Setting", adminConfig.ProdMysql)
		panic(fmt.Sprintf("ProdMysql: %s not Setting", adminConfig.ProdMysql))
	}
	dbPassword := os.Getenv("PROD_DB_PASSWORD")
	if dbPassword == "" {
		sugared.Errorf("ENV PROD_DB_PASSWORD not Setting")
		panic("ENV PROD_DB_PASSWORD not Setting")
	}
	prodMysqlDB, err := common.NewXormEngineWithPassword(prodMysqlConfig, dbPassword)
	if err != nil {
		sugared.Errorf("NewXormEngine error: %s", err.Error())
		panic(err)
	}

	localMysqlConfig, ok := dataTesterServerConfig.GetRDBConfig(adminConfig.LocalMysql)
	if !ok {
		sugared.Errorf("ProdMysql: %s not Setting", adminConfig.ProdMysql)
		panic(fmt.Sprintf("ProdMysql: %s not Setting", adminConfig.ProdMysql))
	}
	localDBPassword := os.Getenv("LOCAL_DB_PASSWORD")
	if localDBPassword == "" {
		sugared.Errorf("ENV PROD_DB_PASSWORD not Setting")
		panic("ENV PROD_DB_PASSWORD not Setting")
	}
	localMysqlDB, err := common.NewXormEngineWithPassword(localMysqlConfig, localDBPassword)
	if err != nil {
		sugared.Errorf("NewXormEngine error: %s", err.Error())
		panic(err)
	}

	if len(os.Args) > 1 && os.Args[1] == "console" {
		console.SetLocalRedis(localRedis)
		console.SetProdRedis(prodRedis)
		console.ParseConsole()
		os.Exit(0)
	}
	gametesterAdmin := models.NewGametesterAdmin(localRedis, grayRedis, prodRedis)
	gametesterAdmin.SetProdMysql(prodMysqlDB)
	gametesterAdmin.SetLocalMysql(localMysqlDB)
	gametesterAdmin.SetAdminConfig(adminConfig)

	adminHttpServer := http.NewGameTesterAdmin("debug", dataTesterServerConfig.HttpConfig)
	adminHttpServer.SetGametesterAdmin(gametesterAdmin)
	adminHttpServer.SetLimitIPs(dataTesterServerConfig.LimitIps)

	// 多链路
	multiLinks := make(map[string]map[string]*testergame.LinksData)
	for bundle, envs := range adminConfig.MultiLinks {
		if _, ok := multiLinks[bundle]; !ok {
			multiLinks[bundle] = make(map[string]*testergame.LinksData)
		}
		for _, mEnv := range envs {
			switch mEnv {
			case testergame.EnvDev, testergame.EnvTesting, testergame.EnvGray:
				multiLinks[bundle][mEnv] = testergame.NewLinksData(localRedis, bundle, mEnv)
			case testergame.EnvProd:
				multiLinks[bundle][mEnv] = testergame.NewLinksData(prodRedis, bundle, mEnv)
			default:
				sugared.Errorf("Unknown env: %s", mEnv)
				panic("Unknown env: " + mEnv)
			}
		}
	}
	adminHttpServer.SetLinksData(multiLinks)
	
	group, ctx := errgroup.WithContext(context.Background())
	shutdownCtx, shutdownCancel := context.WithCancel(ctx)

	group.Go(func() error {
		return adminHttpServer.Start()
	})

	group.Go(func() error {
		gametesterAdmin.Monitor(shutdownCtx, adminConfig.Bundles, adminConfig.DDConfigs)
		return nil
	})

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	<-c
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = adminHttpServer.Stop(ctx)
	if err != nil {
		sugared.Errorf("Admin Server Stop Error: %s", err.Error())
	} else {
		sugared.Infof("Admin Server Stop Success")
	}
	shutdownCancel()
	err = group.Wait()
	if err != nil {
		sugared.Errorf("Admin Server Exit Error: %s", err.Error())
	} else {
		sugared.Infof("Admin Server Exit Success")
	}
}
