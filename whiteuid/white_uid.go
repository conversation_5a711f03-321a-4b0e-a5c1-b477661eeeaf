package whiteuid

import (
	"context"
	"time"

	"go.uber.org/zap"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/datatester/models/testergame"
)

var white_uid_map map[string]string
var redisClient cache.RedisClient
var sugared *zap.SugaredLogger

func SetSugaredLogger(l *zap.SugaredLogger) {
	sugared = l
}

func loadConfigs() {
	white_uid_map = make(map[string]string)
	newuids, err := redisClient.HGetAll(context.Background(), testergame.UserWhiteListCacheKey).Result()
	if err != nil {
		sugared.Errorf("whiteuid redisClient.Get(%q): %v", testergame.UserWhiteListCacheKey, err)
		return
	}
	white_uid_map = newuids
	sugared.Infof("whiteuid load white uid map: %v", newuids)
}
func update(updateInterval int64) {
	ticker := time.NewTicker(time.Minute * time.Duration(updateInterval))
	defer ticker.Stop()
	for range ticker.C {
		loadConfigs()
	}
}

func Init(updateInterval int64, c cache.RedisClient, l *zap.SugaredLogger) {
	redisClient = c
	sugared = l
	loadConfigs()
	go update(updateInterval)
}

func Check(uid string) bool {
	_, ok := white_uid_map[uid]
	return ok
}
