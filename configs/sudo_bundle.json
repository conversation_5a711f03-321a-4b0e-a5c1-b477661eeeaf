[{"bundleId": "com.hungrystudio.matchout.match3d", "platform": "gp", "tableId": "match", "minApiVersion": "v2", "updateInterval": 10, "testerAdWayNums": {"v14": []}, "adsConfigFilters": ["roas_max", "roas_is", "country_code", "country_code2", "country_code3", "country_code4", "default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "2478fd85bdf82411f1f70136cb380073", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int", "applovin_int", "ironsource_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "", "rt_info": [], "is_fb_open": "1", "is_firebase_open": "1", "hs_country_code": "FR", "is_eea": "0", "is_open_business_serverab_by_adwaynum": "1"}, "adInfoDefault": {"adwaynum": "9999", "adplatform": "3", "abtest": "", "is_first": "0", "ab_live": "1", "banner": {}}, "adConfigs": {"roas_max": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["applovin_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}, "roas_is": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["ironsource_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "com.mathbrain.sudoku", "platform": "gp", "tableId": "sudu", "minApiVersion": "v14", "updateInterval": 10, "testerAdWayNums": {"v14": []}, "adsConfigFilters": ["roas_max", "roas_is", "country_code", "country_code2", "country_code3", "country_code4", "default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "2478fd85bdf82411f1f70136cb380073", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int", "applovin_int", "ironsource_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "", "rt_info": [], "is_fb_open": "1", "is_firebase_open": "1", "hs_country_code": "FR", "is_eea": "0", "is_open_business_serverab_by_adwaynum": "1"}, "adInfoDefault": {"adwaynum": "9999", "adplatform": "3", "abtest": "", "is_first": "0", "ab_live": "1", "banner": {}}, "adConfigs": {"roas_max": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["applovin_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}, "roas_is": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["ironsource_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "com.mathbrain.sudoku.ios", "platform": "ios", "tableId": "su<PERSON><PERSON>", "minApiVersion": "v6", "updateInterval": 10, "testerAdWayNums": {"v6": []}, "adsConfigFilters": ["roas_max", "roas_is", "country_code", "country_code2", "country_code3", "country_code4", "default", "ad_info"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "2478fd85bdf82411f1f70136cb380073", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int", "applovin_int", "ironsource_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "", "rt_info": [], "is_fb_open": "1", "is_firebase_open": "1", "hs_country_code": "FR", "is_eea": "0", "is_open_business_serverab_by_adwaynum": "1"}, "adInfoDefault": {"adwaynum": "9999", "adplatform": "3", "abtest": "", "is_first": "0", "ab_live": "1", "banner": {}}, "adConfigs": {"roas_max": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["applovin_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}, "roas_is": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["ironsource_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}]