appModel: "release"
appName: "gametesterMonitor"
logger:
  - level: -1
    lj_logger:
      filename: "./logs/gametesterMonitor.log"
      maxsize: 500
      maxage: 7
      maxbackups: 10
      localtime: true
      compress: true
  - level: 2
    lj_logger:
      filename: "./logs/gametesterMonitor-errors.log"
      maxsize: 500
      maxage: 14
      maxbackups: 20
      localtime: true
      compress: true
metrics:
  addr: ":8002"
eventLog:
  filename: "logs/event.log"
  maxsize: 500
  maxage: 3
  maxbackups: 10
  localtime: true
  compress: false
rdbs:
#  xxgame_aws_slt:
#    master: "xxgame_aws_slt:%s@tcp(xxgame-mysql8.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#    slaves:
#      - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave1.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#      - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave2.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#      - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave3.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#  xxgame_aws_slt_local:
#    master: "root:%s@tcp(3.136.90.70:3306)/xxgame_aws_sync?charset=utf8mb4"
#  gametester:
#    master: "user-gametester-slt:%s@tcp(xxgame-version-release-aurora.cluster-cjmimqdxiiih.us-east-2.rds.amazonaws.com:3306)/gametester?charset=utf8mb4"
#    slaves:
#      - "user-gametester-slt:%s@tcp(xxgame-version-release-aurora.cluster-ro-cjmimqdxiiih.us-east-2.rds.amazonaws.com:3306)/gametester?charset=utf8mb4"
#  gametester_local:
#    master: "root:%s@tcp(127.0.0.1:3306)/gametester?charset=utf8mb4"
  local:
    passwdENVName: DB_PASSWORD
    master: "root:%s@tcp(127.0.0.1:3306)/gametester_log?charset=utf8mb4"
redisCaches:
#  prod:
#    addr:
#      - "127.0.0.1:6379"
#    db: 0
#    isCluster: false
#  prodBackup:
#    addr:
#      - "127.0.0.1:6379"
#    db: 0
#    isCluster: false
#  localBackup:
#    addr: [ "127.0.0.1:6379" ]
#    db: 0
#    isCluster: false
  local:
    addr: [ "127.0.0.1:6379" ]
    db: 0
    isCluster: false
