appModel: "release"
appName: "all"
httpConfig:
  addr: ":8001"
  sockFile: "datatester.sock"
  loggerWriter:
    filename: "logs/datatester-gin.log"
    maxsize: 200
    maxage: 7
    maxbackups: 10
    localtime: true
    compress: true
adminHttpConfig:
  addr: ":8003"
  sockFile: "datatester.sock"
  loggerWriter:
    filename: "logs/datatester-gin.log"
    maxsize: 200
    maxage: 7
    maxbackups: 10
    localtime: true
    compress: true
logger:
  - level: -1
    lj_logger:
      filename: "./logs/datatester.log"
      maxsize: 500
      maxage: 7
      maxbackups: 10
      localtime: true
      compress: true
  - level: 2
    lj_logger:
      filename: "./logs/datatester-errors.log"
      maxsize: 500
      maxage: 14
      maxbackups: 20
      localtime: true
      compress: true
ipDataUrl: "http://fingerprint-test.afafb.com/adfs/IP_city_single_WGS84.awdb"
metrics:
  addr: ":8002"
eventLog:
  filename: "logs/event.log"
  maxsize: 500
  maxage: 3
  maxbackups: 10
  localtime: true
  compress: false
abLogWriter:
  filename: "./logs/huoshan-ab.log"
  maxsize: 500
  maxage: 7
  maxbackups: 10
  localtime: true
  compress: true
bundleAddr: "data/bundle.json"
#dbConfigs:
#  master: "xxgame_aws_slt:%s@tcp(xxgame-mysql8.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#  slaves:
#    - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave1.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#    - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave2.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
#    - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave3.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
dbConfigs:
  master: "root:%s@tcp(***********:3306)/xxgame_aws?charset=utf8mb4"
useDBGameTester: true
dbGameTester:
  master: "root:%s@tcp(**********:3306)/gametester?charset=utf8mb4"
redisConfig:
  addr: ["127.0.0.1:6379"]
  db: 0
  isCluster: false
limitIps: ["**************", "************"]
rdbs:
  xxgame_aws_slt:
    master: "xxgame_aws_slt:%s@tcp(xxgame-mysql8.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
    slaves:
      - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave1.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
      - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave2.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
      - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave3.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
  xxgame_aws_slt_local:
    master: "root:%s@tcp(***********:3306)/xxgame_aws_sync?charset=utf8mb4"
  gametester:
    master: "user-gametester-slt:%s@tcp(xxgame-version-release-aurora.cluster-cjmimqdxiiih.us-east-2.rds.amazonaws.com:3306)/gametester?charset=utf8mb4"
    slaves:
      - "user-gametester-slt:%s@tcp(xxgame-version-release-aurora.cluster-ro-cjmimqdxiiih.us-east-2.rds.amazonaws.com:3306)/gametester?charset=utf8mb4"
  gametester_local:
    master: "root:%s@tcp(127.0.0.1:3306)/gametester?charset=utf8mb4"
redisCaches:
  prod:
    addr:
      - "127.0.0.1:6379"
    db: 0
    isCluster: false
  prodBackup:
    addr:
      - "127.0.0.1:6379"
    db: 0
    isCluster: false
  local:
    addr: [ "127.0.0.1:6379" ]
    db: 0
    isCluster: false
  localBackup:
    addr: [ "127.0.0.1:6379" ]
    db: 0
    isCluster: false
adminConfig:
  rdb: xxgame_aws_slt_local
  redisCaches: local
  useRedisCacheBackup: true
  redisCacheBackup: localBackup
gameTesterConfig:
  qa: false
  updateInterval: 10
  userRDB: gametester_local
  userCache: local
defendConfig:
  updateInterval: 10
  useCacheRewrite: false
  rdb: xxgame_aws_slt_local
  redisCache: local
businessConfig:
  updateInterval: 10
  rdb: xxgame_aws_slt_local
  qaEnable: true
kafkaRemote: true
kafkaOption:
  addr:
    - "b-2.abtestkafkaprod0109.ymyegv.c3.kafka.us-east-2.amazonaws.com:9094"
    - "b-3.abtestkafkaprod0109.ymyegv.c3.kafka.us-east-2.amazonaws.com:9094"
    - "b-1.abtestkafkaprod0109.ymyegv.c3.kafka.us-east-2.amazonaws.com:9094"
  poolSize: 5000
  enabledTLS: true
  enabledIAM: false
  enabledCompress: true
