localRedis: local
grayRedis: local
prodRedis: prodBackup
prodMysql: gametester
localMysql: gametester_local
validate:
  leastCount: 30
  miniDiffCount: 10
refactor2:
  testingUploadApi: "http://10.0.7.244:18001/save_configs"
  prodUploadApi: "https://new-traffic.afafb.com/save_configs"
bundles: ["com.block.juggle", "com.blockpuzzle.us.ios", "com.hungrystudio.block.puzzle.fun.brain.free"]
useInterceptor: true,
overScoreInterceptorRedis: "local",
overScoreInterceptor:
  name: "overScore"
  enabled: true,
  redirectBucket:
    bucket: 800001

dd_configs:
  com.block.juggle:
    - api: "https://oapi.dingtalk.com/robot/send?access_token=bd545edf4058cfd8d074a234ef6acb278a3d84a2412f148bc0fa6089c892dd97"
      at: ["13121336899"]
      key_content: "服务监控\n"
    - api: "https://oapi.dingtalk.com/robot/send?access_token=670750041d6d5aa997249d03dd479142ee379bee0a72eb885eecf5d010580fff"
      at: ["18611564115"]
      key_content: "服务监控\n"
    - api: "https://oapi.dingtalk.com/robot/send?access_token=ca85a7ad5d768f837b223079d9259ad93795374e2e8eefe8985a51f1ec4d9e71"
      at: []
      key_content: "自动\n"
    - api: "https://oapi.dingtalk.com/robot/send?access_token=a6bc7c1adce516493cdf489dde9b4a98c2bf6a467fa7eeaba11c8aaf7f1774a0"
      at: ["18501997733"]
      key_content: "服务监控\n"

multilLinks:
  com.blockpuzzle.us.ios: ["dev", "testing", "gray", "prod"]