[{"bundleId": "com.block.juggle", "platform": "gp", "tableId": "jugg", "minApiVersion": "v46", "updateInterval": 10, "testerAdWayNums": {"v46": []}, "adsConfigFilters": ["preinstall", "roas", "roas_limit", "roas_limit2", "country_code", "country_code2", "country_code3", "country_code4", "default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "1d8c51002e88d2a734530ef53b8049cc", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "532118490", "msgdata": [], "rt_planId": "no", "rt_planInfo": [], "rt_rnew": "no", "is_open_facebook": "1", "is_data_tester": "0", "is_open_push": "1", "is_open_HSAnalyticsSDK": "1", "opr": "k", "game_send_data": "1", "ip_country": "FR", "hs_ip": "***********", "hs_country_code": "FR", "is_eea": "1", "is_fail_eea": "0", "is_open_taichi": "1", "is_open_new_report": "1", "is_open_business_serverab_by_adwaynum": "1", "is_open_new_selectiveinit": "1", "is_open_add_ad_load": "0", "is_upload_device_info": "1", "is_open_hs_push": "0", "is_data_tester_dynamic": "0", "is_open_glthread_priority": "0", "is_open_s2s": "1", "is_open_opt": "1", "is_open_sub": "0"}, "adInfoDefault": {"adwaynum": "9999", "ab_live": "1", "banner_events": "s_ab_banner_revenue_3570", "adplatform": "max", "abtest": "bx3803", "banner_adunit": "7e2ecedd10ef44fd", "banner_interval": [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.5, 3, 3.5, 4, 4.5, 5], "insert_adunit": "7526997d3b69a67f", "reward_adunit": "22832d34c0ef7a8f", "is_first_status": "open"}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "com.blockpuzzle.us.ios", "platform": "ios", "tableId": "ios", "minApiVersion": "v33", "updateInterval": 10, "testerAdWayNums": {"v33": []}, "adsConfigFilters": ["roas_max", "roas_is", "default", "ad_info"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "2478fd85bdf82411f1f70136cb380073", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int", "applovin_int", "ironsource_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "", "rt_info": [], "is_fb_open": "1", "is_firebase_open": "1", "hs_country_code": "FR", "is_eea": "0", "is_open_business_serverab_by_adwaynum": "1"}, "adInfoDefault": {"adwaynum": "9999", "adplatform": "3", "abtest": "", "is_first": "0", "ab_live": "1", "banner": {}}, "adConfigs": {"roas_max": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["applovin_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}, "roas_is": {"keys": ["campaign", "media_source"], "filters": [{"conditions": [{"key": "media_source", "value": ["ironsource_int"], "op": "in", "type": "string", "logic_operator": "&&"}, {"key": "campaign", "value": "ROAS", "op": "contain", "type": "string", "logic_operator": "&&"}], "logic_operator": "&&"}]}}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "solitaire.classic.hungrystudio.free.klondike.card.patience", "platform": "gp", "tableId": "card", "minApiVersion": "v46", "updateInterval": 10}, {"bundleId": "solitaire.hungrystudio.freecard", "platform": "ios", "tableId": "cardios", "minApiVersion": "v46", "updateInterval": 10}, {"bundleId": "com.mathbrain.sudoku", "platform": "gp", "tableId": "sudo", "minApiVersion": "v46", "updateInterval": 10}, {"bundleId": "com.mathbrain.sudoku.ios", "platform": "ios", "tableId": "sudo<PERSON>", "minApiVersion": "v46", "updateInterval": 10}, {"bundleId": "com.hungrystudio.matchout.match3d", "platform": "gp", "tableId": "match", "minApiVersion": "v46", "updateInterval": 10}, {"bundleId": "om.hungrystudio.matchout", "platform": "ios", "tableId": "match", "minApiVersion": "v46", "updateInterval": 10}, {"bundleId": "com.hungrystudio.blockjourney", "platform": "gp", "tableId": "journeygp", "minApiVersion": "v46", "updateInterval": 10}]