appModel: "debug"
httpConfig:
  addr: ":8001"
  sockFile: "datatester.sock"
  loggerWriter:
    filename: "logs/datatester-gin.log"
    maxsize: 200
    maxage: 7
    maxbackups: 10
    localtime: true
    compress: true
logger:
  - level: -1
    lj_logger:
      filename: "./logs/datatester.log"
      maxsize: 500
      maxage: 7
      maxbackups: 10
      localtime: true
      compress: true
  - level: 2
    lj_logger:
      filename: "./logs/datatester-errors.log"
      maxsize: 500
      maxage: 14
      maxbackups: 20
      localtime: true
      compress: true
ipDataUrl: "http://fingerprint-test.afafb.com/adfs/IP_city_single_WGS84.awdb"
metrics:
  addr: ":8002"
abLogWriter:
  filename: "./logs/huoshan-ab.log"
  maxsize: 500
  maxage: 7
  maxbackups: 10
  localtime: true
  compress: true
bundleAddr: "configs/bundle.json"
dbConfigs:
  master: "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave1.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
  slaves:
    - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave2.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
    - "xxgame_aws_slt:%s@tcp(xxgame-mysql8-slave3.ca7epp5tjpuv.us-east-2.rds.amazonaws.com:3306)/xxgame_aws?charset=utf8mb3"
etcdConfig:
  endpoints: ["**********:2379"]
  auto-sync-interval: 0
  dial-timeout: 0
  dial-keep-alive-time: 0
  dial-keep-alive-timeout: 0
redisConfig:
  addr: ["***********:7001","**********:7001","**********:7001"]
  db: 0
  password: "7VKLFHRfydoprbsd"
  isCluster: true
csrParamRedis:
  addr: [ "**********:6379" ]
  db: 0
  isCluster: false