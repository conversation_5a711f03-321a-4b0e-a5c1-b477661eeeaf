[{"bundleId": "com.block.juggle", "csr": {"is_open": true, "indentity_keys": ["ecpm", "adnum", "gamenum"], "indentity_configs": {"ecpm": {"kind": "range", "value": [0, 0.00021, 0.0002967, 0.0003724, 0.0004446, 0.000514, 0.0005894, 0.0006684, 0.0007515, 0.0008391, 0.0009347, 0.001031, 0.0011398, 0.0012551, 0.0013797, 0.001513, 0.0016575, 0.0018141, 0.0019839, 0.0021624, 0.0023703, 0.0026064, 0.0028781, 0.0031793, 0.0035563, 0.0040076, 0.0045884, 0.0053433, 0.0063486, 0.0077535, 0.0098577, 0.0133232, 0.0203356, 0.0537825]}, "adnum": {"kind": "range", "value": [0, 0.14, 0.2, 0.33, 0.43, 0.5, 0.67, 0.86, 1.0, 1.14, 1.4, 1.6, 2.0, 2.14, 2.5, 3.0, 3.29, 3.75, 4.29, 5.0, 5.6, 6.43, 7.43, 8.57, 10.0, 11.57, 13.67, 16.4, 20.0, 25.6, 36.0, 405.86]}, "gamenum": {"kind": "range", "value": [0, 0.14, 1.0, 1.2, 1.67, 2.0, 2.5, 3.0, 3.2, 3.67, 4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0, 7.75, 8.33, 9.0, 9.8, 10.57, 11.43, 12.33, 13.33, 14.43, 15.67, 17.0, 18.57, 20.33, 22.43, 25.0, 28.14, 32.33, 38.57, 50.0, 1534.71]}}}, "platform": "gp", "tableId": "jugg", "minApiVersion": "v46", "updateInterval": 10, "testerAdWayNums": {"v46": []}, "adsConfigFilters": ["preinstall", "roas", "roas_limit", "roas_limit2", "country_code", "country_code2", "country_code3", "country_code4", "default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "1d8c51002e88d2a734530ef53b8049cc", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "532118490", "msgdata": [], "rt_planId": "no", "rt_planInfo": [], "rt_rnew": "no", "is_open_facebook": "1", "is_data_tester": "0", "is_open_push": "1", "is_open_HSAnalyticsSDK": "1", "opr": "k", "game_send_data": "1", "ip_country": "FR", "hs_ip": "***********", "hs_country_code": "FR", "is_eea": "1", "is_fail_eea": "0", "is_open_taichi": "1", "is_open_new_report": "1", "is_open_business_serverab_by_adwaynum": "1", "is_open_new_selectiveinit": "1", "is_open_add_ad_load": "0", "is_upload_device_info": "1", "is_open_hs_push": "0", "is_data_tester_dynamic": "0", "is_open_glthread_priority": "0", "is_open_s2s": "1", "is_open_opt": "1", "is_open_sub": "0"}, "adInfoDefault": {"adwaynum": "9999", "ab_live": "1", "banner_events": "s_ab_banner_revenue_3570", "adplatform": "max", "abtest": "bx3803", "banner_adunit": "7e2ecedd10ef44fd", "banner_interval": [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.5, 3, 3.5, 4, 4.5, 5], "insert_adunit": "7526997d3b69a67f", "reward_adunit": "22832d34c0ef7a8f", "is_first_status": "open"}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "com.blockpuzzle.us.ios", "csr": {"is_open": true, "indentity_keys": ["ecpm", "adnum", "gamenum"], "indentity_configs": {"ecpm": {"kind": "range", "value": [0, 0.00021, 0.0002967, 0.0003724, 0.0004446, 0.000514, 0.0005894, 0.0006684, 0.0007515, 0.0008391, 0.0009347, 0.001031, 0.0011398, 0.0012551, 0.0013797, 0.001513, 0.0016575, 0.0018141, 0.0019839, 0.0021624, 0.0023703, 0.0026064, 0.0028781, 0.0031793, 0.0035563, 0.0040076, 0.0045884, 0.0053433, 0.0063486, 0.0077535, 0.0098577, 0.0133232, 0.0203356, 0.0537825]}, "adnum": {"kind": "range", "value": [0, 0.14, 0.2, 0.33, 0.43, 0.5, 0.67, 0.86, 1.0, 1.14, 1.4, 1.6, 2.0, 2.14, 2.5, 3.0, 3.29, 3.75, 4.29, 5.0, 5.6, 6.43, 7.43, 8.57, 10.0, 11.57, 13.67, 16.4, 20.0, 25.6, 36.0, 405.86]}, "gamenum": {"kind": "range", "value": [0, 0.14, 1.0, 1.2, 1.67, 2.0, 2.5, 3.0, 3.2, 3.67, 4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0, 7.75, 8.33, 9.0, 9.8, 10.57, 11.43, 12.33, 13.33, 14.43, 15.67, 17.0, 18.57, 20.33, 22.43, 25.0, 28.14, 32.33, 38.57, 50.0, 1534.71]}}}, "platform": "gp", "tableId": "jugg", "minApiVersion": "v46", "updateInterval": 10, "testerAdWayNums": {"v46": []}, "adsConfigFilters": ["preinstall", "roas", "roas_limit", "roas_limit2", "country_code", "country_code2", "country_code3", "country_code4", "default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "1d8c51002e88d2a734530ef53b8049cc", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "532118490", "msgdata": [], "rt_planId": "no", "rt_planInfo": [], "rt_rnew": "no", "is_open_facebook": "1", "is_data_tester": "0", "is_open_push": "1", "is_open_HSAnalyticsSDK": "1", "opr": "k", "game_send_data": "1", "ip_country": "FR", "hs_ip": "***********", "hs_country_code": "FR", "is_eea": "1", "is_fail_eea": "0", "is_open_taichi": "1", "is_open_new_report": "1", "is_open_business_serverab_by_adwaynum": "1", "is_open_new_selectiveinit": "1", "is_open_add_ad_load": "0", "is_upload_device_info": "1", "is_open_hs_push": "0", "is_data_tester_dynamic": "0", "is_open_glthread_priority": "0", "is_open_s2s": "1", "is_open_opt": "1", "is_open_sub": "0"}, "adInfoDefault": {"adwaynum": "9999", "ab_live": "1", "banner_events": "s_ab_banner_revenue_3570", "adplatform": "max", "abtest": "bx3803", "banner_adunit": "7e2ecedd10ef44fd", "banner_interval": [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.5, 3, 3.5, 4, 4.5, 5], "insert_adunit": "7526997d3b69a67f", "reward_adunit": "22832d34c0ef7a8f", "is_first_status": "open"}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "com.mathbrain.sudoku", "platform": "gp", "tableId": "sudu", "minApiVersion": "v14", "updateInterval": 10, "testerAdWayNums": {"v14": []}, "adsConfigFilters": ["default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "2478fd85bdf82411f1f70136cb380073", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int", "applovin_int", "ironsource_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "", "rt_info": [], "is_fb_open": "1", "is_firebase_open": "1", "hs_country_code": "FR", "is_eea": "0", "is_open_business_serverab_by_adwaynum": "1"}, "adInfoDefault": {"adwaynum": "9999", "adplatform": "3", "abtest": "", "is_first": "0", "ab_live": "1", "banner": {}}, "adConfigs": {}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}, {"bundleId": "com.mathbrain.sudoku.ios", "platform": "ios", "tableId": "su<PERSON><PERSON>", "minApiVersion": "v6", "updateInterval": 10, "testerAdWayNums": {"v6": []}, "adsConfigFilters": ["default"], "countryList": ["US", "BR", "KR", "DE", "IN", "JP", "MX", "GB", "ID", "FR", "AU", "CA", "TH", "IT", "ES"], "areaMappings": {"EA": {"name": "EA", "paramKey": "is_eea", "countries": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NO", "PL", "PT", "RO", "SK", "SI", "SE", "GB", "IS", "LI", "NL", "ES"]}}, "abTesterConfig": {"appKey": "2478fd85bdf82411f1f70136cb380073", "mediaSources": ["digitalturbine_int", "xiaomipai_int", "vivootapreload_int", "oppopaipreinstall_int", "aura_int", "shalltrypai_int", "vivopreload_int", "xiaomipreload_int", "applovin_int", "ironsource_int"], "mappingCountryFields": ["max_country_code", "server_hs_ip_country", "hs_ip_country"]}, "switch": {"jyid": "", "rt_info": [], "is_fb_open": "1", "is_firebase_open": "1", "hs_country_code": "FR", "is_eea": "0", "is_open_business_serverab_by_adwaynum": "1"}, "adInfoDefault": {"adwaynum": "9999", "adplatform": "3", "abtest": "", "is_first": "0", "ab_live": "1", "banner": {}}, "adConfigs": {}, "newFilingSequence": ["country", "country_group", "model", "model_group", "manufacturer", "manufacturer_group", "start_time", "start_time_group", "ramsize", "ramsize_group", "memsize", "memsize_group", "network", "network_group"], "dynamicFilingSequence": ["active_day", "active_day_group", "game_duration", "game_duration_group", "ecpm", "ecpm_group", "ad_num", "ad_num_group", "game_num", "game_num_group", "game_revenue", "game_revenue_group", "media_source", "media_source_group", "campaign", "campaign_group", "adset", "adset_group", "ad", "ad_group"]}]