appModel: "debug"
httpConfig:
  addr: ":8001"
  sockFile: "datatester.sock"
  loggerWriter:
    filename: "logs/datatester-gin.log"
    maxsize: 200
    maxage: 7
    maxbackups: 10
    localtime: true
    compress: true
logger:
  - level: -1
    lj_logger:
      filename: "./logs/datatester.log"
      maxsize: 500
      maxage: 7
      maxbackups: 10
      localtime: true
      compress: true
  - level: 2
    lj_logger:
      filename: "./logs/datatester-errors.log"
      maxsize: 500
      maxage: 14
      maxbackups: 20
      localtime: true
      compress: true
#ipDataUrl: "http://fingerprint-test.afafb.com/adfs/IP_city_single_WGS84.awdb"
ipDataUrl: "http://testadmarket.com/static/download/ip-data3439530535"
metrics:
  addr: ":8002"
abLogWriter:
  filename: "./logs/huoshan-ab.log"
  maxsize: 500
  maxage: 7
  maxbackups: 10
  localtime: true
  compress: true
bundleAddr: "data/bundle.json"
dbConfigs:
  master: "root:%s@tcp(127.0.0.1:3306)/xxgame_aws?charset=utf8mb3"
  slaves:
    - "root:%s@tcp(127.0.0.1:3306)/xxgame_aws?charset=utf8mb3"
    - "root:%s@tcp(127.0.0.1:3306)/xxgame_aws?charset=utf8mb3"
    - "root:%s@tcp(127.0.0.1:3306)/xxgame_aws?charset=utf8mb3"
redisConfig:
  addr: ["127.0.0.1:6379"]
  db: 0
  isCluster: false
csrParamRedis:
  addr: ["127.0.0.1:6379"]
  db: 0
  isCluster: false
didi:
  api: "https://blockbalance-sc.afafb.com/v1/didi_split"
  timeout: 500
