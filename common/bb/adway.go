package bb

import (
	"encoding/json"
	"fmt"
)

//type AbWayConfig struct {
//	Conditions      []any  `json:"conditions"`
//	Features        []any  `json:"features"`
//	AbWayNum        int    `json:"abWayNum"`
//	AbWay           string `json:"abWay"`
//	Mark            any    `json:"mark"`
//	HotState        any    `json:"hotState"`
//	ActiveDays      any    `json:"activeDays"`
//	ResVersion      any    `json:"resVersion"`
//	AbWayFilters    []any  `json:"abWayFilters"`
//	AbWayPlanNum    any    `json:"abWayPlanNum"`
//	CombinationMark []any  `json:"combinationMark"`
//	CurrentMark     any    `json:"currentMark"`
//	Rate            int    `json:"rate"`
//	Vid             any    `json:"vid"`
//}

type AbWayConfig map[string]any

type AbWayMaps map[string]AbWayConfig

func ParseAbwayMaps(abWayJson []byte) (AbWayMaps, error) {
	abWaySlices := make([]AbWayConfig, 0)
	err := json.Unmarshal(abWayJson, &abWaySlices)
	if err != nil {
		return nil, err
	}
	abWayMaps := make(AbWayMaps)
	for _, abWay := range abWaySlices {
		abWayNum, ok := abWay["abWayNum"]
		if !ok {
			return nil, fmt.Errorf("abWayNum not found in abWay")
		}
		abWayValue, ok := abWay["abWay"]
		if !ok {
			return nil, fmt.Errorf("abWay not found in abWay")
		}
		key := fmt.Sprintf("%v_%v", abWayNum, abWayValue)
		abWayMaps[key] = abWay
	}
	return abWayMaps, nil
}
