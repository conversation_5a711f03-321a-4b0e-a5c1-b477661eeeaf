package common

import (
	v3 "go.etcd.io/etcd/client/v3"
	"gopkg.in/natefinch/lumberjack.v2"
	"hungrystudio.com/core/cache"
	"hungrystudio.com/core/config"
	"hungrystudio.com/core/log"
	"hungrystudio.com/datatester/models/admin"
	"hungrystudio.com/datatester/models/business"
	"hungrystudio.com/datatester/models/defend"
)

type KafkaOption struct {
	Addr            []string `yaml:"addr" json:"addr"`
	PoolSize        int      `yaml:"poolSize" json:"poolSize"`
	EnabledTLS      bool     `yaml:"enabledTLS" json:"enabledTLS"`
	CA              string   `yaml:"ca" json:"ca"`
	EnabledIAM      bool     `yaml:"enabledIAM" json:"enabledIAM"`
	EnabledCompress bool     `yaml:"enabledCompress" json:"enabledCompress"`
}

type DataTesterServerConfig struct {
	AppRoot         string
	AppName         string                       `yaml:"appName"`
	AppModel        string                       `yaml:"appModel"`
	HttpConfig      config.HttpServerConfig      `yaml:"httpConfig"`
	AdminHttpConfig config.HttpServerConfig      `yaml:"adminHttpConfig"`
	Logger          []log.ZapLoggerConfig        `yaml:"logger"`
	IPDataUrl       string                       `yaml:"ipDataUrl"`
	Metrics         config.Metrics               `yaml:"metrics"`
	BundleAddr      string                       `yaml:"bundleAddr"`
	EventLog        *lumberjack.Logger           `yaml:"eventLog"`        // 应用埋点日志数据
	AbLogWriter     *lumberjack.Logger           `yaml:"abLogWriter"`     // 火山ab测试sdk接入运行日志
	DBConfigs       DBConfigs                    `yaml:"dbConfigs"`       // 数据库配置
	RDBs            map[string]DBConfigs         `yaml:"rdbs"`            //数据库配置集合，按键值索引
	UseDBGameTester bool                         `yaml:"useDBGameTester"` // 是否启用DBGameTester
	DBGameTester    DBConfigs                    `yaml:"dbGameTester"`    // gametester数据配置
	EtcdConfig      v3.Config                    `yaml:"etcdConfig"`      // etcd服务器配置
	RedisConfig     cache.RedisConfig            `yaml:"redisConfig"`     // redis配置
	CsrParamRedis   cache.RedisConfig            `yaml:"csrParamRedis"`   // 数仓提供的csr参数的redis配置
	RedisCaches     map[string]cache.RedisConfig `yaml:"redisCaches"`     // redis配置集合，按键值索引
	LimitIps        []string                     `yaml:"limitIps"`        // ip限制
	AdminConfig     admin.Config                 `yaml:"adminConfig"`     // admin配置
	//GameTesterConfig testergame.TesterTrafficConfig `yaml:"gameTesterConfig"` // gametester应用配置
	DefendConfig   defend.Config   `yaml:"defendConfig"`   // defend应用配置
	BusinessConfig business.Config `yaml:"businessConfig"` // business应用配置
	KafkaRemote    bool            `yaml:"kafkaRemote" json:"kafkaRemote"`
	KafkaOption    KafkaOption     `yaml:"kafkaOption" json:"kafkaOption"`
}

func NewDataTesterServerConfig(filename string) *DataTesterServerConfig {
	c := &DataTesterServerConfig{}
	c.Parse(filename)
	return c
}

func (tConfig *DataTesterServerConfig) Parse(filename string) {
	err := config.ParseConfig(tConfig, filename)
	if err != nil {
		panic(err)
	}
}

func (tConfig *DataTesterServerConfig) SetAppRoot(appRoot string) {
	tConfig.AppRoot = appRoot
}

func (tConfig *DataTesterServerConfig) GetRDBConfig(dbname string) (DBConfigs, bool) {
	if c, ok := tConfig.RDBs[dbname]; ok {
		return c, true
	}
	return DBConfigs{}, false
}

func (tConfig *DataTesterServerConfig) GetRedisConfig(cacheName string) (cache.RedisConfig, bool) {
	if c, ok := tConfig.RedisCaches[cacheName]; ok {
		return c, true
	}
	return cache.RedisConfig{}, false
}
