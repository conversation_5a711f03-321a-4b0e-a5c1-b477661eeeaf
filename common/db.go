package common

import (
	"fmt"
	"os"
	"time"
	"xorm.io/xorm"
)

type DBConfigs struct {
	PasswdENVName   string        `yaml:"passwdENVName" json:"passwdENVName"`
	UsePoolSetting  bool          `yaml:"usePoolSetting" json:"usePoolSetting"`
	MaxOpenConns    int           `yaml:"maxOpenConns" json:"maxOpenConns"`       // 最大链接数量
	MaxIdleConns    int           `yaml:"maxIdleConns" json:"maxIdleConns"`       // 最大空闲链接
	ConnMaxLifetime time.Duration `yaml:"connMaxLifetime" json:"connMaxLifetime"` // 链接最长重用时长 单位分钟
	Master          string        `yaml:"master" json:"master"`
	Slaves          []string      `yaml:"slaves" json:"slaves"`
}

// NewXormEngine 新建xorm.EngineInterface 对象
func NewXormEngine(dbConfigs DBConfigs) (xorm.EngineInterface, error) {
	// mysql 密码存储在环境变量DB_PASSWORD中
	dbPassword := os.Getenv("DB_PASSWORD")
	if dbPassword == "" {
		return nil, fmt.Errorf("ENV DB_PASSWORD not set")
	}
	var engine xorm.EngineInterface
	var err error
	if dbConfigs.Master != "" {
		if len(dbConfigs.Slaves) > 0 {
			engine, err = NewXormEngineGroup(dbConfigs, dbPassword)
		} else {
			engine, err = NewXormEngineSingle(dbConfigs.Master, dbPassword)
		}
		if err != nil {
			return engine, err
		}
		if dbConfigs.UsePoolSetting {
			engine.SetMaxIdleConns(dbConfigs.MaxIdleConns)
			engine.SetMaxOpenConns(dbConfigs.MaxOpenConns)
			engine.SetConnMaxLifetime(dbConfigs.ConnMaxLifetime * time.Second)
		}
		return engine, nil
	} else {
		var dataSource string
		for _, slaveSource := range dbConfigs.Slaves {
			if slaveSource != "" {
				engine, err = NewXormEngineSingle(dataSource, dbPassword)
				if err == nil {
					return engine, nil
				}
			}
		}
	}
	return nil, fmt.Errorf("dbconfigs datasource invalid")
}

// NewXormEngineWithPassword 新建xorm.EngineInterface 对象
func NewXormEngineWithPassword(dbConfigs DBConfigs, dbPassword string) (xorm.EngineInterface, error) {
	var engine xorm.EngineInterface
	var err error
	if dbConfigs.Master != "" {
		if len(dbConfigs.Slaves) > 0 {
			engine, err = NewXormEngineGroup(dbConfigs, dbPassword)
		} else {
			engine, err = NewXormEngineSingle(dbConfigs.Master, dbPassword)
		}
		if err != nil {
			return engine, err
		}
		if dbConfigs.UsePoolSetting {
			engine.SetMaxIdleConns(dbConfigs.MaxIdleConns)
			engine.SetMaxOpenConns(dbConfigs.MaxOpenConns)
			engine.SetConnMaxLifetime(dbConfigs.ConnMaxLifetime * time.Minute)
		}
		return engine, nil
	} else {
		var dataSource string
		for _, slaveSource := range dbConfigs.Slaves {
			if slaveSource != "" {
				engine, err = NewXormEngineSingle(dataSource, dbPassword)
				if err == nil {
					return engine, nil
				}
			}
		}
	}
	return nil, fmt.Errorf("dbconfigs datasource invalid")
}

// NewGameTesterXormEngine 新建xorm.EngineInterface 对象
func NewGameTesterXormEngine(dbConfigs DBConfigs) (xorm.EngineInterface, error) {
	// mysql 密码存储在环境变量DB_PASSWORD中
	//dbPassword := os.Getenv("DB_PASSWORD") // 数独csr使用
	dbPassword := os.Getenv("DB_PASSWORD_GAMETESTER")
	if dbPassword == "" {
		return nil, fmt.Errorf("ENV DB_PASSWORD_GAMETESTER not set")
	}
	var engine xorm.EngineInterface
	var err error
	if dbConfigs.Master != "" {
		if len(dbConfigs.Slaves) > 0 {
			engine, err = NewXormEngineGroup(dbConfigs, dbPassword)
		} else {
			engine, err = NewXormEngineSingle(dbConfigs.Master, dbPassword)
		}
		if err != nil {
			return engine, err
		}
		return engine, nil
	} else {
		var dataSource string
		for _, slaveSource := range dbConfigs.Slaves {
			if slaveSource != "" {
				engine, err = NewXormEngineSingle(dataSource, dbPassword)
				if err == nil {
					return engine, nil
				}
			}
		}
	}
	return nil, fmt.Errorf("dbconfigs datasource invalid")
}

// NewXormEngineGroup 新建主从配置的xorm.EngineGroup
func NewXormEngineGroup(dbConfigs DBConfigs, dbPassword string) (*xorm.EngineGroup, error) {
	dataSources := make([]string, 0, len(dbConfigs.Slaves)+1)
	dataSources = append(dataSources, setDBPassword(dbConfigs.Master, dbPassword))
	for _, slaveSource := range dbConfigs.Slaves {
		dataSources = append(dataSources, setDBPassword(slaveSource, dbPassword))
	}
	groupEngine, err := xorm.NewEngineGroup("mysql", dataSources)
	if err != nil {
		return nil, err
	}
	return groupEngine, nil
}

// NewXormEngineSingle 新建xorm.Engine
func NewXormEngineSingle(dataSource string, dbPassword string) (*xorm.Engine, error) {
	engine, err := xorm.NewEngine("mysql", setDBPassword(dataSource, dbPassword))
	if err != nil {
		return nil, err
	}
	return engine, nil
}

func setDBPassword(dataSource string, password string) string {
	return fmt.Sprintf(dataSource, password)
}
