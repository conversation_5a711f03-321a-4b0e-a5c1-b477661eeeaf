#FROM busybox:latest
FROM alpine:latest

RUN apk add --update tzdata
ENV TZ Asia/Shanghai

WORKDIR /workspace/golang

RUN mkdir "/workspace/golang/logs"
RUN mkdir "/workspace/golang/data"
RUN mkdir "/workspace/golang/configs"

VOLUME ["/workspace/golang/logs"]

COPY ./gametesterAdmin ./
COPY ./configs/app.yaml ./configs/app.yaml
COPY ./configs/gametesterAdmin.yaml ./configs/gametesterAdmin.yaml

RUN ln -s /workspace/golang/gametesterAdmin /usr/local/bin/admin

EXPOSE 8001

CMD ["./gametesterAdmin"]