upstream datatester {
{% if container1 %}
        server 127.0.0.1:32201;
{% else %}
        #server 127.0.0.1:32201;
{% endif %}
{% if container2 %}
	    server 127.0.0.1:32202;
{% else %}
	    #server 127.0.0.1:32202;
{% endif %}
        keepalive 24;
}

server {
        listen       80;
        listen  443 ssl;
        ssl_certificate      /usr/local/nginx/conf/ssl/afafb.com-20250426/afafb.com_bundle.pem;
        ssl_certificate_key  /usr/local/nginx/conf/ssl/afafb.com-20250426/afafb.com.key;
        ssl_session_timeout  5m;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers  ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers  on;
        server_name  dt-test.afafb.com;
        #access_log  /data/logs/nginx/flume/access.log  flume;
        access_log  /data/logs/nginx/dt-test.afafb.com_access.log  main;
        error_log /data/logs/nginx/dt-test.afafb.com_error.log;
        location / {
                proxy_pass http://datatester;
                proxy_http_version 1.1;
                proxy_set_header Connection "";
		#proxy_set_header Host $http_host;
		#proxy_set_header X-Real-IP $http_x_forwarded_for;
		#proxy_set_header X-Forwarded-For $http_x_forwarded_for;
		#proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $http_x_forwarded_for;
        }
        location ~ /\.(svn|git|hg|ht|bzr|cvs)(/|$) {
             return 403;
        }
  }