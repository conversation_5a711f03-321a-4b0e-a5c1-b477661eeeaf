version: "3.9"

services:
{% for i in deployItems %}
  datatester-{{i + 1}}:
    image: "{{image.name}}:{{image.tag}}"
    container_name: datatester-{{i + 1}}
    restart: always
    stop_grace_period: 2m
    ports:
      - "{{ports.server + i}}:8001"
      - "{{ports.metrics + i}}:8002"
    networks:
      - datatester-net
    environment:
      DB_PASSWORD: "IqHyAGm7IhMG0m4f"
    volumes:
      - {{appDir}}/app{{ports.server + i}}/logs:/workspace/golang/logs
      - {{appDir}}/app{{ports.server + i}}/data:/workspace/golang/data
{% endfor %}

networks:
  datatester-net:
    ipam:
      driver: default
      config:
        - subnet: "**********/24"
          gateway: "**********"