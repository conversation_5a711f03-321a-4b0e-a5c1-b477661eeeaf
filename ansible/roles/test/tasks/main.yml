- name: create app dir
  file:
    path: "{{appDir}}"
    state: directory
- name: create deploy file dir
  file:
    path: "{{deployFileDir}}"
    state: directory
- name: create docker-compose.yaml file
  template:
    src: docker-compose.j2
    dest: "{{deployFileDir}}/docker-compose.yaml"
- name: nginx remove node container1
  vars:
    container1: false
    container2: true
  template:
    src: nginx-vhost.j2
    dest: "{{deployFileDir}}/nginx-vhost.conf"
- name: nginx reload
  service:
    name: nginx
    state: reloaded
- name: docker compose up -d datatester-1
  command: "docker compose -f {{deployFileDir}}/docker-compose.yaml up -d --force-recreate datatester-1"
- name: nginx add node datatester-1, remote node datatester-2
  vars:
    container1: true
    container2: false
  template:
    src: nginx-vhost.j2
    dest: "{{deployFileDir}}/nginx-vhost.conf"
- name: nginx reload
  service:
    name: nginx
    state: reloaded
- name: docker compose up -d datatester-2
  command: "docker compose -f {{deployFileDir}}/docker-compose.yaml up -d --force-recreate datatester-2"
- name: nginx add node datatester-2
  vars:
    container1: true
    container2: true
  template:
    src: nginx-vhost.j2
    dest: "{{deployFileDir}}/nginx-vhost.conf"
- name: nginx reload
  service:
    name: nginx
    state: reloaded
