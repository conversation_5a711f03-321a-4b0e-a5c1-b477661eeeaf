- name: deploy tester server
  roles: ["test"]
  hosts: devserver
  remote_user: ec2-user
  become: yes
  tasks:
    - name: deploy test
      tags: ["deploy", "test"]

      shell: |
        echo "Time: "`date "+%Y-%m-%d %H:%M:%S"`",Execute Command:docker compose -f {{testWorkDir}}/docker-compose111.yml up -d" >> {{testWorkDir}}/run.log
#        docker compose -f {{testWorkDir}}/docker-compose.yml up -d
    - name: delpoy release
      tags: [ "deploy", "release" ]
      shell: |
        echo "Time: "`date "+%Y-%m-%d %H:%M:%S"`", Execute Command:docker compose -f {{releaseWorkDir}}/docker-compose.yml up -d" >> {{releaseWorkDir}}/run.log
#        docker-compose -f {{releaseWorkDir}}/docker-compose.yml up -d
