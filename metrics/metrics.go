package metrics

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"
)

var (
	InitCount = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "init_count",
			Help:      "初始化结果",
		},
		[]string{"package", "bucket_id"},
	)
	RequestExecTime = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "datatester",
			Name:      "request_exec",
			Help:      "执行时间",
		},
		[]string{"uri"},
	)
	RequestExecCount = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "request_count",
			Help:      "请求次数",
		},
		[]string{"uri"},
	)
	RequestExecStaus = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "request_staus",
			Help:      "执行状态",
		},
		[]string{"uri", "status"})
	AbRequestCount = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "ab_request_count",
			Help:      "ab实验客户端请求次数",
		},
		[]string{"appid"},
	)
	AbHitCount = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "ab_hit_count",
			Help:      "ab实验客户端请求次数",
		},
		[]string{"appid", "experiment", "vid"},
	)
	AbActivateCount = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "ab_activate_count",
			Help:      "ab实验客激活次数",
		},
		[]string{"appid", "experiment", "vid"},
	)
	CsrCount = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "csr",
			Help:      "csr分流",
		},
		[]string{"key", "scheme"},
	)
	SudoProduct = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "sudo_product_csr",
			Help:      "Sudo研发策略csr分流",
		},
		[]string{"bundle_id", "pici", "shunt_type", "shunt_index", "plan"},
	)
	SudoSyh = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "datatester",
			Name:      "sudo_syh_csr",
			Help:      "Sudo商业化策略csr分流",
		},
		[]string{"bundle_id", "api_version", "allot_version", "shunt_index", "plan"},
	)

	GameTesterDBStatics = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "gametester",
			Name:      "db_statics",
			Help:      "游戏测试数据库统计",
		},
		[]string{"operation"},
	)

	GameTesterDBExecTime = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "gametester",
			Name:      "db_exec_time",
			Help:      "执行时长",
		},
		[]string{"operation"},
	)

	GameTesterCacheStatics = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "gametester",
			Name:      "cache_statics",
			Help:      "游戏测试缓存统计",
		},
		[]string{"operation"},
	)

	ABTestRequestExecTime = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "gametester",
			Name:      "abtest_exe_time",
			Help:      "abtest 执行时间",
		})
	ABTestRequestStatus = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "gametester",
			Name:      "abtest_exe_status",
			Help:      "abtest 执行时间",
		}, []string{"status"})

	DefendEarlyWarning = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "defend_early_warning",
			Name:      "defend_early_warning",
			Help:      "黑天鹅预警",
		},
		[]string{"bundle_id", "type"},
	)
)

var sugared *zap.SugaredLogger

func SetSugaredLogger(s *zap.SugaredLogger) {
	sugared = s
}

var metricsServer *http.Server

func Start(addr string) error {
	sugared.Infof("Start Metrics Server")
	metricsServer = &http.Server{
		Addr:    addr,
		Handler: promhttp.Handler(),
	}

	if err := metricsServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		sugared.Errorf("metricsServer.ListenAndServer Error: %v", err)
		return err
	}
	return nil
}

func Stop() error {
	timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), time.Second*2)
	defer timeoutCancel()
	if err := metricsServer.Shutdown(timeoutCtx); err != nil {
		sugared.Errorf("metricsServer.Shutdown Error: %v", err)
		return err
	}
	sugared.Infof("MetricsServer Shutdown")
	return nil
}

func GinMiddlewaresMetrics(c *gin.Context) {
	start := time.Now()
	c.Next()
	labels := prometheus.Labels{"uri": c.Request.URL.Path}
	timeLength := time.Now().Sub(start)
	RequestExecTime.With(labels).Set(float64(timeLength.Microseconds()))
	RequestExecCount.With(labels).Inc()
	statusLabels := prometheus.Labels{"uri": c.Request.URL.Path, "status": strconv.Itoa(c.Writer.Status())}
	RequestExecStaus.With(statusLabels).Inc()
}
