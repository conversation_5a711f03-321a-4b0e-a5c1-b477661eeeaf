version: "3.9"
name: "gametester-dev"

services:
  mysql:
    image: mysql
    container_name: mysql
    volumes:
      - mysql_data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: crazysaint
  redis:
    image: redis
    container_name: redis
  gametester:
    build: Dockerfile.gametester
    container_name: gametester
    restart: always
    stop_grace_period: 1m
    depends_on:
      - mysql
      - redis
    links:
      - mysql
      - redis
    ports:
      - "31701:8001"
      - "41701:8002"
    environment:
      DB_PASSWORD: "crazysaint"
    volumes:
      - configs:/workspace/golang/configs
      - logs:/workspace/golang/logs
      - data:/workspace/golang/data
  gametesterAdmin:
    build: Dockerfile.gametesterAdmin
    container_name: gametester-admin
    depends_on:
      - mysql
      - redis
    links:
      - mysql
      - redis
    ports:
      - "31702:8001"
    environment:
      PROD_DB_PASSWORD: "crazysaint"
      LOCAL_DB_PASSWORD: "crazysaint"
    volumes:
      - configs:/workspace/golang/configs
      - logs:/workspace/golang/logs
      - data:/workspace/golang/data
  gametesterMonitor:
    build: Dockerfile.gametesterMonitor
    container_name: gametester-monitor
    depends_on:
      - mysql
      - redis
    links:
      - mysql
      - redis
    environment:
      DISTRIBUTE_LOG_PROD_DB_PASSWORD: "crazysaint"
      DISTRIBUTE_LOG_LOCAL_DB_PASSWORD: "crazysaint"
    volumes:
      - configs:/workspace/golang/configs
      - logs:/workspace/golang/logs
      - data:/workspace/golang/data






