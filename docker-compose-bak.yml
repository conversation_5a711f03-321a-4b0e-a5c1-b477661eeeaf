name: "fldt-datatester"
services:
  fldt-datatester-1:
    image: 459528473147.dkr.ecr.us-east-2.amazonaws.com/datatester:3.1
    container_name: fldt-datatester-1
    restart: always
    stop_grace_period: 1m
    ports:
      - "32101:8001"
      - "42101:8002"
    networks:
      - fldt-datatester-net
    environment:
      DB_PASSWORD: "IqHyAGm7IhMG0m4f"
    volumes:
      - /data/golang/fldt-datatester/app32101/logs:/workspace/golang/logs
      - /data/golang/fldt-datatester/app32101/data:/workspace/golang/data
#  fldt-datatester-2:
#    image: 459528473147.dkr.ecr.us-east-2.amazonaws.com/datatester::3.1
#    container_name: fldt-datatester-2
#    restart: always
#    stop_grace_period: 1m
#    ports:
#      - "31702:8001"
#      - "41702:8002"
#    networks:
#      - fldt-datatester-net
#    environment:
#      DB_PASSWORD: "IqHyAGm7IhMG0m4f"
#    volumes:
#      - /data/golang/fldt-datatester/app32102/logs:/workspace/golang/logs
#      - /data/golang/fldt-datatester/app32102/data:/workspace/golang/data


networks:
  fldt-datatester-net:
    ipam:
      driver: default
      config:
        - subnet: "**********/24"
          gateway: "**********"