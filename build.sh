#!/usr/bin/env bash
# shellcheck disable=SC2034
accessToken="sJeoiBe4xU_QsuCrC-sv"

echo $TAG
workDir=$PWD
dependencesDir=$PWD/"dependences"
# shellcheck disable=SC2164
if [ ! -d $workDir ]; then
  mkdir $workDir
fi

if [ ! -d "$workDir/core" ]; then
  git clone https://oauth2:$<EMAIL>/hungrystudio/core.git $dependencesDir/core
else
  cd $dependencesDir/core
  git pull
fi
if [ ! -d "$workDir/awdb-golang" ]; then
  git clone https://oauth2:$<EMAIL>/hungrystudio/awdb-golang.git $dependencesDir/awdb-golang
else
  cd $dependencesDir/awdb-golang
  git pull
fi
if [ ! -d "$workDir/datatester-go-sdk" ];then
  git clone https://oauth2:$<EMAIL>/hungrystudio/datatester-go-sdk.git $dependencesDir/datatester-go-sdk
else
  cd $dependencesDir/datatester-go-sdk
  git pull
fi
cd $workDir
mv ./build.work ./go.work

docker build -t datatester-build:latest -f ./Dockerfile.build .

docker run --rm -v $PWD:/App datatester-build:latest make OS=linux ARCH=amd64 jenkins
cd dist/datatester/linux-amd64
docker build -t datatester:$TAG .
docker tag datatester:$TAG 459528473147.dkr.ecr.us-east-2.amazonaws.com/datatester:$TAG
docker push 459528473147.dkr.ecr.us-east-2.amazonaws.com/datatester:$TAG


