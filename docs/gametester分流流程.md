我需要画一个用户方案分流的流程图，根据一下逻辑

* 如果参与过分流返回用户分流信息
  * 检查用户桶，同存在
    * 检查用户方案是否存在
      * 用户方案存在
        * 存在检查用户所在桶的实验类型
          * 新增类型
            * 检查用户参数中是否有isUpgrade参数，如果为true，则进行新增方案重新分配，返回结果
            * 检查实验时长，基于用户installTime
              * now - installTime >= expDays: 转活跃大盘
              * now - installTime < expDays: 继续进行当前新增实验
          * 活跃类型
          * 执行释放配置            
      * 用户已方案不存在，执行释放配置
      * 检查14天判断
        * 满足14天，在活跃大盘内分流，
          * 大盘无可用分流，如果CloseReset=1，则在当前桶内分进行方案分配
        * 不满足14天，则保留当前方案  
  * 桶不存在
    * 检查14天，满足14天，转活跃大盘
    * 不满足，保留现有方案不变      

* 用户为参与过分流
  * 检查参数gameWayNum是否为空，为空，则进行新用户方案分配
  * 检查isClientPanel参数是否为true，进行兜底用户桶操作
  * 检查oldBucket是否大于0，检查是否为老用，是，进行老用户操作
  * 检查用户是否满足14天，满足则进行活跃大盘分配，不满足返回空，标识当前实验不变