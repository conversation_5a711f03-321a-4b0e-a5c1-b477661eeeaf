---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="http://csrbbg-test.afafb.com">csr-test: http://csrbbg-test.afafb.com</a>

# Authentication

# Default

## POST csr_config-test

POST /csr_config-test

方块csr分流测试接口，数据不加密，输出不加密

> Body 请求参数

```json
{
  "hit_way_0814": "9999",
  "install_time": 1723689239318,
  "thinkuid": "85a91f9d-b636-472f-b5b5-e0c0ebd733c6",
  "bundlid": "com.block.juggle",
  "ecpm": 0.0002967,
  "adnum": 0.21,
  "gamenum": 1.21
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» hit_way_0814|body|string| 是 |none|
|» install_time|body|integer| 是 |none|
|» thinkuid|body|string| 是 |none|
|» bundlid|body|string| 是 |none|
|» ecpm|body|number| 是 |单次广告收入均值（过去7天）float|
|» adnum|body|number| 是 |日均广告次数（过去7天）float|
|» gamenum|body|number| 是 |日均游戏局数（过去7天）float|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

# 数据模型

