---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="http://dt-test.afafb.com">测试环境: http://dt-test.afafb.com</a>

# Authentication

# Default

## POST configs

POST /configs

获取火山激活配置。
此接口只获取配置，激活放在后台进行，在获取配置后，会把相关数据放到后台的列表激活

接口域名：http://dt-test.afafb.com 此为测试域名

> Body 请求参数

```yaml
params: IAF1qT
  6n6FL24FkZDB6uoF8q5m0XlpAqbm126eieOZfsoeNelvmY5m8d4s1XdFKYPeie6eNels82dw8UDXLeKC6e6B62DJ3dbs1XDGfqOm0ZbRtYPeietYPe6B62DJ3dbmLqbpAXJRLXDf6n6eBe6B62DJ3d4FmXbJAYJRLXDf6n6e7e6B6ZD94ZdmLXDf6n6e7gHyfnoeNelJ7XbJG2OpkeKC6goeNem9WHOpkeKC6e6B6Zd9fqlm6Xd9mqAeiIAFkZd80ZbRv2O9AIpRK2b9meKC6e6B6ZdwVd4YmqOsV2b36nK
  fuoFkYD9VZpR=24pAZba6n6FbUdY12498qTFm25R8YJRV2O
  6uoF8YTX8IDGv2PeigH/BsWMNelJBqJYmqOsV2b36n6efuKqLgoFRuoFLYd9424FreKC6pfm5PPeNelR=XlpAqbm126eieK/=e6B6ZOpLY57mPD
  6n6FK2bfLZl71ZbNLUOpOYb7me6B6ZdwBplpAqbm126eieK
  LsA3Be6B6UD9lZPeielYmg=X8glJ6Y5a4YWJ8Z=g6uoFt2lJkYPeielf=g4M6uoF=IdsfYDv
  25JfYlRA2PeielJLYTF1UD 6uoFNZDGOXDJOYPeieOVCua882Ogk
  f36uoFVY5YbeKC6Yla=sb/AZDFtYHXtgDJKgAeNeO9V2Dpi2bGmeKC6
  dsVZpB1ab882lXCZDt6uoFfU5mLU4pVYoeiel9tsKslZH
  4uHeGgWeksWy3YPv8ZHqGuDFmsKYmYHJmZKZ=sPeNeO9V2DpfZDvBeKC6gHq7s=ybgKZ4sKMGsoeNels1XDGfqOt6n6FWH6eNelv1Y5pN2lJkYPeiemssuaf=g=YoeOfz

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» params|body|string| 否 |none|

> 返回示例

> 成功

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» ad_info|object|true|none||none|
|»»» ab_live|string|true|none||none|
|»»» abtest|string|true|none||none|
|»»» ad_duration_limit|string|true|none||none|
|»»» ad_platform|string|true|none||none|
|»»» adwaynum|string|true|none||none|
|»»» aqwaynum|string|true|none||none|
|»»» banner|object|true|none||none|
|»»»» adunit|string|true|none||none|
|»»»» bannerInterval|[oneOf]|true|none||none|

*oneOf*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»» *anonymous*|integer|false|none||none|

*xor*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»» *anonymous*|number|false|none||none|

*continued*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»» banner_events|string|true|none||none|
|»»»» keywords|[string]|true|none||none|
|»»» banner_events|string|true|none||none|
|»»» dynamic_config|object|true|none||none|
|»»»» dynamic_banner|object|true|none||none|
|»»»»» is_set_defalut_banner_keywords|string|true|none||none|
|»»»» dynamic_intersititial|object|true|none||none|
|»»»»» is_dont_set_installs_times_keywords|string|true|none||none|
|»»»»» is_load_bidder|string|true|none||none|
|»»»»» is_set_add_ad_load|string|true|none||none|
|»»»»» is_set_disable_auto_retry|string|true|none||none|
|»»»»» is_set_disable_sequential_cache|string|true|none||none|
|»»»»» is_set_load_bidder_sdk|string|true|none||none|
|»»»»» is_set_load_more_two_inter|string|true|none||none|
|»»»» dynamic_rv|object|true|none||none|
|»»»»» is_dont_set_installs_times_keywords|string|true|none||none|
|»»»»» is_set_add_ad_load|string|true|none||none|
|»»»»» is_set_disable_auto_retry|string|true|none||none|
|»»»»» is_set_disable_sequential_cache|string|true|none||none|
|»»»»» is_set_load_more_two_reward|string|true|none||none|
|»»» insert|object|true|none||none|
|»»»» adunit|string|true|none||none|
|»»»» keywords|[string]|true|none||none|
|»»» is_first|integer|true|none||none|
|»»» reward|object|true|none||none|
|»»»» adunit|string|true|none||none|
|»»»» keywords|[string]|true|none||none|
|»»» selective_ids|string|true|none||none|
|»»» selective_old_ids|string|true|none||none|
|»» vid|string|true|none||none|

# 数据模型

