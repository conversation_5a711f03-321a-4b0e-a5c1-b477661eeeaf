---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="https://gametester-admin.afafb.com">gametesterAdmin: https://gametester-admin.afafb.com</a>

# Authentication

# gametesteAdmin

# 返回数据结构
正确
```json
{
    "status": "ok"
}
```
错误
```json
{
    "error": "错误信息"
}
```

## POST admin/gametester/save

POST /admin/gametester/save

配置保存接口
数据放到body里面，post提交
查询参数：
bundle_id ： 包名
env: 环境 测试 testing ，灰度 gray

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |包名|
|env|query|string| 否 |环境：测试 testing， 灰度 gray|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET admin/gametester/push-gray

GET /admin/gametester/push-gray

配置灰度环境发布到正式环境

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |包名|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET admin/gametester/set-bucket-flow

GET /admin/gametester/set-bucket-flow

设置桶的流量，只在本地

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |包名|
|bucket|query|integer| 否 |none|
|flow_type|query|string| 否 |flow_type: hour 小时流量，total 总流量|
|count|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET lookup/exps-state

GET /lookup/exps-state

获取实验状态

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET lookup/user-info

GET /lookup/user-info

获取用户信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |none|
|uid|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET lookup//bucket-flow

GET /lookup/bucket-flow

查询桶流量

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |none|
|env|query|string| 否 |环境：testing 测试环境，prod 生产环境|
|bucket|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET /lookup/bucket-config

GET /lookup/bucket-config

查询配置

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundle_id|query|string| 否 |none|
|env|query|string| 否 |环境：testing 测试环境，gray 灰度环境，prod 生产环境|
|all|query|string| 否 |值不为空时，返回所配置包含方案配置|

> 返回示例

> 200 Response

```json
[
  {}
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

