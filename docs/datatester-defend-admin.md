---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="http://datatester-admin-test.afafb.com">datatester-admin: http://datatester-admin-test.afafb.com</a>

# Authentication

# admin

## POST defend/save

POST /defend/save

配置保存，必须要包含app_version=default的配置，这个是兜底配置。没有这个配置。降不进入配置
host域名：*********** datatester-admin-test.afafb.com

> Body 请求参数

```json
[
  {
    "bundle_id": "com.block.juggle.ios",
    "app_version": "1.1.1",
    "status": true,
    "data": {
      "a": "a1",
      "b": "b1"
    }
  },
  {
    "bundle_id": "com.block.juggle.ios",
    "app_version": "default",
    "status": true,
    "data": {
      "a": "a1",
      "b": "b1"
    }
  },
  {
    "bundle_id": "com.block.juggle.ios",
    "app_version": "1.1.2",
    "status": true,
    "data": {
      "a": "a1",
      "b": "b1"
    }
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

