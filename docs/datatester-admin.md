---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

# Authentication

# admin

## POST gamester/save

POST /gametester/save

> Body 请求参数

```json
[
  {
    "bucketId": 1,
    "bucketTitle": "新增97期桶",
    "bucketDesc": "新增97期桶",
    "maxFlowNum": 0,
    "flowWeightPercent": 50,
    "exprimentList": {
      "100001": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      },
      "100002": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      }
    }
  },
  {
    "bucketId": 2,
    "bucketTitle": "元素桶",
    "bucketDesc": "元素桶",
    "maxFlowNum": 0,
    "flowWeightPercent": 30,
    "exprimentList": {
      "200001": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      },
      "200002": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      }
    }
  },
  {
    "bucketId": 3,
    "bucketTitle": "多邻国桶",
    "bucketDesc": "新增97期桶",
    "maxFlowNum": 0,
    "flowWeightPercent": 10,
    "exprimentList": {
      "300001": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      },
      "300002": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      }
    }
  },
  {
    "bucketId": 4,
    "bucketTitle": "特殊实验桶",
    "bucketDesc": "特殊实验桶",
    "maxFlowNum": 10000,
    "flowWeightPercent": 20,
    "exprimentList": {
      "400001": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      },
      "400002": {
        "conditions": [],
        "features": [
          {
            "id": 102001,
            "score": 500,
            "color": 1,
            "ratio": 200
          }
        ],
        "app_verion": "1.0.0",
        "plan": 9701,
        "mark": 1
      }
    }
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|array[object]| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

# 数据模型

