---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="http://datatester-test.afafb.com">datatester-test: http://datatester-test.afafb.com</a>

# Authentication

# business

## GET business/get_config

GET /business/get_config

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST business/init_test

POST /business/init_test

测试接口，输入输出均不加密
当本地没有方案是，adways 字段不用提交

> Body 请求参数

```json
{
  "bundleId": "com.block.juggle",
  "thinkuid": "85a91f9d-b636-472f-b5b5-e0c043345tyhj",
  "api_version": "v58",
  "adways": {
    "s_adq_1001": "ADQC3_1011",
    "s_adq_1301": "ADQB2_1301",
    "s_opt_2101": "OPTA1_2101"
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» bundleId|body|string| 是 |包名|
|» thinkuid|body|string| 是 |访客ID，数数ID|
|» api_version|body|string| 是 |版本|
|» adways|body|object| 是 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST business/init

POST /business/init

正式接口，需要加密

> Body 请求参数

```
加密字符串

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

