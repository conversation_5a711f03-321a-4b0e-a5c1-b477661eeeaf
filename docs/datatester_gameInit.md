---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="http://bbggt-test.afafb.com">bbggt-test: http://bbggt-test.afafb.com</a>

# Authentication

# Default

## POST game_init

POST /game_init

> Body 请求参数

```json
{
  "uid": "85a91f9d-b636-472f-b5b5-e0c0ebd733c6",
  "installTime": 1724770757,
  "gameWayNum": "",
  "bundleId": "com.block.juggle"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» uid|body|string| 是 |用户访客ID|
|» installTime|body|integer| 是 |客户端安装时间|
|» bundleId|body|string| 是 |包名|
|» resVersion|body|string| 是 |资源版本号|
|» sdkVersion|body|string| 是 |sdk版本号|
|» gameWayNum|body|string| 否 |方案号|
|» isClientPanel|body|boolean| 否 |是否客户端兜底|
|» oldBucket|body|integer| 否 |老用户桶ID|
|» isUpgrade|body|boolean| 否 |是否是热更好请求|
|» csrParams|body|object| 否 |csr参数|
|»» ecpm|body|number| 否 |ecpm值 活跃用|
|»» adnum|body|number| 否 |广告展示次数 活跃用|
|»» gamenum|body|number| 否 |游戏局数 活跃用|
|»» aday|body|integer| 否 |活跃天数 活跃用|
|»» country|body|string| 否 |国家 新增用|
|»» start_time|body|number| 否 |启动市场 新增用|
|»» network|body|string| 否 |网络 新增用|

> 返回示例

```json
{
  "experimentDataMap": {
    "defaultLayer": {
      "gameWayNum": "135_1",
      "bucketId": 0,
      "experimentType": 0,
      "configData": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» experimentDataMap|object|true|none||none|
|»» defaultLayer|object|true|none||none|
|»»» gameWayNum|string|true|none||方案号|
|»»» bucketId|integer|true|none||桶ID|
|»»» experimentType|integer|true|none||桶实验类型|
|»»» configData|object|true|none||方案配置|

# 数据模型

