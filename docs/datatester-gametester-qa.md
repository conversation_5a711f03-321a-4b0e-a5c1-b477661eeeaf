---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.23"

---

# datatester

Base URLs:

* <a href="http://bbggt-test.afafb.com">bbggt-test: http://bbggt-test.afafb.com</a>

# Authentication

# Default

## POST gametester/addqa

POST /gametester/addqa

添加qa用户

> Body 请求参数1

```json
{
  "bundleid": "string",
  "uid": "string",
  "bucket": 0,
  "experiment": "string",
  "state": 0
}
```

> Body 请求参数2 适合用Uid来指定实验配置。客户端测试用。state true 添加qa用户，false 取消测试， state默认为false
```json
{
    "uid": "test_0.869760983403373222",
    "experiment":{"aa":"bb"},
    "state": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» bundleid|body|string| 是 |包名|
|» uid|body|string| 是 |访客ID|
|» bucket|body|integer| 是 |桶ID|
|» experiment|body|string| 是 |实验ID|
|» state|body|integer| 是 |1 开启 0 关闭|

> 返回示例

```json
{
  "code": 1,
  "message": "success"
}
```

```json
{
  "code": 0,
  "message": "err info"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

# 数据模型

