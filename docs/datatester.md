---
title: datatester
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# datatester

Base URLs:

* <a href="https://gametester-admin.afafb.com">gametesterAdmin: https://gametester-admin.afafb.com</a>

# Authentication

# gametesteAdmin/multilink

## POST admin/multilink/upload-links

POST /admin/multilink/upload-links/{bundleId}/{env}

只上传链路，通过链路参数和链路对应的方案数组的对象数据进行上传。

> Body 请求参数

```json
[
  {
    "links": {
      "customParams": {
        "param1": "value1"
      },
      "link": "0",
      "urls": [
        "http://linkurl"
      ],
      "version": "*******.1"
    },
    "schemes": [
      "方案1",
      "方案2"
    ]
  },
  {
    "links": {
      "customParams": {
        "param1": "value1"
      },
      "link": "1",
      "urls": [
        "http://linkurl"
      ],
      "version": "*******.1"
    },
    "schemes": [
      "方案3",
      "方案4"
    ]
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|
|body|body|array[object]| 否 |none|

> 返回示例

```json
{
  "code": 1,
  "message": "success",
  "data": {
    "link1": [
      "方案1",
      "方案2"
    ],
    "link2": [
      "方案3",
      "方案4"
    ]
  }
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||链路ID对应链路绑定方案列表的json对象|
|»» link1|[string]|true|none||none|
|»» link2|[string]|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## POST admin/multilink/upload-white

POST /admin/multilink/upload-white/{bundleId}/{env}

上传白名单

> Body 请求参数

```json
[
  "uid1",
  "uid2",
  "uid3"
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|
|body|body|array[string]| 否 |none|

> 返回示例

```json
{
  "code": 1,
  "message": "success"
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## POST admin/multilink/upload-params

POST /admin/multilink/upload-params/{bundleId}/{env}

上传全局参数

> Body 请求参数

```json
{
  "param1": "val1",
  "param2": "val2"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|
|body|body|object| 否 |none|
|» param1|body|string| 是 |none|
|» param2|body|string| 是 |none|

> 返回示例

```json
{
  "code": 1,
  "message": "success"
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## POST admin/multilink/switch-link

POST /admin/multilink/switch-link/{bundleId}/{env}

线路切换，切换白名单线路到线上线路

> Body 请求参数

```json
[
  {
    "link": "link1",
    "schemes": [
      "方案1",
      "方案2"
    ]
  },
  {
    "link": "link2",
    "schemes": [
      "方案3",
      "方案4"
    ]
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|
|body|body|array[object]| 否 |none|

> 返回示例

```json
{
  "code": 1,
  "message": "success",
  "data": {
    "link1": [
      "方案1",
      "方案2"
    ],
    "link2": [
      "方案3",
      "方案4"
    ]
  }
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||链路ID对应链路绑定方案列表的json对象|
|»» link1|[string]|true|none||none|
|»» link2|[string]|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## POST admin/multilink/delete-white

POST /admin/multilink/delete-white/{bundleId}/{env}

删除白名单

> Body 请求参数

```json
[
  "uid1",
  "uid2",
  "uid3"
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|
|body|body|array[string]| 否 |none|

> 返回示例

```json
{
  "code": 1,
  "message": "success"
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## GET admin/multilink/switch-params

GET /admin/multilink/switch-params/{bundleId}/{env}

全局参数切换上线

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST admin/multilink/down-link-type

POST /down-link-type/{bundleId}/{env}/{linkType}

线路下线，下线指定线路，和线路绑定的方案同时解绑

> Body 请求参数

```json
[
  "link1",
  "link2",
  "link3",
  "link4"
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |none|
|env|path|string| 是 |none|
|linkType|path|string| 是 |链路类型： white 白名单链路， release 正式链路|
|body|body|array[string]| 否 |none|

> 返回示例

```json
{
  "code": 1,
  "message": "success",
  "data": {
    "link1": [
      "方案1",
      "方案2"
    ],
    "link2": [
      "方案3",
      "方案4"
    ]
  }
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||链路ID对应链路绑定方案列表的json对象|
|»» link1|[string]|true|none||none|
|»» link2|[string]|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## GET admin/multilink/publish

GET /admin/multilink/publish/{bundleId}/{env}

发布当前链路数据，线路数据修改后，需要需要发布，数据才会生效

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|
|is_force|query|integer| 否 |是否强制发布：强制 1，不强制 0；强制发布时，即使没有更新也会发布|

> 返回示例

> 200 Response

```json
{
  "code": 1,
  "message": "success"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## GET admin/multilink/get

GET /admin/multilink/get/{bundleId}/{env}

获取当前链路配置所有数据

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|

> 返回示例

```json
{
  "whilte_list": [
    "uid1",
    "uid2"
  ],
  "global_params": {
    "white": {
      "name1": "val1"
    },
    "release": {
      "name1": "val1"
    }
  },
  "links_params": {
    "white": {
      "方案号1": {}
    },
    "release": {
      "方案号2": {}
    }
  },
  "latest_update": 1742054296,
  "latest_publish": 1742054296,
  "publish_version": "20250321.14.15"
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

*白名单列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» whilte_list|[string]|true|none||none|
|» global_params|object|true|none||全局参数|
|»» white|object|true|none||none|
|»»» name1|string|true|none||none|
|»» release|object|true|none||none|
|»»» name1|string|true|none||none|
|» links_params|object|true|none||链路配置|
|»» white|object|true|none||none|
|»»» 方案号1|object|true|none||none|
|»» release|object|true|none||none|
|»»» 方案号2|object|true|none||none|
|» latest_update|integer|true|none||最近更新时间|
|» latest_publish|integer|true|none||最近发布时间|
|» publish_version|string|true|none||当前正式发布版本|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

## GET admin/multilink/get-published

GET /admin/multilink/get-published/{bundleId}/{env}

获取当前链路配置所有数据

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bundleId|path|string| 是 |包名|
|env|path|string| 是 |环境|

> 返回示例

```json
{
  "whilte_list": [
    "uid1",
    "uid2"
  ],
  "global_params": {
    "white": {
      "name1": "val1"
    },
    "release": {
      "name1": "val1"
    }
  },
  "links_params": {
    "white": {
      "方案号1": {}
    },
    "release": {
      "方案号2": {}
    }
  },
  "latest_update": 1742054296,
  "latest_publish": 1742054296,
  "publish_version": "20250321.14.15"
}
```

```json
{
  "code": 0,
  "message": "错误信息"
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|Inline|

### 返回数据结构

状态码 **200**

*白名单列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» whilte_list|[string]|true|none||none|
|» global_params|object|true|none||全局参数|
|»» white|object|true|none||none|
|»»» name1|string|true|none||none|
|»» release|object|true|none||none|
|»»» name1|string|true|none||none|
|» links_params|object|true|none||链路配置|
|»» white|object|true|none||none|
|»»» 方案号1|object|true|none||none|
|»» release|object|true|none||none|
|»»» 方案号2|object|true|none||none|
|» latest_update|integer|true|none||最近更新时间|
|» latest_publish|integer|true|none||最近发布时间|
|» publish_version|string|true|none||当前正式发布版本|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|

# 数据模型

