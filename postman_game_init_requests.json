{"基础新用户请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-001", "installTime": 1710000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.6", "sdkVersion": "9.9.9", "gameWayNum": "", "isClientPanel": false, "oldBucket": 0, "isUpgrade": false, "country": "CN"}}, "老用户请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-002", "installTime": 1700000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.6", "sdkVersion": "9.9.9", "gameWayNum": "135_1", "isClientPanel": false, "oldBucket": 100, "isUpgrade": false, "country": "US"}}, "客户端兜底请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-003", "installTime": 1710000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.6", "sdkVersion": "9.9.9", "gameWayNum": "135_2", "isClientPanel": true, "oldBucket": 0, "isUpgrade": false, "country": "JP"}}, "热更新请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-004", "installTime": 1700000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.7", "sdkVersion": "9.9.10", "gameWayNum": "135_1", "isClientPanel": false, "oldBucket": 100, "isUpgrade": true, "country": "CN"}}, "带CSR参数请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-005", "installTime": 1710000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.6", "sdkVersion": "9.9.9", "gameWayNum": "", "isClientPanel": false, "oldBucket": 0, "isUpgrade": false, "country": "CN", "csrParams": {"ecpm": 0.00011, "adnum": 0.21, "gamenum": 2.2, "aday": 3, "start_time": 1710000000, "network": "wifi"}}}, "带自定义参数请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-006", "deviceId": "device-123456", "installTime": 1710000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.6", "sdkVersion": "9.9.9", "userType": "normal", "gameWayNum": "", "isClientPanel": false, "oldBucket": 0, "isUpgrade": false, "country": "CN", "customPlan": "plan_a", "customLine": "line_1", "oneWayReleaseTag": 12345, "layer_ids": [1, 2, 3]}}, "完整参数请求": {"method": "POST", "url": "http://bbggt-test.afafb.com/game_init", "headers": {"Content-Type": "application/json"}, "body": {"uid": "test-user-007", "deviceId": "device-789012", "installTime": 1710000000000, "bundleId": "com.block.juggle", "resVersion": "2.9.6", "sdkVersion": "9.9.9", "userType": "premium", "gameWayNum": "135_3", "isClientPanel": false, "oldBucket": 200, "isUpgrade": false, "country": "KR", "customPlan": "plan_b", "customLine": "line_2", "oneWayReleaseTag": 67890, "layer_ids": [4, 5, 6], "csrParams": {"ecpm": 0.0005, "adnum": 1.5, "gamenum": 10.0, "aday": 7, "country": "KR", "start_time": 1710000000, "network": "4g"}}}}