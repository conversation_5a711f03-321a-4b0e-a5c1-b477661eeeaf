APP_NAME=datatester
CMD_PATH=./cmd

OS=`go env GOOS`
ARCH=`go env GOARCH`

TAG=dev

DIST_DIR=./dist/${OS}-${ARCH}/${APP_NAME}

COMPILE_TARGET=${DIST_DIR}/${APP_NAME} ${CMD_PATH}

COMPILE=CGO_ENABLED=0 go build -tags ${APP_NAME} -o ${COMPILE_TARGET}
COMPILE_CROSS=CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -tags ${APP_NAME} -o ${COMPILE_TARGET}
COMPILE_JENKINS=CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -tags ${APP_NAME} -o ${COMPILE_TARGET}

build-local:
	if [ -d "${DIST_DIR}" ]; then rm -rf ${DIST_DIR}; fi
	mkdir -p ${DIST_DIR}/configs
	cp -r configs/* ${DIST_DIR}/configs/
	if [ "${APP_NAME}" = "admin" ]; then \
		cp sql/gametester.sql ${DIST_DIR}/configs/; \
	fi
	cp Dockerfile.${APP_NAME} ${DIST_DIR}/Dockerfile
	#cp docker-compose.yml ${DIST_DIR}/docker-compose.yml
	cp Makefile ${DIST_DIR}/Makefile
	go work use
	$(COMPILE)

build-admin:
	if [ -d "${DIST_DIR}" ]; then rm -rf ${DIST_DIR}; fi
	mkdir -p ${DIST_DIR}/configs
	cp -r configs/* ${DIST_DIR}/configs/
	cp Dockerfile.admin ${DIST_DIR}/Dockerfile.admin
	#cp docker-compose.yml ${DIST_DIR}/docker-compose.yml
	cp Makefile ${DIST_DIR}/Makefile
	$(COMPILE)

build-cross:
	date
	if [ -d "${DIST_DIR}" ]; then rm -rf ${DIST_DIR}; fi
	mkdir -p ${DIST_DIR}/configs
	cp -r configs/* ${DIST_DIR}/configs/
	cp Dockerfile ${DIST_DIR}/Dockerfile
	cp docker-compose.yml ${DIST_DIR}/docker-compose.yml
	cp Makefile ${DIST_DIR}/Makefile
	$(COMPILE_CROSS)

ecr:
	aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 459528473147.dkr.ecr.us-east-2.amazonaws.com
	docker build -t ${APP_NAME}:${TAG} .
	docker tag ${APP_NAME}:${TAG} 459528473147.dkr.ecr.us-east-2.amazonaws.com/${APP_NAME}:${TAG}
	docker push 459528473147.dkr.ecr.us-east-2.amazonaws.com/${APP_NAME}:${TAG}

jenkins:
	go mod download
	date
	if [ -d "${DIST_DIR}" ]; then rm -rf ${DIST_DIR}; fi
	mkdir -p ${DIST_DIR}/configs
	cp -r configs/* ${DIST_DIR}/configs/
	cp Dockerfile ${DIST_DIR}/Dockerfile
	cp docker-compose.yml ${DIST_DIR}/docker-compose.yml
	cp Makefile ${DIST_DIR}/Makefile
	$(COMPILE_JENKINS)