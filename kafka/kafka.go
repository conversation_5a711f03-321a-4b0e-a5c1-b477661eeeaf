package kafka

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"os"
	"sync"

	"github.com/IBM/sarama"
	"github.com/aws/aws-msk-iam-sasl-signer-go/signer"
	"go.uber.org/zap"
	"hungrystudio.com/datatester/common"
)

var sugared *zap.SugaredLogger

func SetSugaredLogger(s *zap.SugaredLogger) {
	sugared = s
}

type KafkaRemote struct {
	client   sarama.Client
	producer sarama.AsyncProducer
	addr     []string
	wg       sync.WaitGroup
}

type MSKAccessTokenProvider struct {
}

func (m *MSKAccessTokenProvider) Token() (*sarama.AccessToken, error) {
	token, _, err := signer.GenerateAuthToken(context.TODO(), os.Getenv("AWS_REGION"))
	return &sarama.AccessToken{Token: token}, err
}

func NewKafkaRemote(option common.KafkaOption) *KafkaRemote {
	cfg := sarama.NewConfig()
	cfg.Version = sarama.V3_5_1_0
	//cfg.Net.SASL.Enable = true
	if option.EnabledTLS {
		cfg.Net.TLS.Enable = true
		tlsCfg := tls.Config{}
		if option.CA != "" {
			certPool := x509.NewCertPool()
			sugared.Infof("CA: %s", option.CA)
			certPool.AppendCertsFromPEM([]byte(option.CA))
			tlsCfg.RootCAs = certPool
		}
		cfg.Net.TLS.Enable = true
		cfg.Net.TLS.Config = &tlsCfg
	}
	if option.EnabledIAM {
		cfg.Net.SASL.Mechanism = sarama.SASLTypeOAuth
		cfg.Net.SASL.TokenProvider = &MSKAccessTokenProvider{}
	}
	cfg.Producer.MaxMessageBytes = 1024 * 1024 * 100
	if option.EnabledCompress {
		sugared.Infof("Kafka Producer Compressed")
		cfg.Producer.Compression = sarama.CompressionLZ4
		cfg.Producer.CompressionLevel = sarama.CompressionLevelDefault
	}
	client, err := sarama.NewClient(option.Addr, cfg)
	if err != nil {
		panic(err)
	}
	producer, err := sarama.NewAsyncProducerFromClient(client)
	if err != nil {
		panic(err)
	}
	sugared.Infof("Start KafkaRemote")
	sugared.Infof("Kafka Addr : %v", option.Addr)
	kafkaRemote := &KafkaRemote{
		client:   client,
		producer: producer,
		addr:     option.Addr,
		wg:       sync.WaitGroup{},
	}
	return kafkaRemote
}

func (k *KafkaRemote) Push(topic, key string, body []byte) {
	if topic == "" || key == "" {
		sugared.Warnf("topic = %s, key = %s", topic, key)
	}
	k.wg.Add(1)
	defer k.wg.Done()
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Key:   sarama.StringEncoder(key),
		Value: sarama.ByteEncoder(body),
	}
	msgSize := msg.ByteSize(2)
	select {
	case k.producer.Input() <- msg:
	case err := <-k.producer.Errors():
		sugared.Errorf("Kafka Send Msg Size:%d, Error : %s", msgSize, err.Error())
	}
}

func (k *KafkaRemote) Close(ctx context.Context) error {
	<-ctx.Done()
	k.wg.Wait()
	err := k.producer.Close()
	if err != nil {
		sugared.Errorf("k.producer.Close Error: %v", err)
		return err
	}
	err = k.client.Close()
	if err != nil {
		sugared.Errorf("k.client.Close Error: %v", err)
		return err
	}
	sugared.Infof("KafkaRetmoe Closed")
	return nil
}

func (k *KafkaRemote) WriteEnable() bool {
	return true
}
