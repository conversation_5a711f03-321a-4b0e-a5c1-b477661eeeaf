FROM alpine:latest

RUN apk add --update tzdata
ENV TZ Asia/Shanghai

WORKDIR /workspace/golang

RUN mkdir "/workspace/golang/logs"
RUN mkdir "/workspace/golang/data"
RUN mkdir "/workspace/golang/configs"

VOLUME ["/workspace/golang/logs"]

COPY ./lookup ./
COPY ./configs/app.yaml ./configs/app.yaml
COPY ./configs/lookup.json ./configs/lookup.json
COPY ./configs/bundle.json ./configs/bundle.json
COPY ./configs/gametester.json ./configs/gametester.json

EXPOSE 8001
EXPOSE 8002

CMD ["./lookup"]