package dbconfigs

// FindLiuLiangConfigs 获取流量配置表的配置
func FindLiuLiangConfigs(tableId string) []LiuliangConfig {
	if tableName, has := GetLiulingTableName(TableTypeLiuLiang, tableId); has {
		llc := make([]LiuliangConfig, 0)
		err := engine.Where("`status`=?", 0).Table(tableName).Find(&llc)
		if err != nil {
			sugared.Errorf("FindLiuLiangConfig Error: %v", err)
			return nil
		}
		return llc
	}
	return nil
}

// FindLiuLiangNoVersionConfigs 后去版本为空的流量配置
func FindLiuLiangNoVersionConfigs(tableId string) []LiuliangConfig {
	if tableName, has := GetLiulingTableName(TableTypeLiuLiang, tableId); has {
		llc := make([]LiuliangConfig, 0)
		err := engine.Where("version=? and status=?", "", 0).Table(tableName).Find(&llc)
		if err != nil {
			sugared.Errorf("FindLiuLiangConfig Error: %v", err)
			return nil
		}
		return llc
	}
	return nil
}

// FindLiuLiangDidiConfigs 获取滴滴分流配置表数据
func FindLiuLiangDidiConfigs(tableId string) []LiuliangDidiConfig {
	if tableName, has := GetLiulingTableName(TableTypeDidi, tableId); has {
		didi := make([]LiuliangDidiConfig, 0)
		err := engine.Where("`status`=?", 0).Table(tableName).Find(&didi)
		if err != nil {
			sugared.Errorf("FindLiuLiangDidi Error: %v", err)
			return nil
		}
		return didi
	}
	return nil
}

// FindDjhConfigs 获取多聚合表的配置
func FindDjhConfigs(tableId string) []LiuliangDjhConfig {
	if tableName, has := GetLiulingTableName(TableTypeDjh, tableId); has {
		djh := make([]LiuliangDjhConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&djh)
		if err != nil {
			sugared.Errorf("FindLiuLiangDjhConfig Error: %v", err)
			return nil
		}
		return djh
	}
	return nil
}

// FindDoudiConfigs 获取兜底表配置
func FindDoudiConfigs(tableId string) []LiuliangDoudiConfig {
	if tableName, has := GetLiulingTableName(TableTypeDoudi, tableId); has {
		doudi := make([]LiuliangDoudiConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&doudi)
		if err != nil {
			sugared.Errorf("FindLiuLiangDoudiConfig Error: %v", err)
			return nil
		}
		return doudi
	}
	return nil
}

// FindGuoShenConfigs 获取过审表的配置
func FindGuoShenConfigs(tableId string, bundleId string) []LiuliangGuoshen {
	if tableName, has := GetLiulingTableName(TableTypeGuoShen, tableId); has {
		guoshen := make([]LiuliangGuoshen, 0)
		err := engine.Where("bundleid=? and status=?", bundleId, 0).Table(tableName).Find(&guoshen)
		if err != nil {
			sugared.Errorf("FindLiuLiangGuoShenConfig Error: %v", err)
			return nil
		}
		return guoshen
	}
	return nil
}

// FindNoNormalConfigs 获取低端机配置
func FindNoNormalConfigs(tableId string) []LiuliangNoNormalConfig {
	if tableName, has := GetLiulingTableName(TableTypeNoNormal, tableId); has {
		nonormal := make([]LiuliangNoNormalConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&nonormal)
		if err != nil {
			sugared.Errorf("FindNoNormalConfigs Error: %v", err)
			return nil
		}
		return nonormal
	}
	return nil
}

// FindQaConfigs 获取QA用户配置
func FindQaConfigs(tableId string) []LiuliangQaAdwaynum {
	if tableName, has := GetLiulingTableName(TableTypeQa, tableId); has {
		qa := make([]LiuliangQaAdwaynum, 0)
		err := engine.Table(tableName).Find(&qa)
		if err != nil {
			sugared.Errorf("FindQaConfigs Error: %v", err)
			return nil
		}
		return qa
	}
	return nil
}

// FindCsrQaConfigs 获取CsrQA用户配置
func FindCsrQaConfigs(tableId string) []LiuliangCsrqaAdwaynum {
	if tableName, has := GetLiulingTableName(TableTypeCsrQa, tableId); has {
		qa := make([]LiuliangCsrqaAdwaynum, 0)
		err := engine.Table(tableName).Find(&qa)
		if err != nil {
			sugared.Errorf("FindCsrQaConfigs Error: %v", err)
			return nil
		}
		return qa
	}
	return nil
}

// FindSpecialConfigs 获取Special表配置
func FindSpecialConfigs(tableId string) []LiuliangSpecialConfig {
	if tableName, has := GetLiulingTableName(TableTypeSpecial, tableId); has {
		special := make([]LiuliangSpecialConfig, 0)
		err := engine.Table(tableName).Find(&special)
		if err != nil {
			sugared.Errorf("FindSpecialConfigs Error: %v", err)
			return nil
		}
		return special
	}
	return nil
}

// FindTesterConfigs 获取tester表配置， 火山是否开启
func FindTesterConfigs(tableId string) []LiuliangTester {
	if tableName, has := GetLiulingTableName(TableTypeTester, tableId); has {
		tester := make([]LiuliangTester, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&tester)
		if err != nil {
			sugared.Errorf("FindTesterConfigs Error: %v", err)
			return nil
		}
		return tester
	}
	return nil
}

// FindSwitchConfigs 获取开关配置
func FindSwitchConfigs(tableId string) []LiuliangSwitchConfig {
	if tableName, has := GetLiulingTableName(TableTypeSwitch, tableId); has {
		switchs := make([]LiuliangSwitchConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&switchs)
		if err != nil {
			sugared.Errorf("FindSwitchConfigs Error: %v", err)
			return nil
		}
		return switchs
	}
	return nil
}

// FindTianChongConfigs 获取填充配置
func FindTianChongConfigs(tableId string) []LiuliangTianchongConfig {
	if tableName, has := GetLiulingTableName(TableTypeTianChong, tableId); has {
		tianChong := make([]LiuliangTianchongConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&tianChong)
		if err != nil {
			sugared.Errorf("FindTianChongConfigs Error: %v", err)
			return nil
		}
		return tianChong
	}
	return nil
}

// FindChunJingConfigs 获取纯净版配置
func FindChunJingConfigs(tableId string) []LiuliangChunjingConfig {
	if tableName, has := GetLiulingTableName(TableTypeChunJing, tableId); has {
		chunjing := make([]LiuliangChunjingConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&chunjing)
		if err != nil {
			sugared.Errorf("FindChunJingConfigs Error: %v", err)
			return nil
		}
		return chunjing
	}
	return nil
}

// FindAbTesterConfigs 获取火山AB测试配置
func FindAbTesterConfigs(tableId string) []LiuliangAbtestConfig {
	if tableName, has := GetLiulingTableName(TableTypeABTester, tableId); has {
		abtester := make([]LiuliangAbtestConfig, 0)
		err := engine.Table(tableName).Find(&abtester)
		if err != nil {
			sugared.Errorf("FindAbTesterConfigs Error: %v", err)
			return nil
		}
		return abtester
	}
	return nil
}

// FindVersionGatherConfigs 获取版本分流配置
func FindVersionGatherConfigs(tableId string) []LiuliangVersiongatherConfig {
	if tableName, has := GetLiulingTableName(TableTypeVersionGather, tableId); has {
		versiongather := make([]LiuliangVersiongatherConfig, 0)
		err := engine.Where("status=?", 0).Table(tableName).Find(&versiongather)
		if err != nil {
			sugared.Errorf("FindVersionGatherConfigs Error: %v", err)
			return nil
		}
		return versiongather
	}
	return nil
}
