package dbconfigs

import "time"

func FindCsrConfigByCsr() []CsrConfig {
	configs := make([]CsrConfig, 0)
	err := engine.Where("`is_open`=? and `end_ts`>=?", 1, time.Now().Unix()).Desc("created_at").Table("abtest_csr_config").Find(&configs)
	if err != nil {
		sugared.Errorf("read csr_config Error: %v", err)
		return nil
	}
	return configs
}

func FindCsrConfigTransition() []AbtestCsrConfigTransition {
	configs := make([]AbtestCsrConfigTransition, 0)
	err := engine.Where("`is_open`=? and `end_ts`>=?", 1, time.Now().Unix()).Desc("created_at").Table("abtest_csr_config_transition").Find(&configs)
	if err != nil {
		sugared.Errorf("read csr_config Error: %v", err)
		return nil
	}
	return configs
}
