package dbconfigs

import (
	"fmt"
	"time"
)

type LiuliangChunjingConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangConfig struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Adwaynum int    `xorm:"'adwaynum' not null comment('方案id') index INT"`
	Version  string `xorm:"'version' not null comment('api版本') index VARCHAR(64)"`
	Config   string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate     string `xorm:"'rate' not null comment('占比') DECIMAL(10,2)"`
	Type     int    `xorm:"'type' not null default 0 comment('新增活跃 0活跃 1新增') INT"`
	Status   int    `xorm:"'status' not null comment('0 生效 1 不生效') index INT"`
}

type LiuliangDidiConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Cnt        int    `xorm:"'cnt' not null comment('总数') INT"`
	Rate       int    `xorm:"'rate' not null comment('总比例') INT"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
	Scene      string `xorm:"'scene' not null default '' comment('scene') VARCHAR(32)"`
	GroupStr   string `xorm:"'group_str' not null default '' comment('group_str') VARCHAR(32)"`
	Platform   string `xorm:"'platform' not null default '' comment('platform') VARCHAR(32)"`
}

type LiuliangDjhConfig struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Adwaynum int    `xorm:"'adwaynum' not null comment('方案id') index INT"`
	Config   string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate     string `xorm:"'rate' not null comment('占比') DECIMAL(10,2)"`
	Mark     string `xorm:"'mark' not null default '' comment('多聚合标识') VARCHAR(32)"`
	Status   int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangDoudiConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangGuoshen struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Bundleid string `xorm:"'bundleid' not null default '' comment('包名') index(index_b_v) VARCHAR(200)"`
	Version  string `xorm:"'version' not null default '' comment('版本') index(index_b_v) VARCHAR(32)"`
	Type     int    `xorm:"'type' not null comment('类型') INT"`
	Status   int    `xorm:"'status' not null default 0 INT"`
}

type LiuliangNoNormalConfig struct {
	Id     uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Opt    string `xorm:"'opt' not null default '' comment('opt') index VARCHAR(32)"`
	Config string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangQaAdwaynum struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Thinkuid string `xorm:"'thinkuid' not null default '' index VARCHAR(64)"`
	Adwaynum string `xorm:"'adwaynum' not null default '' VARCHAR(64)"`
}

type LiuliangCsrqaAdwaynum struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT(10)"`
	Thinkuid string `xorm:"'thinkuid' not null default '' index VARCHAR(64)"`
	Version  string `xorm:"'version' not null default '' comment('版本号') VARCHAR(64)"`
	Adwaynum string `xorm:"'adwaynum' not null default '' VARCHAR(64)"`
}

type LiuliangSpecialConfig struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Thinkuid string `xorm:"'thinkuid' not null default '' index VARCHAR(64)"`
	Config   string `xorm:"'config' not null TEXT(65535)"`
}

type LiuliangSwitchConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangTester struct {
	Id       uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Bundleid string `xorm:"'bundleid' not null default '' comment('包名') index(index_b_v) VARCHAR(200)"`
	Version  string `xorm:"'version' not null default '' comment('版本') index(index_b_v) VARCHAR(32)"`
	Type     int    `xorm:"'type' not null comment('类型') INT"`
	Status   int    `xorm:"'status' not null default 0 INT"`
}

type LiuliangTianchongConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Tcnum      string `xorm:"'TCNum' not null default '' comment('TCNum') VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Rate       string `xorm:"'rate' not null comment('比例') DECIMAL(10,2)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangAbtestConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT(65535)"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT"`
}

type LiuliangVersiongatherConfig struct {
	Id         uint   `xorm:"'id' not null pk autoincr UNSIGNED INT(10)"`
	Apiversion string `xorm:"'apiversion' not null default '' comment('apiversion') index VARCHAR(32)"`
	Config     string `xorm:"'config' not null comment('配置') TEXT"`
	Status     int    `xorm:"'status' not null comment('0 生效 1 不生效') INT(11)"`
}

type ProductPiciSchemeConf struct {
	Id        uint      `xorm:"'id' not null pk autoincr UNSIGNED INT(11)"`
	BundleId  string    `xorm:"'bundle_id' not null default '' comment('应用包名') VARCHAR(128)"`
	Type      int       `xorm:"'type' not null default 0 comment('实验类型：0新增，1活跃') TINYINT(3)"`
	Pici      string    `xorm:"'pici' not null default '' comment('批次') VARCHAR(31)"`
	Scheme    string    `xorm:"'scheme' not null default '' comment('方案号') VARCHAR(2560)"`
	ConfInfo  string    `xorm:"'conf_info' not null comment('配置信息') TEXT"`
	Status    int       `xorm:"'status' not null default 0 comment('生效状态，默认0生效') TINYINT(3)"`
	CreatedAt time.Time `xorm:"'created_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
	UpdatedAt time.Time `xorm:"'updated_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
}

type CsrConfig struct {
	Id            uint      `xorm:"'id' not null pk autoincr comment('自增ID') UNSIGNED INT"`
	BundleId      string    `xorm:"'bundle_id' not null default '' comment('包体') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	ClientVersion string    `xorm:"'client_version' not null default '' comment('客户端版本') index(unqidx_bundle_cv_group) VARCHAR(64)"`
	GroupId       string    `xorm:"'group_id' not null default '' comment('分组标识') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	Ratio         int       `xorm:"'ratio' not null default 0 comment('流量生效百分比') INT"`
	Config        string    `xorm:"'config' not null comment('分组配置') TEXT(65535)"`
	StartTs       int       `xorm:"'start_ts' default 0 comment('生效时间(单位：秒)') INT"`
	EndTs         int       `xorm:"'end_ts' default 0 comment('失效时间(单位：秒)') INT"`
	IsOpen        int       `xorm:"'is_open' default 0 comment('是否开启') TINYINT(1)"`
	CreatedAt     time.Time `xorm:"'created_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
	UpdatedAt     time.Time `xorm:"'updated_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
}

type AbtestCsrConfigTransition struct {
	Id            uint      `xorm:"'id' not null pk autoincr comment('自增ID') UNSIGNED INT"`
	BundleId      string    `xorm:"'bundle_id' not null default '' comment('包体') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	ClientVersion string    `xorm:"'client_version' not null default '' comment('客户端版本') index(unqidx_bundle_cv_group) VARCHAR(64)"`
	MaxVersion    string    `xorm:"'max_version' not null comment('最大版本') VARCHAR(64)"`
	Type          string    `xorm:"'type' not null comment('类型 new 新用户， active 活跃用户') VARCHAR(16)"`
	GroupId       string    `xorm:"'group_id' not null default '' comment('分组标识') index(unqidx_bundle_cv_group) VARCHAR(128)"`
	Ratio         int       `xorm:"'ratio' not null default 0 comment('流量生效百分比') INT"`
	MarkRatio     string    `xorm:"'mark_ratio' not null comment('同标识生效比例') VARCHAR(16)"`
	Config        string    `xorm:"'config' not null comment('分组配置') MEDIUMTEXT(16777215)"`
	StartTs       int       `xorm:"'start_ts' default 0 comment('生效时间(单位：秒)') INT"`
	EndTs         int       `xorm:"'end_ts' default 0 comment('失效时间(单位：秒)') INT"`
	IsOpen        int       `xorm:"'is_open' default 0 comment('是否开启') TINYINT(1)"`
	CreatedAt     time.Time `xorm:"'created_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
	UpdatedAt     time.Time `xorm:"'updated_at' not null default CURRENT_TIMESTAMP TIMESTAMP"`
}

func (m *AbtestCsrConfigTransition) TableName() string {
	return "abtest_csr_config_transition"
}

const (
	TableTypeLiuLiang      = "liuliang"
	TableTypeDidi          = "didi"
	TableTypeDjh           = "djh"
	TableTypeDoudi         = "doudi"
	TableTypeGuoShen       = "guoshen"
	TableTypeNoNormal      = "nonormal"
	TableTypeQa            = "qa"
	TableTypeCsrQa         = "csrqa"
	TableTypeSpecial       = "special"
	TableTypeSwitch        = "switch"
	TableTypeTester        = "tester"
	TableTypeTianChong     = "tianchong"
	TableTypeChunJing      = "chunjing"
	TableTypeABTester      = "abtest"
	TableTypeVersionGather = "versiongather"
)

var tableNameTemplates = map[string]string{
	TableTypeLiuLiang:      "liuliang_%s_config",
	TableTypeDidi:          "liuliang_%s_didi_config",
	TableTypeDjh:           "liuliang_%s_djh_config",
	TableTypeDoudi:         "liuliang_%s_doudi_config",
	TableTypeGuoShen:       "liuliang_%s_guoshen",
	TableTypeNoNormal:      "liuliang_%s_no_normal_config",
	TableTypeQa:            "liuliang_%s_qa_adwaynum",
	TableTypeCsrQa:         "liuliang_%s_csrqa_adwaynum",
	TableTypeSpecial:       "liuliang_%s_special_config",
	TableTypeTester:        "liuliang_%s_tester",
	TableTypeSwitch:        "liuliang_%s_switch_config",
	TableTypeTianChong:     "liuliang_%s_tianchong_config",
	TableTypeChunJing:      "liuliang_%s_chunjing_config",
	TableTypeABTester:      "liuliang_%s_abtest_config",
	TableTypeVersionGather: "liuliang_%s_versiongather_config",
}

func GetLiulingTableName(tableType, tableId string) (string, bool) {
	tTemplate, ok := tableNameTemplates[tableType]
	if !ok {
		return "", false
	}
	tableName := fmt.Sprintf(tTemplate, tableId)
	if ok, err := engine.IsTableExist(tableName); err == nil && ok {
		return tableName, true
	}
	return tableName, false
}
