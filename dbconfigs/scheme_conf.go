package dbconfigs

// ProductPiciSchemeConfConfigs 获取研发方案配置
func ProductPiciSchemeConfConfigs(bundleId string) []ProductPiciSchemeConf {
	if ok, err := engine.IsTableExist("product_pici_scheme_conf"); err == nil && ok {
		FailureConfs := make([]ProductPiciSchemeConf, 0)
		err := engine.Where("bundle_id=? and status=?", bundleId, 0).Table("product_pici_scheme_conf").Find(&FailureConfs)
		if err != nil {
			sugared.Errorf("FindProductPiciSchemeConfConfigs Error: %v", err)
			return nil
		}
		return FailureConfs
	}

	return nil
}
