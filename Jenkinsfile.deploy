pipeline {
    agent any
    parameters {
        choice choices: ['test', 'release'], name: 'deployEnv'
        string defaultValue: 'dev3', name: 'tag'
    }
    stages {
        stage('Login Docker') {
            steps {
                withCredentials([aws(accessKeyVariable: 'AWS_ACCESS_KEY_ID', credentialsId: '459528473147', secreteyVariable: 'AWS_SECRET_ACCESS_KEY')]) {
                    sh "aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 459528473147.dkr.ecr.us-east-2.amazonaws.com"
                }
            }
        }
        stage('Deploy') {            
            steps {
                sh """
                set +x
                bash deploy.sh
                """
            }
        }
    }
}