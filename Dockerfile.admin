#FROM busybox:latest
FROM alpine:latest

WORKDIR /workspace/golang

RUN mkdir "/workspace/golang/logs"
RUN mkdir "/workspace/golang/data"
RUN mkdir "/workspace/golang/configs"

VOLUME ["/workspace/golang/logs"]

COPY ./admin ./
COPY ./configs/app.yaml ./configs/app.yaml
COPY ./configs/bundle.json ./configs/bundle.json
COPY ./configs/gametester.sql ./configs/gametester.sql

EXPOSE 8003

CMD ["./admin"]