package services

import (
	"hungrystudio.com/datatester/common"
	"log"
	"os"
	"xorm.io/xorm"
)

var mysqlServices map[string]xorm.EngineInterface

func InitMysqlServices(mysqlConfigs map[string]common.DBConfigs) error {
	mysqlServices = make(map[string]xorm.EngineInterface, len(mysqlConfigs))
	for k, v := range mysqlConfigs {
		dbPassword := os.Getenv(v.PasswdENVName)
		mysqlClient, err := common.NewXormEngineWithPassword(v, dbPassword)
		if err != nil {
			log.Printf("dbConfig: %v, password: %s, err: %v", v, dbPassword, err)
			return err
		}
		if err = mysqlClient.Ping(); err != nil {
			log.Printf("Mysql dbConfig: %v, Ping err: %v", v, err)
			return err
		}
		mysqlServices[k] = mysqlClient
	}
	return nil
}

func GetMysqlClient(name string) xorm.EngineInterface {
	if client, ok := mysqlServices[name]; ok {
		return client
	}
	return nil
}
