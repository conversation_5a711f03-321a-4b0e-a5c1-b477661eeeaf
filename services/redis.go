package services

import (
	"context"
	"hungrystudio.com/core/cache"
	"log"
	"time"
)

var redisServices map[string]cache.RedisClient

func InitRedisServices(cacheConfigs map[string]cache.RedisConfig) error {
	redisServices = make(map[string]cache.RedisClient, len(cacheConfigs))
	for name, config := range cacheConfigs {
		redisClient := cache.NewRedisClient(config)
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		if err := redisClient.Ping(ctx).Err(); err == nil {
			redisServices[name] = redisClient
			cancel()
		} else {
			cancel()
			log.Printf("Failed to connect to %v redis: %s", config, err)
			return err
		}
	}
	return nil
}

func GetRedisClient(name string) cache.RedisClient {
	if redisClient, ok := redisServices[name]; ok {
		return redisClient
	}
	return nil
}
