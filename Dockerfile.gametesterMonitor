#FROM busybox:latest
FROM alpine:latest

RUN apk add --update tzdata
ENV TZ Asia/Shanghai

WORKDIR /workspace/golang

RUN mkdir "/workspace/golang/logs"
RUN mkdir "/workspace/golang/data"
RUN mkdir "/workspace/golang/configs"

VOLUME ["/workspace/golang/logs"]

COPY ./gametesterMonitor ./
COPY ./configs/app.yaml ./configs/app.yaml
COPY ./configs/gametesterMonitor.yaml ./configs/gametesterMonitor.yaml

CMD ["./gametesterMonitor"]